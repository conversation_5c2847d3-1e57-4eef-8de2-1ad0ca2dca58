/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/triggers/workflows/agents/[agentId]/workflows/route";
exports.ids = ["app/api/triggers/workflows/agents/[agentId]/workflows/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&page=%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&page=%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_triggers_workflows_agents_agentId_workflows_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/triggers/workflows/agents/[agentId]/workflows/route.ts */ \"(rsc)/./src/app/api/triggers/workflows/agents/[agentId]/workflows/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/triggers/workflows/agents/[agentId]/workflows/route\",\n        pathname: \"/api/triggers/workflows/agents/[agentId]/workflows\",\n        filename: \"route\",\n        bundlePath: \"app/api/triggers/workflows/agents/[agentId]/workflows/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/api/triggers/workflows/agents/[agentId]/workflows/route.ts\",\n    nextConfigOutput,\n    userland: _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_triggers_workflows_agents_agentId_workflows_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/triggers/workflows/agents/[agentId]/workflows/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&page=%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00c3b743d074a69244eab1d948789d902598d3c17c\": () => (/* reexport safe */ _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__.createClient)\n/* harmony export */ });\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/lib/supabase/server.ts */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyJTJGbW50JTJGYyUyRlVzZXJzJTJGSWJyYWhpbSUyRkRvd25sb2FkcyUyRm9sYXJpJTJGc3VuYS1tYWluJTJGZnJvbnRlbmQlMkZzcmMlMkZsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiUyQyU1QiU3QiUyMmlkJTIyJTNBJTIyMDBjM2I3NDNkMDc0YTY5MjQ0ZWFiMWQ5NDg3ODlkOTAyNTk4ZDNjMTdjJTIyJTJDJTIyZXhwb3J0ZWROYW1lJTIyJTNBJTIyY3JlYXRlQ2xpZW50JTIyJTJDJTIyZmlsZW5hbWUlMjIlM0ElMjJsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiU3RCU1RCU1RCU1RCZfX2NsaWVudF9pbXBvcnRlZF9fPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ2lLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBjcmVhdGVDbGllbnQgYXMgXCIwMGMzYjc0M2QwNzRhNjkyNDRlYWIxZDk0ODc4OWQ5MDI1OThkM2MxN2NcIiB9IGZyb20gXCIvbW50L2MvVXNlcnMvSWJyYWhpbS9Eb3dubG9hZHMvb2xhcmkvc3VuYS1tYWluL2Zyb250ZW5kL3NyYy9saWIvc3VwYWJhc2Uvc2VydmVyLnRzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/triggers/workflows/agents/[agentId]/workflows/route.ts":
/*!****************************************************************************!*\
  !*** ./src/app/api/triggers/workflows/agents/[agentId]/workflows/route.ts ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n// Sample workflows for default agents and OLARI\nconst DEFAULT_WORKFLOWS = {\n    'olari-agent-001': [\n        {\n            workflow_id: 'olari-workflow-1',\n            agent_id: 'olari-agent-001',\n            name: 'Data Processing Workflow',\n            description: 'Automated data processing and analysis workflow',\n            trigger_type: 'manual',\n            trigger_config: {\n                trigger_phrase: '/process'\n            },\n            steps: [\n                {\n                    id: 'step-1',\n                    type: 'action',\n                    name: 'Fetch Data',\n                    description: 'Retrieve data from external sources',\n                    config: {\n                        tool_name: 'data_fetcher',\n                        parameters: {}\n                    },\n                    next_step: 'step-2'\n                },\n                {\n                    id: 'step-2',\n                    type: 'action',\n                    name: 'Process Data',\n                    description: 'Transform and analyze the data',\n                    config: {\n                        tool_name: 'data_processor',\n                        parameters: {}\n                    }\n                }\n            ],\n            is_active: true,\n            created_at: new Date('2024-01-01').toISOString(),\n            updated_at: new Date().toISOString(),\n            last_run: new Date(Date.now() - 86400000).toISOString(),\n            run_count: 15,\n            success_count: 14,\n            failure_count: 1\n        },\n        {\n            workflow_id: 'olari-workflow-2',\n            agent_id: 'olari-agent-001',\n            name: 'Report Generation',\n            description: 'Generate comprehensive reports',\n            trigger_type: 'manual',\n            trigger_config: {\n                trigger_phrase: '/report'\n            },\n            steps: [\n                {\n                    id: 'step-1',\n                    type: 'action',\n                    name: 'Collect Metrics',\n                    description: 'Gather relevant metrics and data points',\n                    config: {\n                        tool_name: 'metrics_collector',\n                        parameters: {}\n                    }\n                }\n            ],\n            is_active: true,\n            created_at: new Date('2024-01-15').toISOString(),\n            updated_at: new Date().toISOString(),\n            run_count: 8,\n            success_count: 8,\n            failure_count: 0\n        }\n    ],\n    'default-1': [\n        {\n            workflow_id: 'wf-general-1',\n            agent_id: 'default-1',\n            name: 'Daily Summary Report',\n            description: 'Generate a daily summary report of activities',\n            trigger_type: 'schedule',\n            trigger_config: {\n                cron: '0 9 * * *',\n                timezone: 'UTC'\n            },\n            steps: [\n                {\n                    id: 'step-1',\n                    type: 'action',\n                    name: 'Collect Data',\n                    description: 'Gather data from various sources',\n                    config: {\n                        sources: [\n                            'database',\n                            'api',\n                            'files'\n                        ],\n                        timeRange: '24h'\n                    },\n                    next_step: 'step-2'\n                },\n                {\n                    id: 'step-2',\n                    type: 'action',\n                    name: 'Generate Report',\n                    description: 'Create summary report from collected data',\n                    config: {\n                        template: 'daily_summary',\n                        format: 'markdown'\n                    },\n                    next_step: 'step-3'\n                },\n                {\n                    id: 'step-3',\n                    type: 'action',\n                    name: 'Send Report',\n                    description: 'Email report to stakeholders',\n                    config: {\n                        recipients: [\n                            '<EMAIL>'\n                        ],\n                        subject: 'Daily Summary Report'\n                    }\n                }\n            ],\n            is_active: true,\n            created_at: new Date('2024-01-01').toISOString(),\n            updated_at: new Date().toISOString(),\n            last_run: new Date(Date.now() - 86400000).toISOString(),\n            run_count: 30,\n            success_count: 28,\n            failure_count: 2\n        }\n    ],\n    'default-2': [\n        {\n            workflow_id: 'wf-code-1',\n            agent_id: 'default-2',\n            name: 'Code Review Pipeline',\n            description: 'Automated code review and testing workflow',\n            trigger_type: 'webhook',\n            trigger_config: {\n                endpoint: '/webhooks/github',\n                events: [\n                    'pull_request.opened',\n                    'pull_request.synchronize'\n                ],\n                secret: 'webhook_secret_key'\n            },\n            steps: [\n                {\n                    id: 'step-1',\n                    type: 'action',\n                    name: 'Lint Code',\n                    description: 'Run code linting checks',\n                    config: {\n                        linters: [\n                            'eslint',\n                            'prettier',\n                            'pylint'\n                        ],\n                        autoFix: false\n                    },\n                    next_step: 'step-2'\n                },\n                {\n                    id: 'step-2',\n                    type: 'parallel',\n                    name: 'Run Tests',\n                    description: 'Execute test suites in parallel',\n                    config: {\n                        branches: [\n                            {\n                                id: 'unit-tests',\n                                command: 'npm test'\n                            },\n                            {\n                                id: 'integration-tests',\n                                command: 'npm run test:integration'\n                            }\n                        ]\n                    },\n                    next_step: 'step-3'\n                },\n                {\n                    id: 'step-3',\n                    type: 'condition',\n                    name: 'Check Results',\n                    description: 'Evaluate test results',\n                    config: {},\n                    condition: {\n                        type: 'if',\n                        expression: 'tests.passed && coverage > 80',\n                        branches: [\n                            {\n                                condition: 'true',\n                                next_step: 'step-4-success'\n                            },\n                            {\n                                condition: 'false',\n                                next_step: 'step-4-failure'\n                            }\n                        ]\n                    }\n                },\n                {\n                    id: 'step-4-success',\n                    type: 'action',\n                    name: 'Approve PR',\n                    description: 'Mark pull request as approved',\n                    config: {\n                        status: 'approved',\n                        comment: 'All checks passed! ✅'\n                    }\n                },\n                {\n                    id: 'step-4-failure',\n                    type: 'action',\n                    name: 'Request Changes',\n                    description: 'Request changes on pull request',\n                    config: {\n                        status: 'changes_requested',\n                        comment: 'Please fix the failing tests'\n                    }\n                }\n            ],\n            is_active: true,\n            created_at: new Date('2024-01-15').toISOString(),\n            updated_at: new Date().toISOString(),\n            last_run: new Date(Date.now() - 3600000).toISOString(),\n            run_count: 150,\n            success_count: 142,\n            failure_count: 8\n        },\n        {\n            workflow_id: 'wf-code-2',\n            agent_id: 'default-2',\n            name: 'Deploy to Production',\n            description: 'Automated deployment workflow',\n            trigger_type: 'manual',\n            trigger_config: {\n                requiresApproval: true,\n                approvers: [\n                    '<EMAIL>'\n                ]\n            },\n            steps: [\n                {\n                    id: 'step-1',\n                    type: 'action',\n                    name: 'Build Application',\n                    description: 'Build production bundle',\n                    config: {\n                        command: 'npm run build:prod',\n                        environment: 'production'\n                    },\n                    next_step: 'step-2'\n                },\n                {\n                    id: 'step-2',\n                    type: 'action',\n                    name: 'Run E2E Tests',\n                    description: 'Execute end-to-end tests',\n                    config: {\n                        command: 'npm run test:e2e',\n                        timeout: 1800\n                    },\n                    next_step: 'step-3'\n                },\n                {\n                    id: 'step-3',\n                    type: 'action',\n                    name: 'Deploy',\n                    description: 'Deploy to production servers',\n                    config: {\n                        strategy: 'blue-green',\n                        rollbackOnFailure: true\n                    }\n                }\n            ],\n            is_active: true,\n            created_at: new Date('2024-02-01').toISOString(),\n            updated_at: new Date().toISOString(),\n            run_count: 45,\n            success_count: 43,\n            failure_count: 2\n        }\n    ]\n};\nasync function getBackendWorkflows(agentId, accessToken) {\n    const backendUrl = \"http://localhost:8003\";\n    if (!backendUrl) {\n        return null;\n    }\n    try {\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        if (accessToken) {\n            headers['Authorization'] = `Bearer ${accessToken}`;\n        }\n        const response = await fetch(`${backendUrl}/api/triggers/workflows/agents/${agentId}/workflows`, {\n            method: 'GET',\n            headers,\n            signal: AbortSignal.timeout(10000)\n        });\n        if (response.ok) {\n            return await response.json();\n        }\n        return null;\n    } catch (error) {\n        console.error('Error fetching workflows from backend:', error);\n        return null;\n    }\n}\nasync function getDatabaseWorkflows(agentId) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data, error } = await supabase.from('workflows').select('*').eq('agent_id', agentId).order('created_at', {\n            ascending: false\n        });\n        if (error || !data) {\n            return [];\n        }\n        // Map database fields to Workflow type\n        return data.map((workflow)=>({\n                workflow_id: workflow.workflow_id,\n                agent_id: workflow.agent_id,\n                name: workflow.name,\n                description: workflow.description,\n                trigger_type: workflow.trigger_type,\n                trigger_config: workflow.trigger_config || {},\n                steps: workflow.steps || [],\n                is_active: workflow.is_active,\n                created_at: workflow.created_at,\n                updated_at: workflow.updated_at,\n                last_run: workflow.last_run,\n                run_count: workflow.run_count || 0,\n                success_count: workflow.success_count || 0,\n                failure_count: workflow.failure_count || 0,\n                metadata: workflow.metadata\n            }));\n    } catch (error) {\n        console.error('Error fetching workflows from database:', error);\n        return [];\n    }\n}\nasync function GET(request, { params }) {\n    try {\n        const { agentId } = await params;\n        // Check if it's a default agent with predefined workflows\n        if (DEFAULT_WORKFLOWS[agentId]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(DEFAULT_WORKFLOWS[agentId], {\n                headers: {\n                    'Cache-Control': 'public, max-age=300'\n                }\n            });\n        }\n        // Get the current session if available\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        // Try to get workflows from backend first\n        if (session?.access_token) {\n            const backendWorkflows = await getBackendWorkflows(agentId, session.access_token);\n            if (backendWorkflows !== null) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(backendWorkflows, {\n                    headers: {\n                        'Cache-Control': 'private, max-age=60'\n                    }\n                });\n            }\n        }\n        // Try to get workflows from database\n        const databaseWorkflows = await getDatabaseWorkflows(agentId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(databaseWorkflows, {\n            headers: {\n                'Cache-Control': 'private, max-age=60'\n            }\n        });\n    } catch (error) {\n        console.error('Error in workflows endpoint:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Support OPTIONS for CORS preflight\nasync function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Methods': 'GET, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/triggers/workflows/agents/[agentId]/workflows/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00c3b743d074a69244eab1d948789d902598d3c17c\":\"createClient\"} */ \n\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://llliyihxaeakffunjjcb.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxsbGl5aWh4YWVha2ZmdW5qamNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4NDMxOTAsImV4cCI6MjA3MjQxOTE5MH0.Cu0nxk1MregjKOTlXNU4S0NXZlTiaRsPEmiK4isI04M\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    createClient\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(createClient, \"00c3b743d074a69244eab1d948789d902598d3c17c\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&page=%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftriggers%2Fworkflows%2Fagents%2F%5BagentId%5D%2Fworkflows%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();