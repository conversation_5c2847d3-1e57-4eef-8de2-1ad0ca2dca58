import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';

// Helper function to get OAuth scope for a toolkit
function getOAuthScope(toolkit: string): string {
  const scopes: Record<string, string> = {
    gmail: 'https://www.googleapis.com/auth/gmail.send https://www.googleapis.com/auth/gmail.readonly',
    google: 'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile',
    googlecalendar: 'https://www.googleapis.com/auth/calendar',
    googledrive: 'https://www.googleapis.com/auth/drive',
    slack: 'chat:write,channels:read,users:read',
    github: 'repo,user,workflow',
    twitter: 'tweet.read,tweet.write,users.read',
    linkedin: 'r_liteprofile,r_emailaddress',
    notion: 'read,write',
    discord: 'identify,email,guilds',
    dropbox: 'files.metadata.read,files.content.read',
    spotify: 'user-read-email,user-read-private',
    trello: 'read,write,account',
    zoom: 'user:read,meeting:write',
    salesforce: 'api,refresh_token',
    stripe: 'read_write',
    shopify: 'read_products,write_products'
  };
  
  // Default scope if toolkit not found
  return scopes[toolkit] || 'read,write';
}

// Mock profiles data
const MOCK_PROFILES = {
  success: true,
  profiles: [
    {
      id: 'profile-001',
      profile_id: 'profile-001',
      name: 'Default Profile',
      toolkit: 'gmail',
      status: 'connected',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-09-01T00:00:00Z',
      auth_type: 'oauth2',
      metadata: {
        email: '<EMAIL>',
        scopes: ['gmail.send', 'gmail.read']
      }
    },
    {
      id: 'profile-002',
      profile_id: 'profile-002',
      name: 'Slack Workspace',
      toolkit: 'slack',
      status: 'connected',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-09-01T00:00:00Z',
      auth_type: 'oauth2',
      metadata: {
        workspace: 'Example Workspace',
        team_id: 'T123456'
      }
    },
    {
      id: 'profile-003',
      profile_id: 'profile-003',
      name: 'GitHub Account',
      toolkit: 'github',
      status: 'connected',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-09-01T00:00:00Z',
      auth_type: 'api_key',
      metadata: {
        username: 'developer',
        org: 'example-org'
      }
    }
  ],
  total: 3
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get the current session for authentication (optional for this endpoint)
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    // If API_URL is not configured or empty, return mock data
    if (!API_URL || API_URL.trim() === '') {
      console.log('Using mock data for Composio profiles (API_URL not configured)');
      
      // Filter mock profiles based on query parameters
      let filteredProfiles = [...MOCK_PROFILES.profiles];
      
      const toolkit = searchParams.get('toolkit');
      if (toolkit) {
        filteredProfiles = filteredProfiles.filter(p => p.toolkit === toolkit);
      }
      
      const status = searchParams.get('status');
      if (status) {
        filteredProfiles = filteredProfiles.filter(p => p.status === status);
      }
      
      return NextResponse.json({
        success: true,
        profiles: filteredProfiles,
        total: filteredProfiles.length
      });
    }
    
    // Validate URL format
    try {
      new URL(`${API_URL}/api/composio/profiles`);
    } catch (urlError) {
      console.log('Invalid backend URL format, returning mock data');
      return NextResponse.json(MOCK_PROFILES);
    }
    
    // Build the backend URL with query parameters
    const backendUrl = new URL(`${API_URL}/api/composio/profiles`);
    searchParams.forEach((value, key) => {
      backendUrl.searchParams.append(key, value);
    });
    
    // Build headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    // Add authorization header if session exists
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
      console.log('GET request: Using authorization token from session');
    } else {
      console.warn('GET request: No session token available');
    }
    
    // Call the backend API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    try {
      const response = await fetch(backendUrl.toString(), {
        method: 'GET',
        headers,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        console.error(`Backend returned error: ${response.status} ${response.statusText}`);
        console.log('Backend failed, returning mock data as fallback');
        return NextResponse.json({
          ...MOCK_PROFILES,
          _fallback: true,
          _error: `Backend API error: ${response.status}`
        });
      }
      
      const data = await response.json();
      return NextResponse.json(data);
      
    } catch (fetchError: any) {
      clearTimeout(timeoutId);
      
      if (fetchError.name === 'AbortError') {
        console.log('Backend API timeout, returning mock data');
      } else {
        console.log('Backend API unavailable, returning mock data');
      }
      
      return NextResponse.json({
        ...MOCK_PROFILES,
        _fallback: true,
        _error: 'Backend unavailable, using mock data'
      });
    }
    
  } catch (error) {
    console.error('Error fetching profiles:', error);
    
    // Return mock data on any error
    return NextResponse.json({
      ...MOCK_PROFILES,
      _fallback: true,
      _error: 'Failed to fetch profiles, using mock data'
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Get the current session for authentication with proper error handling
    const supabase = await createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    // If there's an error getting the session, try to refresh it
    if (sessionError || !session) {
      console.error('Session error or no session:', sessionError?.message || 'No session found');
      
      // Try to get the user to trigger a token refresh if needed
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        console.error('Authentication failed:', userError?.message || 'No user found');
        return NextResponse.json(
          { 
            success: false,
            error: 'Authentication required. Please log in and try again.',
            details: 'Session expired or invalid'
          },
          { status: 401 }
        );
      }
      
      // Get session again after potential refresh
      const refreshedSession = await supabase.auth.getSession();
      if (!refreshedSession.data.session) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Session refresh failed. Please log in again.'
          },
          { status: 401 }
        );
      }
    }
    
    // Get the refreshed or existing session
    const currentSession = session || (await supabase.auth.getSession()).data.session;
    
    console.log('Session status:', {
      hasSession: !!currentSession,
      hasAccessToken: !!currentSession?.access_token,
      userId: currentSession?.user?.id,
      tokenExpiry: currentSession?.expires_at,
      tokenExpiresIn: currentSession?.expires_at ? 
        new Date(currentSession.expires_at * 1000).toISOString() : null
    });
    
    // Backend URL is required
    if (!API_URL || API_URL.trim() === '') {
      console.error('Backend URL not configured');
      return NextResponse.json(
        { 
          success: false,
          error: 'Backend service not configured'
        },
        { status: 503 }
      );
    }
    
    // Build headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    // Check if we have a valid session
    if (!currentSession || !currentSession.access_token) {
      console.error('POST request: No valid session after refresh attempt');
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required',
          details: 'Please log in to connect integrations'
        },
        { status: 401 }
      );
    }
    
    // Add authorization header with the access token
    headers['Authorization'] = `Bearer ${currentSession.access_token}`;
    
    // Also add the refresh token if available (backend might need it)
    if (currentSession.refresh_token) {
      headers['X-Refresh-Token'] = currentSession.refresh_token;
    }
    
    // Add user ID for backend validation
    if (currentSession.user?.id) {
      headers['X-User-Id'] = currentSession.user.id;
    }
    
    // Add Composio API key if available (backend might need it)
    const composioApiKey = process.env.COMPOSIO_API_KEY || process.env.NEXT_PUBLIC_COMPOSIO_API_KEY;
    if (composioApiKey) {
      headers['X-Composio-Api-Key'] = composioApiKey;
    }
    
    console.log('POST request: Sending to backend with auth headers:', {
      hasAuth: !!headers['Authorization'],
      hasRefresh: !!headers['X-Refresh-Token'],
      userId: headers['X-User-Id'] || 'not-set',
      hasComposioKey: !!headers['X-Composio-Api-Key']
    });
    
    // Log the request for debugging
    console.log('Sending to backend:', {
      url: `${API_URL}/api/composio/profiles`,
      headers: {
        ...headers,
        'Authorization': headers['Authorization'] ? '[REDACTED]' : 'missing'
      },
      body,
      session: session ? 'present' : 'missing',
      access_token: session?.access_token ? 'present' : 'missing'
    });
    
    // Call the backend API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout for OAuth flow
    
    try {
      const response = await fetch(`${API_URL}/api/composio/profiles`, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        console.error(`Backend returned error: ${response.status} ${response.statusText}`);
        
        // Try to parse error response
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          errorData = { error: await response.text() || 'Failed to create profile' };
        }
        
        console.error('Backend error details:', errorData);
        console.error('Request body was:', body);
        
        // For authentication errors, provide clear guidance
        if (response.status === 401) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Authentication failed. Please ensure you are logged in and try again.',
              details: 'The backend service requires valid authentication credentials.',
              ...errorData
            },
            { status: 401 }
          );
        }
        
        // For 400 errors, provide detailed information
        if (response.status === 400) {
          console.error('Backend returned 400 Bad Request:', {
            errorData,
            requestBody: body,
            url: `${API_URL}/api/composio/profiles`,
            hasSession: !!session,
            hasToken: !!session?.access_token
          });
          
          return NextResponse.json(
            { 
              success: false,
              error: errorData.detail || errorData.error || 'Invalid request parameters',
              details: `Backend validation error: ${JSON.stringify(errorData)}`,
              request_body: body,
              debug_info: {
                backend_url: API_URL,
                has_session: !!session,
                has_token: !!session?.access_token,
                error_response: errorData
              }
            },
            { status: 400 }
          );
        }
        
        // For other errors, return the actual error without fallback
        return NextResponse.json(
          { 
            success: false,
            ...errorData
          },
          { status: response.status }
        );
      }
      
      const data = await response.json();
      
      console.log('Backend response received:', {
        hasProfileId: !!data.profile_id,
        hasRedirectUrl: !!data.redirect_url,
        profileName: data.profile_name,
        toolkitSlug: data.toolkit_slug
      });
      
      // Ensure the response includes required fields for frontend compatibility
      const responseData = {
        success: true,
        profile_id: data.profile_id || data.id,
        redirect_url: data.redirect_url || data.auth_url || null,
        auth_url: data.redirect_url || data.auth_url || null,  // Include both for compatibility
        mcp_url: data.mcp_url || `http://localhost:3001/mcp/composio/${data.profile_id || data.id}`,
        profile_name: data.profile_name || body.profile_name,
        toolkit_slug: data.toolkit_slug || body.toolkit_slug,
        ...data
      };
      
      console.log('Returning response to frontend:', {
        profileId: responseData.profile_id,
        hasAuthUrl: !!responseData.redirect_url,
        mcpUrl: responseData.mcp_url
      });
      
      return NextResponse.json(responseData);
      
    } catch (fetchError: any) {
      clearTimeout(timeoutId);
      
      const errorMessage = fetchError.name === 'AbortError' 
        ? 'Request timeout - backend took too long to respond'
        : 'Backend service is unavailable';
      
      console.error(`Backend API error: ${errorMessage}`, fetchError);
      
      return NextResponse.json(
        { 
          success: false,
          error: errorMessage,
          details: 'Please ensure the backend service is running and accessible.'
        },
        { status: 503 }
      );
    }
    
  } catch (error) {
    console.error('Error creating profile:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'An unexpected error occurred while creating the profile',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { profile_id, ...updateData } = body;
    
    if (!profile_id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'profile_id is required' 
        },
        { status: 400 }
      );
    }
    
    // Get the current session for authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    // If API_URL is not configured or empty, return mock response
    if (!API_URL || API_URL.trim() === '') {
      console.log('Mock updating Composio profile (API_URL not configured)');
      
      return NextResponse.json({
        success: true,
        profile: {
          id: profile_id,
          profile_id: profile_id,
          ...updateData,
          updated_at: new Date().toISOString()
        }
      });
    }
    
    // Build headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    // Add authorization header if session exists
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }
    
    // Call the backend API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    try {
      const response = await fetch(`${API_URL}/api/composio/profiles/${profile_id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(updateData),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        console.error(`Backend returned error: ${response.status} ${response.statusText}`);
        
        // Return mock success for non-critical errors
        console.log('Backend failed, returning mock success');
        return NextResponse.json({
          success: true,
          profile: {
            id: profile_id,
            profile_id: profile_id,
            ...updateData,
            updated_at: new Date().toISOString()
          },
          _fallback: true
        });
      }
      
      const data = await response.json();
      return NextResponse.json(data);
      
    } catch (fetchError: any) {
      clearTimeout(timeoutId);
      
      console.log('Backend unavailable, returning mock response');
      return NextResponse.json({
        success: true,
        profile: {
          id: profile_id,
          profile_id: profile_id,
          ...updateData,
          updated_at: new Date().toISOString()
        },
        _fallback: true
      });
    }
    
  } catch (error) {
    console.error('Error updating profile:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update profile' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const profile_id = searchParams.get('profile_id');
    
    if (!profile_id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'profile_id is required' 
        },
        { status: 400 }
      );
    }
    
    // Get the current session for authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    // If API_URL is not configured or empty, return mock response
    if (!API_URL || API_URL.trim() === '') {
      console.log('Mock deleting Composio profile (API_URL not configured)');
      
      return NextResponse.json({
        success: true,
        message: 'Profile deleted successfully'
      });
    }
    
    // Build headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    // Add authorization header if session exists
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }
    
    // Call the backend API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    try {
      const response = await fetch(`${API_URL}/api/composio/profiles/${profile_id}`, {
        method: 'DELETE',
        headers,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        console.error(`Backend returned error: ${response.status} ${response.statusText}`);
        
        // Return mock success for non-critical errors
        console.log('Backend failed, returning mock success');
        return NextResponse.json({
          success: true,
          message: 'Profile deleted successfully',
          _fallback: true
        });
      }
      
      const data = await response.json();
      return NextResponse.json(data);
      
    } catch (fetchError: any) {
      clearTimeout(timeoutId);
      
      console.log('Backend unavailable, returning mock response');
      return NextResponse.json({
        success: true,
        message: 'Profile deleted successfully',
        _fallback: true
      });
    }
    
  } catch (error) {
    console.error('Error deleting profile:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to delete profile' 
      },
      { status: 500 }
    );
  }
}