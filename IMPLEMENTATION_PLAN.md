# Multi-Agent System Implementation Plan

## 🎯 Project Overview
Integrate AutoGen, DeepResearchAgent, and LlamaIndex into a unified collaborative agent system where agents converse dynamically to solve complex queries.

## 📅 Implementation Timeline

### Week 1: Foundation Setup
- [ ] Day 1-2: Environment setup and repository configuration
- [ ] Day 3-4: Path management and import verification
- [ ] Day 5-7: Core integration layer development

### Week 2: Agent Integration
- [ ] Day 8-9: AutoGen agent setup
- [ ] Day 10-11: DeepResearchAgent wrapping
- [ ] Day 12-14: LlamaIndex component integration

### Week 3: Orchestration & Testing
- [ ] Day 15-16: GroupChat configuration
- [ ] Day 17-18: Inter-agent communication testing
- [ ] Day 19-21: End-to-end conversation flows

### Week 4: Optimization & Deployment
- [ ] Day 22-23: Performance optimization
- [ ] Day 24-25: Production deployment setup
- [ ] Day 26-28: Documentation and handover

## 🏗️ Technical Architecture

### System Components Map

```python
# Component Hierarchy
olari/
├── backend/
│   ├── agents/           # Agent wrappers and integrations
│   │   ├── __init__.py
│   │   ├── autogen_wrapper.py
│   │   ├── deepresearch_wrapper.py
│   │   └── llamaindex_wrapper.py
│   ├── orchestration/    # Orchestration logic
│   │   ├── __init__.py
│   │   ├── group_chat_manager.py
│   │   └── conversation_controller.py
│   ├── config/          # Configuration files
│   │   ├── agent_config.yaml
│   │   ├── llm_config.yaml
│   │   └── tool_config.yaml
│   └── utils/           # Utility functions
│       ├── path_manager.py
│       ├── logger.py
│       └── metrics.py
├── autogen-main/        # AutoGen repository (DO NOT MODIFY)
├── DeepResearchAgent-main/ # Research agent repository (DO NOT MODIFY)
├── llama_index-main/    # LlamaIndex repository (DO NOT MODIFY)
└── tests/              # Integration tests
    ├── test_agents.py
    ├── test_orchestration.py
    └── test_e2e.py
```

## 💻 Detailed Implementation Steps

### Step 1: Path Configuration Module
```python
# backend/utils/path_manager.py
import sys
import os
from pathlib import Path

class PathManager:
    """Manages repository paths without modifying original code"""
    
    BASE_DIR = Path('/mnt/c/Users/<USER>/Downloads/olari')
    
    PATHS = {
        'autogen': BASE_DIR / 'autogen-main/python',
        'deepresearch': BASE_DIR / 'DeepResearchAgent-main',
        'llamaindex': BASE_DIR / 'llama_index-main'
    }
    
    @classmethod
    def setup_paths(cls):
        """Add all repository paths to Python path"""
        for name, path in cls.PATHS.items():
            if path.exists():
                sys.path.insert(0, str(path))
                print(f"✓ Added {name} to path: {path}")
            else:
                raise FileNotFoundError(f"Repository not found: {path}")
    
    @classmethod
    def get_path(cls, repo_name):
        """Get specific repository path"""
        return cls.PATHS.get(repo_name)
```

### Step 2: Agent Wrapper Implementation
```python
# backend/agents/autogen_wrapper.py
from autogen import ConversableAgent
from typing import List, Dict, Any

class AutoGenAgentAdapter:
    """Adapter for AutoGen agents - uses existing code"""
    
    def __init__(self):
        # Import from existing AutoGen
        from autogen import AssistantAgent, UserProxyAgent
        
        self.assistant = AssistantAgent(
            name="AutoGenAssistant",
            llm_config={"model": "gpt-4"},
            system_message="You are a helpful AI assistant."
        )
        
        self.user_proxy = UserProxyAgent(
            name="UserProxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=10
        )
    
    def get_agents(self) -> List[ConversableAgent]:
        """Return configured agents"""
        return [self.assistant, self.user_proxy]
```

### Step 3: DeepResearch Integration
```python
# backend/agents/deepresearch_wrapper.py
from autogen import ConversableAgent
import sys
sys.path.append('/mnt/c/Users/<USER>/Downloads/olari/DeepResearchAgent-main')

class DeepResearchAgentAdapter(ConversableAgent):
    """Wraps existing DeepResearchAgent for AutoGen compatibility"""
    
    def __init__(self, **kwargs):
        super().__init__(name="DeepResearcher", **kwargs)
        
        # Import existing agents
        from src.agent.deep_researcher_agent import DeepResearcherAgent
        from src.agent.planning_agent import PlanningAgent
        
        self.researcher = DeepResearcherAgent()
        self.planner = PlanningAgent()
    
    def generate_reply(self, messages, sender, config):
        """Use existing research functionality"""
        query = messages[-1]['content'] if messages else ""
        
        # Use existing planning
        plan = self.planner.create_plan(query)
        
        # Use existing research
        results = self.researcher.research(plan)
        
        return results
```

### Step 4: LlamaIndex Integration
```python
# backend/agents/llamaindex_wrapper.py
from autogen import ConversableAgent
import sys
sys.path.append('/mnt/c/Users/<USER>/Downloads/olari/llama_index-main')

class LlamaIndexAgentAdapter(ConversableAgent):
    """Wraps existing LlamaIndex for AutoGen compatibility"""
    
    def __init__(self, **kwargs):
        super().__init__(name="LlamaIndexRAG", **kwargs)
        
        # Import existing components
        from llama_index.core import VectorStoreIndex, SimpleDirectoryReader
        from llama_index.core.query_engine import SubQuestionQueryEngine
        
        # Use existing LlamaIndex setup
        documents = SimpleDirectoryReader('data').load_data()
        self.index = VectorStoreIndex.from_documents(documents)
        self.query_engine = self.index.as_query_engine()
    
    def generate_reply(self, messages, sender, config):
        """Use existing query functionality"""
        query = messages[-1]['content'] if messages else ""
        
        # Use existing query engine
        response = self.query_engine.query(query)
        
        return str(response)
```

### Step 5: Orchestration Setup
```python
# backend/orchestration/group_chat_manager.py
from autogen import GroupChat, GroupChatManager
from typing import List

class MultiAgentOrchestrator:
    """Orchestrates multi-agent conversations"""
    
    def __init__(self):
        # Import our adapters
        from backend.agents.autogen_wrapper import AutoGenAgentAdapter
        from backend.agents.deepresearch_wrapper import DeepResearchAgentAdapter
        from backend.agents.llamaindex_wrapper import LlamaIndexAgentAdapter
        
        # Create agent instances
        autogen_adapter = AutoGenAgentAdapter()
        self.agents = autogen_adapter.get_agents()
        
        # Add wrapped agents
        self.agents.append(DeepResearchAgentAdapter())
        self.agents.append(LlamaIndexAgentAdapter())
        
        # Configure group chat
        self.group_chat = GroupChat(
            agents=self.agents,
            messages=[],
            max_round=20,
            speaker_selection_method="auto",  # LLM selects speaker
            allow_repeat_speaker=True
        )
        
        # Create manager
        self.manager = GroupChatManager(
            groupchat=self.group_chat,
            llm_config={"model": "gpt-4", "temperature": 0.7}
        )
    
    def process_query(self, query: str) -> str:
        """Process user query through agent conversation"""
        # Initiate conversation
        self.agents[0].initiate_chat(
            self.manager,
            message=query
        )
        
        # Return consolidated response
        return self.group_chat.messages[-1]['content']
```

## 🔧 Configuration Files

### Agent Configuration (agent_config.yaml)
```yaml
agents:
  autogen:
    enabled: true
    models:
      - gpt-4
      - gpt-3.5-turbo
    max_tokens: 2000
    temperature: 0.7
    
  deepresearch:
    enabled: true
    search_depth: 3
    max_results: 10
    timeout: 30
    
  llamaindex:
    enabled: true
    chunk_size: 512
    overlap: 50
    embedding_model: "text-embedding-ada-002"
    
orchestration:
  max_rounds: 20
  speaker_selection: "auto"
  conversation_memory: 50
  parallel_execution: false
```

### LLM Configuration (llm_config.yaml)
```yaml
openai:
  api_key: ${OPENAI_API_KEY}
  models:
    primary: gpt-4
    fallback: gpt-3.5-turbo
  retry_attempts: 3
  timeout: 60

anthropic:
  api_key: ${ANTHROPIC_API_KEY}
  model: claude-3-opus
  max_tokens: 4000
```

## 🧪 Testing Strategy

### Phase 1: Unit Tests
```python
# tests/test_agents.py
import pytest
from backend.agents.autogen_wrapper import AutoGenAgentAdapter

def test_autogen_adapter_initialization():
    """Test AutoGen adapter can be initialized"""
    adapter = AutoGenAgentAdapter()
    agents = adapter.get_agents()
    assert len(agents) == 2
    assert agents[0].name == "AutoGenAssistant"

def test_deepresearch_wrapper():
    """Test DeepResearch wrapper functionality"""
    from backend.agents.deepresearch_wrapper import DeepResearchAgentAdapter
    agent = DeepResearchAgentAdapter()
    response = agent.generate_reply(
        [{"content": "Test query"}], 
        None, 
        None
    )
    assert response is not None
```

### Phase 2: Integration Tests
```python
# tests/test_orchestration.py
def test_multi_agent_conversation():
    """Test agents can have a conversation"""
    orchestrator = MultiAgentOrchestrator()
    response = orchestrator.process_query(
        "What are the latest AI trends?"
    )
    
    # Verify multiple agents participated
    messages = orchestrator.group_chat.messages
    speakers = set(msg['name'] for msg in messages)
    assert len(speakers) >= 2
```

### Phase 3: End-to-End Tests
```python
# tests/test_e2e.py
def test_complex_query_processing():
    """Test complex query requiring multiple agents"""
    query = """
    Analyze our internal sales data for Q3 2024,
    research competitor pricing strategies,
    and recommend optimal pricing for Q4.
    """
    
    orchestrator = MultiAgentOrchestrator()
    response = orchestrator.process_query(query)
    
    # Verify comprehensive response
    assert "internal data" in response.lower()
    assert "competitor" in response.lower()
    assert "recommendation" in response.lower()
```

## 🚀 Deployment Steps

### 1. Development Environment
```bash
# Clone repositories (if not already done)
cd /mnt/c/Users/<USER>/Downloads/olari
git clone [autogen-repo] autogen-main
git clone [deepresearch-repo] DeepResearchAgent-main
git clone [llamaindex-repo] llama_index-main

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys

# Run tests
pytest tests/
```

### 2. Docker Deployment
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Copy repositories (read-only)
COPY --chown=app:app autogen-main /app/autogen-main
COPY --chown=app:app DeepResearchAgent-main /app/DeepResearchAgent-main
COPY --chown=app:app llama_index-main /app/llama_index-main

# Copy our integration code
COPY --chown=app:app backend /app/backend
COPY --chown=app:app requirements.txt /app/

# Install dependencies
RUN pip install -r requirements.txt

# Set Python path
ENV PYTHONPATH="/app:/app/autogen-main/python:/app/DeepResearchAgent-main:/app/llama_index-main"

CMD ["python", "backend/main.py"]
```

### 3. Production Deployment
```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: multi-agent-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: multi-agent
  template:
    metadata:
      labels:
        app: multi-agent
    spec:
      containers:
      - name: orchestrator
        image: multi-agent-system:latest
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: openai
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
```

## 📊 Success Metrics

### Performance KPIs
- **Response Time**: < 10 seconds average
- **Token Efficiency**: < 4000 tokens per conversation
- **Success Rate**: > 95% queries completed
- **Agent Utilization**: Balanced across all agents

### Quality Metrics
- **Answer Accuracy**: > 90% verified correct
- **Source Attribution**: 100% responses cite sources
- **Self-Correction Rate**: > 80% errors caught and fixed
- **User Satisfaction**: > 4.5/5 rating

## 🛡️ Risk Mitigation

### Technical Risks
1. **Repository Updates**: Lock versions, test updates in staging
2. **API Rate Limits**: Implement caching and rate limiting
3. **Memory Issues**: Conversation pruning after N messages
4. **Integration Conflicts**: Isolate dependencies per agent

### Operational Risks
1. **Agent Failures**: Fallback agents for each role
2. **LLM Outages**: Multiple LLM provider support
3. **Data Loss**: Regular backups of conversation history
4. **Security Breaches**: API key rotation, audit logging

## 📚 Documentation Deliverables

1. **Integration Guide** ✅ (INTEGRATION_DOCUMENTATION.md)
2. **Implementation Plan** ✅ (This document)
3. **API Documentation** (To be created)
4. **Troubleshooting Guide** (To be created)
5. **Performance Tuning Guide** (To be created)

## ✅ Checklist for Production Readiness

- [ ] All repositories cloned and accessible
- [ ] Path configuration verified
- [ ] All agent wrappers implemented
- [ ] GroupChat orchestration working
- [ ] Unit tests passing (>80% coverage)
- [ ] Integration tests passing
- [ ] E2E tests passing
- [ ] Performance benchmarks met
- [ ] Security audit completed
- [ ] Documentation complete
- [ ] Monitoring configured
- [ ] Deployment scripts ready
- [ ] Rollback plan documented
- [ ] Team training completed

---

*Last Updated: January 2025*
*Version: 1.0.0*