"""
Path Manager for Multi-Agent System Integration
Manages repository paths without modifying original code
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class PathManager:
    """Manages repository paths without modifying original code"""
    
    BASE_DIR = Path('/mnt/c/Users/<USER>/Downloads/olari')
    
    PATHS = {
        'autogen': BASE_DIR / 'autogen-main/python',
        'deepresearch': BASE_DIR / 'DeepResearchAgent-main',
        'llamaindex': BASE_DIR / 'llama_index-main',
        'backend': BASE_DIR / 'backend'
    }
    
    # Python packages from each repository
    PACKAGE_PATHS = {
        'autogen': [
            'packages/autogen-core/src',
            'packages/autogen-agentchat/src',
            'packages/autogen-ext/src'
        ],
        'deepresearch': [
            'src',
            '.'
        ],
        'llamaindex': [
            'llama-index-core/llama_index',
            'llama-index-legacy/llama_index',
            '.'
        ]
    }
    
    @classmethod
    def setup_paths(cls, verbose: bool = True) -> bool:
        """
        Add all repository paths to Python path
        
        Args:
            verbose: Whether to print status messages
            
        Returns:
            bool: True if all paths were added successfully
        """
        success = True
        
        for name, path in cls.PATHS.items():
            if path.exists():
                if str(path) not in sys.path:
                    sys.path.insert(0, str(path))
                    if verbose:
                        logger.info(f"✓ Added {name} to path: {path}")
                else:
                    if verbose:
                        logger.info(f"✓ {name} already in path: {path}")
            else:
                logger.error(f"✗ Repository not found: {path}")
                success = False
        
        # Add specific package paths
        for repo_name, repo_path in cls.PATHS.items():
            if repo_name in cls.PACKAGE_PATHS:
                for package_path in cls.PACKAGE_PATHS[repo_name]:
                    full_path = repo_path / package_path
                    if full_path.exists():
                        if str(full_path) not in sys.path:
                            sys.path.insert(0, str(full_path))
                            if verbose:
                                logger.info(f"✓ Added {repo_name}/{package_path} to path")
        
        return success
    
    @classmethod
    def get_path(cls, repo_name: str) -> Optional[Path]:
        """Get specific repository path"""
        return cls.PATHS.get(repo_name)
    
    @classmethod
    def validate_repositories(cls) -> Dict[str, bool]:
        """
        Validate that all repositories exist and have expected structure
        
        Returns:
            Dict mapping repository names to validation status
        """
        results = {}
        
        # Expected files/directories for each repository
        expected_structure = {
            'autogen': [
                'packages/autogen-core',
                'packages/autogen-agentchat',
                'setup.py'
            ],
            'deepresearch': [
                'src/agent',
                'src/agent/deep_researcher_agent.py',
                'requirements.txt'
            ],
            'llamaindex': [
                'llama-index-core',
                'llama-index-legacy',
                'setup.py'
            ]
        }
        
        for repo_name, expected_items in expected_structure.items():
            repo_path = cls.PATHS.get(repo_name)
            if not repo_path or not repo_path.exists():
                results[repo_name] = False
                logger.error(f"Repository {repo_name} not found at {repo_path}")
                continue
            
            repo_valid = True
            for item in expected_items:
                item_path = repo_path / item
                if not item_path.exists():
                    logger.warning(f"Expected {item} not found in {repo_name}")
                    repo_valid = False
            
            results[repo_name] = repo_valid
            if repo_valid:
                logger.info(f"✓ Repository {repo_name} structure validated")
            else:
                logger.warning(f"⚠ Repository {repo_name} structure incomplete")
        
        return results
    
    @classmethod
    def get_import_statements(cls) -> Dict[str, List[str]]:
        """
        Get correct import statements for each repository
        
        Returns:
            Dict with repository names and their import statements
        """
        return {
            'autogen': [
                'from autogen import ConversableAgent, GroupChat, GroupChatManager',
                'from autogen import AssistantAgent, UserProxyAgent',
                'from autogen import register_function'
            ],
            'deepresearch': [
                'from src.agent.deep_researcher_agent import DeepResearcherAgent',
                'from src.agent.planning_agent import PlanningAgent',
                'from src.agent.deep_analyzer_agent import DeepAnalyzerAgent',
                'from src.agent.browser_use_agent import BrowserUseAgent'
            ],
            'llamaindex': [
                'from llama_index.core import VectorStoreIndex, SimpleDirectoryReader',
                'from llama_index.core.query_engine import SubQuestionQueryEngine',
                'from llama_index.core.memory import ChatMemoryBuffer',
                'from llama_index.core.tools import QueryEngineTool'
            ]
        }
    
    @classmethod
    def test_imports(cls) -> Dict[str, bool]:
        """
        Test that imports work correctly after path setup
        
        Returns:
            Dict mapping repository names to import success status
        """
        cls.setup_paths(verbose=False)
        results = {}
        
        # Test AutoGen imports
        try:
            from autogen import ConversableAgent
            results['autogen'] = True
            logger.info("✓ AutoGen imports successful")
        except ImportError as e:
            results['autogen'] = False
            logger.error(f"✗ AutoGen import failed: {e}")
        
        # Test DeepResearch imports
        try:
            from src.agent.deep_researcher_agent import DeepResearcherAgent
            results['deepresearch'] = True
            logger.info("✓ DeepResearch imports successful")
        except ImportError as e:
            results['deepresearch'] = False
            logger.error(f"✗ DeepResearch import failed: {e}")
        
        # Test LlamaIndex imports
        try:
            from llama_index.core import VectorStoreIndex
            results['llamaindex'] = True
            logger.info("✓ LlamaIndex imports successful")
        except ImportError as e:
            results['llamaindex'] = False
            logger.error(f"✗ LlamaIndex import failed: {e}")
        
        return results
    
    @classmethod
    def create_requirements_file(cls) -> str:
        """
        Create a unified requirements.txt file for all repositories
        
        Returns:
            Path to the created requirements file
        """
        requirements = []
        
        # Collect requirements from each repository
        for repo_name, repo_path in cls.PATHS.items():
            if repo_name == 'backend':
                continue
                
            req_files = [
                repo_path / 'requirements.txt',
                repo_path / 'pyproject.toml',
                repo_path / 'setup.py'
            ]
            
            for req_file in req_files:
                if req_file.exists():
                    requirements.append(f"# From {repo_name} repository")
                    if req_file.name == 'requirements.txt':
                        with open(req_file, 'r') as f:
                            requirements.extend(f.read().splitlines())
                    requirements.append("")
                    break
        
        # Add our integration requirements
        requirements.extend([
            "# Integration layer requirements",
            "fastapi>=0.100.0",
            "uvicorn>=0.23.0",
            "pydantic>=2.0.0",
            "python-dotenv>=1.0.0",
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "httpx>=0.24.0"
        ])
        
        # Write to file
        output_path = cls.BASE_DIR / 'requirements.txt'
        with open(output_path, 'w') as f:
            f.write('\n'.join(requirements))
        
        logger.info(f"✓ Created unified requirements.txt at {output_path}")
        return str(output_path)
    
    @classmethod
    def get_environment_setup(cls) -> str:
        """
        Get environment setup script
        
        Returns:
            Shell script content for environment setup
        """
        script = f"""#!/bin/bash
# Multi-Agent System Environment Setup

# Set base directory
export OLARI_BASE_DIR="{cls.BASE_DIR}"

# Add repository paths to PYTHONPATH
export PYTHONPATH="$PYTHONPATH:{cls.PATHS['autogen']}"
export PYTHONPATH="$PYTHONPATH:{cls.PATHS['deepresearch']}"
export PYTHONPATH="$PYTHONPATH:{cls.PATHS['llamaindex']}"
export PYTHONPATH="$PYTHONPATH:{cls.PATHS['backend']}"

# Set repository home directories
export AUTOGEN_HOME="{cls.PATHS['autogen']}"
export DEEP_RESEARCH_HOME="{cls.PATHS['deepresearch']}"
export LLAMA_INDEX_HOME="{cls.PATHS['llamaindex']}"

echo "✓ Environment variables set"
echo "PYTHONPATH: $PYTHONPATH"
"""
        return script

def setup_environment():
    """Convenience function to set up the entire environment"""
    logger.info("Setting up multi-agent system environment...")
    
    # Setup paths
    if not PathManager.setup_paths():
        logger.error("Failed to setup paths")
        return False
    
    # Validate repositories
    validation_results = PathManager.validate_repositories()
    if not all(validation_results.values()):
        logger.warning("Some repositories failed validation")
    
    # Test imports
    import_results = PathManager.test_imports()
    if not all(import_results.values()):
        logger.error("Some imports failed")
        return False
    
    # Create requirements file
    PathManager.create_requirements_file()
    
    logger.info("✓ Environment setup complete")
    return True

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run setup
    success = setup_environment()
    exit(0 if success else 1)