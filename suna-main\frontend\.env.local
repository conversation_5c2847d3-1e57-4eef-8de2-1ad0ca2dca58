# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://llliyihxaeakffunjjcb.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Cu0nxk1MregjKOTlXNU4S0NXZlTiaRsPEmiK4isI04M

# Environment Configuration
NEXT_PUBLIC_ENV_MODE=local
NEXT_PUBLIC_URL=http://localhost:3000

# Backend URL for API calls
NEXT_PUBLIC_BACKEND_URL=http://localhost:8003

# Enable backend fallback for development
ENABLE_BACKEND_FALLBACK=true

# Backend connection timeout
BACKEND_TIMEOUT=30000

# Health check interval
BACKEND_HEALTH_CHECK_INTERVAL=30000

# Composio Integration
NEXT_PUBLIC_COMPOSIO_API_KEY=ak_hPlpPFHaetHA4ErO50-v
COMPOSIO_API_KEY=ak_hPlpPFHaetHA4ErO50-v