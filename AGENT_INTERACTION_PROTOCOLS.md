# Agent Interaction Protocols

## 🎯 Overview
This document defines how agents communicate, collaborate, and coordinate in our multi-agent system. The protocols ensure smooth conversation flow, prevent conflicts, and enable self-correcting collaborative problem-solving.

## 💬 Message Structure

### Standard Message Format
```json
{
  "id": "msg_uuid_12345",
  "timestamp": "2024-01-20T10:30:00.000Z",
  "sender": {
    "name": "DataAnalyst",
    "type": "llamaindex",
    "role": "analysis"
  },
  "recipient": {
    "name": "GroupChatManager",
    "type": "autogen",
    "role": "orchestrator"
  },
  "content": {
    "text": "Based on the internal sales data, Q3 revenue was $1.2M...",
    "format": "markdown",
    "attachments": []
  },
  "metadata": {
    "confidence": 0.95,
    "sources": ["internal_db", "sales_reports"],
    "tokens_used": 150,
    "processing_time_ms": 2300,
    "requires_followup": false,
    "references": ["msg_uuid_12344"],
    "tags": ["analysis", "financial", "q3"]
  },
  "context": {
    "conversation_id": "conv_12345",
    "round_number": 3,
    "query_type": "analytical",
    "user_intent": "revenue_analysis"
  }
}
```

### Message Types
```python
MESSAGE_TYPES = {
    "query": "Initial user question",
    "response": "Agent answer/analysis", 
    "clarification": "Request for more information",
    "correction": "Fix previous statement",
    "synthesis": "Combine multiple sources",
    "tool_call": "External tool invocation",
    "handoff": "Transfer to another agent",
    "termination": "End conversation"
}
```

## 🔄 Conversation Flow Protocols

### 1. Speaker Selection Protocol
```python
class SpeakerSelectionProtocol:
    """LLM-based dynamic speaker selection"""
    
    def select_next_speaker(self, conversation_history, available_agents):
        """
        GroupChatManager uses LLM to decide next speaker based on:
        1. Query requirements analysis
        2. Agent capability matching
        3. Conversation progress assessment
        4. Previous speaker patterns
        5. Missing information detection
        """
        
        prompt = f"""
        Conversation so far: {conversation_history}
        Available agents: {[agent.name for agent in available_agents]}
        
        Agent capabilities:
        - DataAnalyst: Internal data, RAG queries, vector search
        - ExternalResearcher: Web search, external data gathering
        - ToolAgent: API calls, external tool integration
        - Synthesizer: Combining information, final answers
        
        Based on the conversation and what's needed next, 
        select the most appropriate agent to respond.
        
        Consider:
        - What information is still missing?
        - Which agent can best provide that information?
        - Is synthesis/combination of sources needed?
        - Has this agent spoken too many times in a row?
        """
        
        return llm.select_speaker(prompt, available_agents)
```

### 2. Turn-Taking Rules
```python
TURN_TAKING_RULES = {
    "max_consecutive_turns": 2,      # Same agent can't speak >2 times in a row
    "mandatory_gaps": {              # Forced gaps between agents
        "DataAnalyst": 1,            # Must wait 1 turn after speaking
        "ExternalResearcher": 0,     # Can speak immediately if needed
        "ToolAgent": 1,              # Wait 1 turn (tools are expensive)
        "Synthesizer": 2             # Wait 2 turns (should speak last)
    },
    "priority_agents": ["UserProxy"], # Can interrupt sequence if needed
    "fallback_order": [              # If LLM selection fails
        "DataAnalyst", 
        "ExternalResearcher", 
        "Synthesizer"
    ]
}
```

### 3. Context Awareness Protocol
```python
class ContextAwarenessProtocol:
    """Ensures agents are aware of full conversation context"""
    
    def prepare_agent_context(self, agent, conversation_history):
        """
        Each agent receives:
        1. Full conversation history
        2. Their previous contributions
        3. Other agents' findings
        4. Current query status
        5. Missing information gaps
        """
        
        context = {
            "conversation_history": conversation_history,
            "my_previous_contributions": filter_by_sender(conversation_history, agent.name),
            "other_agent_findings": extract_findings(conversation_history, exclude=agent.name),
            "current_gaps": identify_information_gaps(conversation_history),
            "user_intent": extract_user_intent(conversation_history[0]),
            "progress_assessment": assess_completion_status(conversation_history)
        }
        
        return context
```

## 🔧 Self-Correction Mechanisms

### 1. Error Detection Protocol
```python
class ErrorDetectionProtocol:
    """Agents detect and correct errors in conversation"""
    
    DETECTION_TRIGGERS = [
        "contradiction_detected",     # Agent A says X, Agent B says not X
        "outdated_information",       # Information has been superseded
        "incomplete_analysis",        # Analysis missing key components
        "tool_failure",              # External tool returned error
        "confidence_too_low"         # Agent not confident in response
    ]
    
    def detect_errors(self, conversation_history, current_message):
        """Scan for errors that need correction"""
        errors = []
        
        # Check for contradictions
        contradictions = find_contradictions(conversation_history, current_message)
        
        # Check for outdated info
        outdated_facts = find_outdated_information(conversation_history)
        
        # Check confidence levels
        low_confidence = find_low_confidence_responses(conversation_history)
        
        return {
            "contradictions": contradictions,
            "outdated_info": outdated_facts,
            "low_confidence": low_confidence
        }
```

### 2. Correction Protocol
```python
class CorrectionProtocol:
    """How agents correct themselves and others"""
    
    def initiate_correction(self, agent, error_type, original_message):
        """
        Correction message format:
        """
        correction = {
            "type": "correction",
            "corrects": original_message["id"],
            "error_type": error_type,
            "content": {
                "text": f"I need to correct my previous statement about {topic}...",
                "original_claim": original_message["content"]["text"],
                "corrected_claim": "The correct information is...",
                "reason_for_correction": "New evidence shows that...",
                "confidence": 0.9
            },
            "metadata": {
                "correction_source": "self_review" | "peer_correction" | "tool_verification"
            }
        }
        
        return correction
    
    CORRECTION_ETIQUETTE = [
        "Be respectful when correcting others",
        "Clearly state what is being corrected",
        "Provide evidence for the correction",
        "Acknowledge if uncertain",
        "Thank others for corrections received"
    ]
```

### 3. Building on Previous Work
```python
class CollaborationProtocol:
    """How agents build on each other's work"""
    
    def build_on_previous(self, agent, conversation_history):
        """
        Agents should:
        1. Reference specific previous findings
        2. Add new information without repeating
        3. Synthesize different perspectives
        4. Fill identified gaps
        """
        
        building_patterns = {
            "reference": "As DataAnalyst mentioned, the Q3 data shows...",
            "extend": "Building on that analysis, external market data indicates...",
            "synthesize": "Combining the internal data with market research...",
            "fill_gap": "The missing piece seems to be...",
            "validate": "I can confirm those findings using..."
        }
        
        return self.generate_collaborative_response(agent, patterns)
```

## 🎭 Agent Role Definitions

### UserProxy Agent
```python
USERPROXY_PROTOCOL = {
    "responsibilities": [
        "Interface with human user",
        "Translate user queries into agent-understandable format",
        "Provide clarifications when agents request them",
        "Deliver final synthesized response to user"
    ],
    "communication_style": "Clear, direct, user-friendly",
    "when_to_speak": [
        "User provides new input",
        "Agents need user clarification", 
        "Final response ready for user"
    ],
    "decision_authority": "Can override agent selections if user intervenes"
}
```

### DataAnalyst Agent (LlamaIndex)
```python
DATAANALYST_PROTOCOL = {
    "responsibilities": [
        "Query internal knowledge base",
        "Perform vector similarity searches",
        "Analyze structured data",
        "Provide context from historical data"
    ],
    "communication_style": "Analytical, data-driven, precise",
    "when_to_speak": [
        "Internal data needed",
        "Historical context required",
        "Data analysis requested"
    ],
    "information_sources": ["vector_db", "internal_docs", "structured_data"],
    "typical_contributions": [
        "Historical trends",
        "Internal metrics",
        "Previous analysis results",
        "Baseline comparisons"
    ]
}
```

### ExternalResearcher Agent (DeepResearchAgent)
```python
EXTERNAL_RESEARCHER_PROTOCOL = {
    "responsibilities": [
        "Gather external information",
        "Perform web searches",
        "Browse specific websites",
        "Research current market conditions"
    ],
    "communication_style": "Investigative, comprehensive, source-conscious",
    "when_to_speak": [
        "External data needed",
        "Current market info required",
        "Competitive analysis needed",
        "Recent developments relevant"
    ],
    "information_sources": ["web_search", "news_apis", "public_databases"],
    "typical_contributions": [
        "Market trends",
        "Competitor analysis", 
        "Recent news/developments",
        "Public data comparisons"
    ]
}
```

### ToolAgent (Composio)
```python
TOOLAGENT_PROTOCOL = {
    "responsibilities": [
        "Execute API calls to external services",
        "Handle OAuth authentication",
        "Retrieve data from connected apps",
        "Perform actions on external platforms"
    ],
    "communication_style": "Precise, action-oriented, status-focused",
    "when_to_speak": [
        "External tool access needed",
        "API data retrieval required",
        "Action execution requested"
    ],
    "available_tools": ["250+ integrated apps"],
    "typical_contributions": [
        "CRM data",
        "Financial metrics from accounting software",
        "Social media analytics",
        "Project management data"
    ]
}
```

### Synthesizer Agent
```python
SYNTHESIZER_PROTOCOL = {
    "responsibilities": [
        "Combine findings from multiple agents",
        "Identify patterns across data sources",
        "Resolve contradictions",
        "Generate comprehensive final answers"
    ],
    "communication_style": "Comprehensive, balanced, conclusive",
    "when_to_speak": [
        "Multiple sources need combination",
        "Final answer needed",
        "Contradictions need resolution",
        "Summary requested"
    ],
    "typical_contributions": [
        "Comprehensive analysis",
        "Multi-source insights",
        "Actionable recommendations",
        "Executive summaries"
    ]
}
```

## ⚡ Conversation Termination Protocols

### 1. Natural Completion
```python
def check_natural_completion(conversation_history):
    """
    Conversation naturally ends when:
    1. User query fully answered
    2. All agents agree completion reached
    3. No new information to add
    4. Synthesizer provided final summary
    """
    
    completion_indicators = {
        "user_satisfaction": check_user_query_addressed(conversation_history),
        "agent_consensus": check_agent_completion_consensus(conversation_history),
        "information_completeness": assess_information_gaps(conversation_history),
        "synthesis_provided": check_final_synthesis_given(conversation_history)
    }
    
    return all(completion_indicators.values())
```

### 2. Circuit Breakers
```python
CIRCUIT_BREAKER_CONDITIONS = {
    "max_rounds": 20,                    # Hard limit on turns
    "token_limit": 8000,                 # Cost control
    "time_limit": 300,                   # 5 minutes max
    "error_threshold": 0.3,              # 30% error rate
    "repetition_detected": True,         # Circular conversations
    "user_interrupt": True               # User can stop anytime
}

def check_circuit_breakers(conversation_state):
    """Check if conversation should be forcibly terminated"""
    for condition, threshold in CIRCUIT_BREAKER_CONDITIONS.items():
        if evaluate_condition(conversation_state, condition, threshold):
            return True, condition
    return False, None
```

### 3. Graceful Degradation
```python
def handle_conversation_limits(conversation_history, limit_reason):
    """
    When limits reached, provide best possible answer:
    1. Synthesize available information
    2. Acknowledge limitations
    3. Suggest follow-up actions
    """
    
    partial_synthesis = synthesize_available_information(conversation_history)
    
    graceful_response = f"""
    Based on the analysis so far:
    
    {partial_synthesis}
    
    Note: This conversation reached {limit_reason}. 
    The above represents our best analysis with available information.
    
    For a more complete analysis, consider:
    - Breaking the query into smaller parts
    - Providing more specific requirements
    - Allowing more time for external research
    """
    
    return graceful_response
```

## 📊 Quality Assurance Protocols

### 1. Response Validation
```python
def validate_agent_response(response, agent_type):
    """Validate agent responses meet quality standards"""
    
    validation_rules = {
        "DataAnalyst": {
            "must_cite_sources": True,
            "min_confidence": 0.7,
            "required_fields": ["data_source", "analysis_method"]
        },
        "ExternalResearcher": {
            "must_cite_sources": True,
            "min_confidence": 0.6,
            "required_fields": ["search_terms", "source_urls"]
        },
        "Synthesizer": {
            "must_reference_agents": True,
            "min_confidence": 0.8,
            "required_fields": ["synthesis_method", "agent_contributions"]
        }
    }
    
    rules = validation_rules.get(agent_type, {})
    return apply_validation_rules(response, rules)
```

### 2. Conversation Quality Metrics
```python
QUALITY_METRICS = {
    "coherence": "Logical flow between messages",
    "efficiency": "Minimal redundant exchanges", 
    "completeness": "All query aspects addressed",
    "accuracy": "Factual correctness of information",
    "collaboration": "Agents building on each other's work",
    "resolution": "Clear conclusion reached"
}

def assess_conversation_quality(conversation_history):
    """Rate conversation on quality dimensions"""
    scores = {}
    for metric in QUALITY_METRICS:
        scores[metric] = calculate_metric_score(conversation_history, metric)
    
    return scores
```

## 🔄 Learning and Adaptation

### 1. Conversation Pattern Learning
```python
class ConversationPatternLearner:
    """Learn from successful conversation patterns"""
    
    def analyze_successful_patterns(self, conversation_logs):
        """
        Identify patterns in successful conversations:
        - Optimal speaker sequences
        - Effective collaboration strategies
        - Best termination points
        """
        
        patterns = {
            "speaker_sequences": extract_speaker_patterns(conversation_logs),
            "collaboration_moves": identify_collaboration_patterns(conversation_logs),
            "optimal_length": calculate_optimal_conversation_length(conversation_logs)
        }
        
        return patterns
    
    def adapt_protocols(self, learned_patterns):
        """Update protocols based on learned patterns"""
        # Update speaker selection weights
        # Adjust termination criteria
        # Refine collaboration prompts
        pass
```

### 2. Error Pattern Recognition
```python
def learn_from_errors(error_logs):
    """
    Learn from conversation failures:
    - Common error patterns
    - Agent failure modes
    - User dissatisfaction causes
    """
    
    error_patterns = {
        "agent_conflicts": identify_conflict_patterns(error_logs),
        "information_gaps": find_common_gaps(error_logs),
        "termination_issues": analyze_premature_terminations(error_logs)
    }
    
    return error_patterns
```

---

## 🎯 Protocol Summary

**Key Principles:**
1. **Dynamic Speaker Selection**: LLM decides who speaks next based on context
2. **Full Context Awareness**: Every agent sees complete conversation history
3. **Self-Correction**: Agents can correct themselves and each other
4. **Collaborative Building**: Agents build on each other's work
5. **Quality Assurance**: Multiple validation layers ensure response quality
6. **Graceful Termination**: Clear completion criteria and circuit breakers

**Success Indicators:**
- Agents reference each other's contributions
- Information builds progressively without repetition
- Errors are caught and corrected quickly
- Final synthesis incorporates all relevant findings
- User query is comprehensively addressed

---

*These protocols ensure smooth, productive, and self-correcting multi-agent conversations.*