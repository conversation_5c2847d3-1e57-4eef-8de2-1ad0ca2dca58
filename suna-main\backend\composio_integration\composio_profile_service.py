import json
import hashlib
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from uuid import uuid4
from cryptography.fernet import Fernet
import os

from services.supabase import DBConnection
from utils.logger import logger


@dataclass
class ComposioProfile:
    profile_id: str
    account_id: str
    mcp_qualified_name: str
    profile_name: str
    display_name: str
    encrypted_config: str
    config_hash: str
    toolkit_slug: str
    toolkit_name: str
    mcp_url: str
    redirect_url: Optional[str] = None
    is_active: bool = True
    is_default: bool = False
    is_connected: bool = False
    created_at: datetime = None
    updated_at: datetime = None


class ComposioProfileService:
    def __init__(self, db_connection: Optional[DBConnection] = None):
        self.db = db_connection or DBConnection()
        
    def _get_encryption_key(self) -> bytes:
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            raise ValueError("ENCRYPTION_KEY environment variable is required")
        return key.encode()

    def _encrypt_config(self, config_json: str) -> str:
        fernet = Fernet(self._get_encryption_key())
        return fernet.encrypt(config_json.encode()).decode()

    def _decrypt_config(self, encrypted_config: str) -> Dict[str, Any]:
        fernet = Fernet(self._get_encryption_key())
        decrypted = fernet.decrypt(encrypted_config.encode()).decode()
        return json.loads(decrypted)

    def _generate_config_hash(self, config_json: str) -> str:
        return hashlib.sha256(config_json.encode()).hexdigest()

    def _parse_datetime(self, datetime_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string with various formats, handling microsecond truncation issues."""
        if not datetime_str:
            return None
        
        try:
            # Replace Z with +00:00 for timezone
            datetime_str = datetime_str.replace('Z', '+00:00')
            
            # Handle microseconds with variable precision
            # Format: 2025-09-20T20:47:43.7878+00:00 (4 digits) needs to be padded to 6
            if '.' in datetime_str and '+' in datetime_str:
                parts = datetime_str.split('.')
                microsec_and_tz = parts[1].split('+')
                microseconds = microsec_and_tz[0]
                
                # Pad or truncate microseconds to 6 digits
                if len(microseconds) < 6:
                    microseconds = microseconds.ljust(6, '0')
                elif len(microseconds) > 6:
                    microseconds = microseconds[:6]
                
                datetime_str = f"{parts[0]}.{microseconds}+{microsec_and_tz[1]}"
            
            return datetime.fromisoformat(datetime_str)
        except Exception as e:
            logger.warning(f"Failed to parse datetime '{datetime_str}': {e}")
            return None

    def _build_config(
        self,
        toolkit_slug: str,
        toolkit_name: str,
        mcp_url: str,
        redirect_url: Optional[str] = None,
        user_id: str = "default",
        connected_account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        return {
            "type": "composio",
            "toolkit_slug": toolkit_slug,
            "toolkit_name": toolkit_name,
            "mcp_url": mcp_url,
            "redirect_url": redirect_url,
            "user_id": user_id,
            "connected_account_id": connected_account_id,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

    async def _generate_unique_profile_name(self, base_name: str, account_id: str, mcp_qualified_name: str, client) -> str:
        original_name = base_name
        counter = 1
        current_name = base_name
        
        while True:
            existing = await client.table('user_mcp_credential_profiles').select('profile_id').eq(
                'account_id', account_id
            ).eq('mcp_qualified_name', mcp_qualified_name).eq('profile_name', current_name).execute()
            
            if not existing.data:
                return current_name
            
            counter += 1
            current_name = f"{original_name} ({counter})"

    async def create_profile(
        self,
        account_id: str,
        profile_name: str,
        toolkit_slug: str,
        toolkit_name: str,
        mcp_url: str,
        redirect_url: Optional[str] = None,
        user_id: str = "default",
        is_default: bool = False,
        jwt_token: Optional[str] = None,
        connected_account_id: Optional[str] = None
    ) -> ComposioProfile:
        try:
            logger.info(f"Creating Composio profile for user: {account_id}, toolkit: {toolkit_slug}")
            logger.info(f"MCP URL to store: {mcp_url}")
            logger.info(f"Connected account ID: {connected_account_id}")
            
            config = self._build_config(
                toolkit_slug, toolkit_name, mcp_url, redirect_url, user_id, connected_account_id
            )
            config_json = json.dumps(config, sort_keys=True)
            encrypted_config = self._encrypt_config(config_json)
            config_hash = self._generate_config_hash(config_json)
            
            mcp_qualified_name = f"composio.{toolkit_slug}"
            profile_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            # Use authenticated client if JWT token is provided, otherwise fall back to default
            if jwt_token:
                client = await self.db.get_authenticated_client(jwt_token)
            else:
                client = await self.db.client
            
            unique_profile_name = await self._generate_unique_profile_name(
                profile_name, account_id, mcp_qualified_name, client
            )
            
            if unique_profile_name != profile_name:
                logger.info(f"Generated unique profile name: {unique_profile_name} (original: {profile_name})")
            
            if is_default:
                await client.table('user_mcp_credential_profiles').update({
                    'is_default': False
                }).eq('account_id', account_id).eq('mcp_qualified_name', mcp_qualified_name).execute()
            
            result = await client.table('user_mcp_credential_profiles').insert({
                'profile_id': profile_id,
                'account_id': account_id,
                'mcp_qualified_name': mcp_qualified_name,
                'profile_name': unique_profile_name,
                'display_name': unique_profile_name,
                'encrypted_config': encrypted_config,
                'config_hash': config_hash,
                'is_active': True,
                'is_default': is_default,
                'created_at': now.isoformat(),
                'updated_at': now.isoformat()
            }).execute()
            
            if not result.data:
                raise Exception("Failed to create profile in database")
            
            logger.info(f"Successfully created Composio profile: {profile_id}")
            
            return ComposioProfile(
                profile_id=profile_id,
                account_id=account_id,
                mcp_qualified_name=mcp_qualified_name,
                profile_name=unique_profile_name,
                display_name=unique_profile_name,
                encrypted_config=encrypted_config,
                config_hash=config_hash,
                toolkit_slug=toolkit_slug,
                toolkit_name=toolkit_name,
                mcp_url=mcp_url,
                redirect_url=redirect_url,
                is_active=True,
                is_default=is_default,
                is_connected=False,  # Will be set to True after OAuth completion
                created_at=now,
                updated_at=now
            )
            
        except Exception as e:
            logger.error(f"Failed to create Composio profile: {e}", exc_info=True)
            raise

    async def get_mcp_config_for_agent(self, profile_id: str) -> Dict[str, Any]:
        try:
            client = await self.db.client
            result = await client.table('user_mcp_credential_profiles').select('*').eq(
                'profile_id', profile_id
            ).execute()
            
            if not result.data:
                raise ValueError(f"Profile {profile_id} not found")
            
            profile_data = result.data[0]

            config = self._decrypt_config(profile_data['encrypted_config'])
            
            if config.get('type') != 'composio':
                raise ValueError(f"Profile {profile_id} is not a Composio profile")
            
            return {
                "name": config['toolkit_name'],
                "type": "composio",
                "mcp_qualified_name": profile_data['mcp_qualified_name'],
                "toolkit_slug": config.get('toolkit_slug', ''),
                "config": {
                    "profile_id": profile_id
                },
                "enabledTools": []
            }
            
        except Exception as e:
            logger.error(f"Failed to get MCP config for profile {profile_id}: {e}", exc_info=True)
            raise
    
    async def get_mcp_url_for_runtime(self, profile_id: str) -> str:
        try:
            client = await self.db.client
            
            result = await client.table('user_mcp_credential_profiles').select('*').eq(
                'profile_id', profile_id
            ).execute()
            
            if not result.data:
                raise ValueError(f"Profile {profile_id} not found")
            
            profile_data = result.data[0]
            
            config = self._decrypt_config(profile_data['encrypted_config'])
            
            if config.get('type') != 'composio':
                raise ValueError(f"Profile {profile_id} is not a Composio profile")
            
            mcp_url = config.get('mcp_url')
            if not mcp_url:
                raise ValueError(f"Profile {profile_id} has no MCP URL")
            
            logger.info(f"Retrieved MCP URL for profile {profile_id}")
            return mcp_url
            
        except Exception as e:
            logger.error(f"Failed to get MCP URL for profile {profile_id}: {e}", exc_info=True)
            raise

    async def get_profile_config(self, profile_id: str) -> Dict[str, Any]:
        try:
            client = await self.db.client
            
            result = await client.table('user_mcp_credential_profiles').select('encrypted_config').eq(
                'profile_id', profile_id
            ).execute()
            
            if not result.data:
                raise ValueError(f"Profile {profile_id} not found")
            
            return self._decrypt_config(result.data[0]['encrypted_config'])
            
        except Exception as e:
            logger.error(f"Failed to get config for profile {profile_id}: {e}", exc_info=True)
            raise

    async def get_profile(self, profile_id: str, account_id: str) -> Optional[ComposioProfile]:
        """Get a single profile by ID and account ID"""
        try:
            client = await self.db.client
            
            result = await client.table('user_mcp_credential_profiles').select('*').eq(
                'profile_id', profile_id
            ).eq('account_id', account_id).execute()
            
            if not result.data:
                return None
                
            row = result.data[0]
            config = self._decrypt_config(row['encrypted_config'])
            
            return ComposioProfile(
                profile_id=row['profile_id'],
                account_id=row['account_id'],
                mcp_qualified_name=row['mcp_qualified_name'],
                profile_name=row['profile_name'],
                display_name=row['display_name'],
                encrypted_config=row['encrypted_config'],
                config_hash=row['config_hash'],
                toolkit_slug=config.get('toolkit_slug', ''),
                toolkit_name=config.get('toolkit_name', ''),
                mcp_url=config.get('mcp_url', ''),
                redirect_url=config.get('redirect_url'),
                is_active=row.get('is_active', True),
                is_default=row.get('is_default', False),
                is_connected=config.get('oauth_completed', False),
                created_at=self._parse_datetime(row.get('created_at')),
                updated_at=self._parse_datetime(row.get('updated_at'))
            )
            
        except Exception as e:
            logger.error(f"Failed to get profile {profile_id}: {e}", exc_info=True)
            return None

    async def mark_profile_connected(self, connection_id: str, account_id: str, jwt_token: Optional[str] = None) -> bool:
        """Mark a profile as connected after OAuth completion."""
        try:
            # Use authenticated client if JWT token is provided
            if jwt_token:
                client = await self.db.get_authenticated_client(jwt_token)
            else:
                client = await self.db.client
            
            # Find profiles that match the connection_id
            profiles = await self.get_profiles(account_id, jwt_token=jwt_token)
            
            for profile in profiles:
                # Get the config to check for connected_account_id
                config = self._decrypt_config(profile.encrypted_config)
                
                # Check if this profile matches the connection_id (either by profile_id or connected_account_id)
                if (profile.profile_id == connection_id or 
                    config.get('connected_account_id') == connection_id):
                    
                    # Mark as OAuth completed
                    config['oauth_completed'] = True
                    config['oauth_completed_at'] = datetime.now(timezone.utc).isoformat()
                    
                    # Re-encrypt the config
                    config_json = json.dumps(config, sort_keys=True)
                    encrypted_config = self._encrypt_config(config_json)
                    config_hash = self._generate_config_hash(config_json)
                    
                    # Update the profile
                    await client.table('user_mcp_credential_profiles').update({
                        'encrypted_config': encrypted_config,
                        'config_hash': config_hash,
                        'updated_at': datetime.now(timezone.utc).isoformat()
                    }).eq('profile_id', profile.profile_id).execute()
                    
                    logger.info(f"Marked profile {profile.profile_id} as connected for connection_id {connection_id}")
                    return True
            
            logger.warning(f"No profile found with connection_id {connection_id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to mark profile as connected: {e}", exc_info=True)
            return False
    
    async def mark_profile_connected_by_new_account_id(self, new_account_id: str, original_account_id: str) -> bool:
        """Update profile with new connected account ID after OAuth completion."""
        try:
            client = await self.db.client
            
            # Find ALL profiles since we don't have account_id
            all_profiles = await client.table('user_mcp_credential_profiles').select('*').like(
                'mcp_qualified_name', 'composio.%'
            ).execute()
            
            for profile_data in all_profiles.data:
                config = self._decrypt_config(profile_data['encrypted_config'])
                
                # Check if this profile has the original account ID
                if config.get('connected_account_id') == original_account_id:
                    # Update with the new account ID and mark as connected
                    config['connected_account_id'] = new_account_id
                    config['oauth_completed'] = True
                    config['oauth_completed_at'] = datetime.now(timezone.utc).isoformat()
                    
                    # Re-encrypt and save
                    config_json = json.dumps(config, sort_keys=True)
                    encrypted_config = self._encrypt_config(config_json)
                    config_hash = self._generate_config_hash(config_json)
                    
                    await client.table('user_mcp_credential_profiles').update({
                        'encrypted_config': encrypted_config,
                        'config_hash': config_hash,
                        'updated_at': datetime.now(timezone.utc).isoformat()
                    }).eq('profile_id', profile_data['profile_id']).execute()
                    
                    logger.info(f"Updated profile {profile_data['profile_id']} with new account ID {new_account_id}")
                    return True
            
            logger.warning(f"No profile found with original account ID {original_account_id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to update profile with new account ID: {e}", exc_info=True)
            return False
    
    async def get_profiles(self, account_id: str, toolkit_slug: Optional[str] = None, jwt_token: Optional[str] = None) -> List[ComposioProfile]:
        try:
            # Use authenticated client if JWT token is provided
            if jwt_token:
                client = await self.db.get_authenticated_client(jwt_token)
            else:
                client = await self.db.client
            
            query = client.table('user_mcp_credential_profiles').select('*').eq('account_id', account_id)
            
            if toolkit_slug:
                query = query.eq('mcp_qualified_name', f"composio.{toolkit_slug}")
            else:
                query = query.like('mcp_qualified_name', 'composio.%')
            
            result = await query.execute()
            
            profiles = []
            for row in result.data:
                config = self._decrypt_config(row['encrypted_config'])
                
                profile = ComposioProfile(
                    profile_id=row['profile_id'],
                    account_id=row['account_id'],
                    mcp_qualified_name=row['mcp_qualified_name'],
                    profile_name=row['profile_name'],
                    display_name=row['display_name'],
                    encrypted_config=row['encrypted_config'],
                    config_hash=row['config_hash'],
                    toolkit_slug=config.get('toolkit_slug', ''),
                    toolkit_name=config.get('toolkit_name', ''),
                    mcp_url=config.get('mcp_url', ''),
                    redirect_url=config.get('redirect_url'),
                    is_active=row.get('is_active', True),
                    is_default=row.get('is_default', False),
                    is_connected=config.get('oauth_completed', False),
                    created_at=self._parse_datetime(row.get('created_at')),
                    updated_at=self._parse_datetime(row.get('updated_at'))
                )
                profiles.append(profile)
            
            return profiles
            
        except Exception as e:
            logger.error(f"Failed to get Composio profiles: {e}", exc_info=True)
            raise 