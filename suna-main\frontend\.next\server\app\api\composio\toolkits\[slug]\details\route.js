/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/composio/toolkits/[slug]/details/route";
exports.ids = ["app/api/composio/toolkits/[slug]/details/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_composio_toolkits_slug_details_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/composio/toolkits/[slug]/details/route.ts */ \"(rsc)/./src/app/api/composio/toolkits/[slug]/details/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/composio/toolkits/[slug]/details/route\",\n        pathname: \"/api/composio/toolkits/[slug]/details\",\n        filename: \"route\",\n        bundlePath: \"app/api/composio/toolkits/[slug]/details/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/api/composio/toolkits/[slug]/details/route.ts\",\n    nextConfigOutput,\n    userland: _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_composio_toolkits_slug_details_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/composio/toolkits/[slug]/details/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00c3b743d074a69244eab1d948789d902598d3c17c\": () => (/* reexport safe */ _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__.createClient)\n/* harmony export */ });\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/lib/supabase/server.ts */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyJTJGbW50JTJGYyUyRlVzZXJzJTJGSWJyYWhpbSUyRkRvd25sb2FkcyUyRm9sYXJpJTJGc3VuYS1tYWluJTJGZnJvbnRlbmQlMkZzcmMlMkZsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiUyQyU1QiU3QiUyMmlkJTIyJTNBJTIyMDBjM2I3NDNkMDc0YTY5MjQ0ZWFiMWQ5NDg3ODlkOTAyNTk4ZDNjMTdjJTIyJTJDJTIyZXhwb3J0ZWROYW1lJTIyJTNBJTIyY3JlYXRlQ2xpZW50JTIyJTJDJTIyZmlsZW5hbWUlMjIlM0ElMjJsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiU3RCU1RCU1RCU1RCZfX2NsaWVudF9pbXBvcnRlZF9fPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ2lLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBjcmVhdGVDbGllbnQgYXMgXCIwMGMzYjc0M2QwNzRhNjkyNDRlYWIxZDk0ODc4OWQ5MDI1OThkM2MxN2NcIiB9IGZyb20gXCIvbW50L2MvVXNlcnMvSWJyYWhpbS9Eb3dubG9hZHMvb2xhcmkvc3VuYS1tYWluL2Zyb250ZW5kL3NyYy9saWIvc3VwYWJhc2Uvc2VydmVyLnRzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/composio/toolkits/[slug]/details/route.ts":
/*!***************************************************************!*\
  !*** ./src/app/api/composio/toolkits/[slug]/details/route.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nconst API_URL = \"http://localhost:8003\" || 0;\n// Mock detailed toolkit data\nconst MOCK_TOOLKIT_DETAILS = {\n    gmail: {\n        success: true,\n        toolkit: {\n            id: 'gmail',\n            name: 'Gmail',\n            slug: 'gmail',\n            category: 'communication',\n            description: 'Send, receive, and manage emails with Gmail. Integrate Gmail functionality into your applications.',\n            long_description: 'Gmail integration provides comprehensive email management capabilities including sending, receiving, organizing, and searching emails. Perfect for automating email workflows, managing customer communications, and building email-powered applications.',\n            icon: 'https://composio.dev/icons/gmail.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 15,\n            popularity: 95,\n            tags: [\n                'email',\n                'google',\n                'messaging',\n                'communication'\n            ],\n            features: [\n                'send_email',\n                'receive_email',\n                'manage_labels',\n                'search_emails',\n                'manage_drafts',\n                'manage_attachments'\n            ],\n            auth_config: {\n                oauth2: {\n                    client_id: 'mock-gmail-client-id',\n                    scopes: [\n                        'https://www.googleapis.com/auth/gmail.modify'\n                    ],\n                    authorize_url: 'https://accounts.google.com/oauth/authorize',\n                    token_url: 'https://oauth2.googleapis.com/token'\n                }\n            },\n            tools: [\n                {\n                    id: 'send_email',\n                    name: 'Send Email',\n                    description: 'Send an email to one or more recipients',\n                    category: 'action'\n                },\n                {\n                    id: 'list_emails',\n                    name: 'List Emails',\n                    description: 'List emails in the inbox',\n                    category: 'query'\n                },\n                {\n                    id: 'search_emails',\n                    name: 'Search Emails',\n                    description: 'Search for emails matching specific criteria',\n                    category: 'query'\n                },\n                {\n                    id: 'create_label',\n                    name: 'Create Label',\n                    description: 'Create a new label for organizing emails',\n                    category: 'action'\n                },\n                {\n                    id: 'manage_drafts',\n                    name: 'Manage Drafts',\n                    description: 'Create, update, and send draft emails',\n                    category: 'action'\n                }\n            ],\n            setup_instructions: [\n                'Go to Google Cloud Console and create a new project',\n                'Enable Gmail API for your project',\n                'Create OAuth 2.0 credentials',\n                'Add authorized redirect URIs',\n                'Configure the integration with your credentials'\n            ],\n            documentation_url: 'https://developers.google.com/gmail/api',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    },\n    slack: {\n        success: true,\n        toolkit: {\n            id: 'slack',\n            name: 'Slack',\n            slug: 'slack',\n            category: 'communication',\n            description: 'Connect with Slack to send messages, manage channels, and interact with workspaces.',\n            long_description: 'Slack integration enables powerful team collaboration features including messaging, channel management, file sharing, and workspace administration. Build bots, automate workflows, and enhance team productivity.',\n            icon: 'https://composio.dev/icons/slack.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 20,\n            popularity: 90,\n            tags: [\n                'messaging',\n                'team',\n                'collaboration',\n                'chat'\n            ],\n            features: [\n                'send_message',\n                'create_channel',\n                'upload_file',\n                'manage_users',\n                'schedule_messages',\n                'manage_reactions'\n            ],\n            auth_config: {\n                oauth2: {\n                    client_id: 'mock-slack-client-id',\n                    scopes: [\n                        'chat:write',\n                        'channels:manage',\n                        'files:write',\n                        'users:read'\n                    ],\n                    authorize_url: 'https://slack.com/oauth/v2/authorize',\n                    token_url: 'https://slack.com/api/oauth.v2.access'\n                }\n            },\n            tools: [\n                {\n                    id: 'send_message',\n                    name: 'Send Message',\n                    description: 'Send a message to a channel or user',\n                    category: 'action'\n                },\n                {\n                    id: 'create_channel',\n                    name: 'Create Channel',\n                    description: 'Create a new Slack channel',\n                    category: 'action'\n                },\n                {\n                    id: 'list_channels',\n                    name: 'List Channels',\n                    description: 'List all channels in the workspace',\n                    category: 'query'\n                },\n                {\n                    id: 'upload_file',\n                    name: 'Upload File',\n                    description: 'Upload a file to Slack',\n                    category: 'action'\n                },\n                {\n                    id: 'add_reaction',\n                    name: 'Add Reaction',\n                    description: 'Add an emoji reaction to a message',\n                    category: 'action'\n                }\n            ],\n            setup_instructions: [\n                'Create a Slack app at api.slack.com',\n                'Configure OAuth & Permissions',\n                'Add required scopes',\n                'Install the app to your workspace',\n                'Use the provided tokens for authentication'\n            ],\n            documentation_url: 'https://api.slack.com',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    },\n    github: {\n        success: true,\n        toolkit: {\n            id: 'github',\n            name: 'GitHub',\n            slug: 'github',\n            category: 'developer_tools',\n            description: 'Manage repositories, issues, pull requests, and more on GitHub.',\n            long_description: 'GitHub integration provides complete access to repository management, issue tracking, pull requests, actions, and more. Perfect for automating development workflows, managing code reviews, and building developer tools.',\n            icon: 'https://composio.dev/icons/github.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2',\n                'api_key'\n            ],\n            tools_count: 35,\n            popularity: 98,\n            tags: [\n                'git',\n                'version_control',\n                'development',\n                'code',\n                'collaboration'\n            ],\n            features: [\n                'create_issue',\n                'create_pr',\n                'manage_repos',\n                'code_review',\n                'manage_workflows',\n                'manage_releases'\n            ],\n            auth_config: {\n                oauth2: {\n                    client_id: 'mock-github-client-id',\n                    scopes: [\n                        'repo',\n                        'user',\n                        'workflow'\n                    ],\n                    authorize_url: 'https://github.com/login/oauth/authorize',\n                    token_url: 'https://github.com/login/oauth/access_token'\n                },\n                api_key: {\n                    header_name: 'Authorization',\n                    header_value_prefix: 'token '\n                }\n            },\n            tools: [\n                {\n                    id: 'create_issue',\n                    name: 'Create Issue',\n                    description: 'Create a new issue in a repository',\n                    category: 'action'\n                },\n                {\n                    id: 'create_pr',\n                    name: 'Create Pull Request',\n                    description: 'Create a new pull request',\n                    category: 'action'\n                },\n                {\n                    id: 'list_repos',\n                    name: 'List Repositories',\n                    description: 'List repositories for a user or organization',\n                    category: 'query'\n                },\n                {\n                    id: 'merge_pr',\n                    name: 'Merge Pull Request',\n                    description: 'Merge an approved pull request',\n                    category: 'action'\n                },\n                {\n                    id: 'create_release',\n                    name: 'Create Release',\n                    description: 'Create a new release',\n                    category: 'action'\n                }\n            ],\n            setup_instructions: [\n                'Go to GitHub Settings > Developer settings',\n                'Create a new OAuth App or Personal Access Token',\n                'Configure the required permissions',\n                'Add callback URLs if using OAuth',\n                'Use the credentials in your integration'\n            ],\n            documentation_url: 'https://docs.github.com/en/rest',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    },\n    notion: {\n        success: true,\n        toolkit: {\n            id: 'notion',\n            name: 'Notion',\n            slug: 'notion',\n            category: 'productivity',\n            description: 'Create, update, and manage pages and databases in Notion.',\n            long_description: 'Notion integration allows you to programmatically interact with Notion workspaces, pages, and databases. Build knowledge bases, project management systems, and content management tools.',\n            icon: 'https://composio.dev/icons/notion.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 18,\n            popularity: 85,\n            tags: [\n                'notes',\n                'documentation',\n                'knowledge_base',\n                'productivity'\n            ],\n            features: [\n                'create_page',\n                'update_database',\n                'search_content',\n                'manage_workspace',\n                'manage_blocks',\n                'manage_comments'\n            ],\n            auth_config: {\n                oauth2: {\n                    client_id: 'mock-notion-client-id',\n                    scopes: [],\n                    authorize_url: 'https://api.notion.com/v1/oauth/authorize',\n                    token_url: 'https://api.notion.com/v1/oauth/token'\n                }\n            },\n            tools: [\n                {\n                    id: 'create_page',\n                    name: 'Create Page',\n                    description: 'Create a new page in Notion',\n                    category: 'action'\n                },\n                {\n                    id: 'update_page',\n                    name: 'Update Page',\n                    description: 'Update an existing page',\n                    category: 'action'\n                },\n                {\n                    id: 'create_database',\n                    name: 'Create Database',\n                    description: 'Create a new database',\n                    category: 'action'\n                },\n                {\n                    id: 'query_database',\n                    name: 'Query Database',\n                    description: 'Query a database with filters',\n                    category: 'query'\n                },\n                {\n                    id: 'add_comment',\n                    name: 'Add Comment',\n                    description: 'Add a comment to a page or block',\n                    category: 'action'\n                }\n            ],\n            setup_instructions: [\n                'Create a Notion integration at notion.so/my-integrations',\n                'Get your integration token',\n                'Share pages/databases with your integration',\n                'Configure OAuth if needed',\n                'Use the token for API access'\n            ],\n            documentation_url: 'https://developers.notion.com',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    },\n    discord: {\n        success: true,\n        toolkit: {\n            id: 'discord',\n            name: 'Discord',\n            slug: 'discord',\n            category: 'communication',\n            description: 'Send messages, manage servers, and interact with Discord communities.',\n            long_description: 'Discord integration enables bot creation, server management, and community interaction. Build moderation tools, game bots, or community management systems.',\n            icon: 'https://composio.dev/icons/discord.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2',\n                'bot_token'\n            ],\n            tools_count: 25,\n            popularity: 80,\n            tags: [\n                'messaging',\n                'community',\n                'gaming',\n                'voice'\n            ],\n            features: [\n                'send_message',\n                'manage_server',\n                'create_channel',\n                'moderate_users',\n                'manage_roles',\n                'voice_features'\n            ],\n            auth_config: {\n                bot_token: {\n                    header_name: 'Authorization',\n                    header_value_prefix: 'Bot '\n                },\n                oauth2: {\n                    client_id: 'mock-discord-client-id',\n                    scopes: [\n                        'bot',\n                        'applications.commands'\n                    ],\n                    authorize_url: 'https://discord.com/api/oauth2/authorize',\n                    token_url: 'https://discord.com/api/oauth2/token'\n                }\n            },\n            tools: [\n                {\n                    id: 'send_message',\n                    name: 'Send Message',\n                    description: 'Send a message to a channel',\n                    category: 'action'\n                },\n                {\n                    id: 'create_channel',\n                    name: 'Create Channel',\n                    description: 'Create a new channel',\n                    category: 'action'\n                },\n                {\n                    id: 'ban_user',\n                    name: 'Ban User',\n                    description: 'Ban a user from the server',\n                    category: 'action'\n                },\n                {\n                    id: 'assign_role',\n                    name: 'Assign Role',\n                    description: 'Assign a role to a user',\n                    category: 'action'\n                },\n                {\n                    id: 'create_invite',\n                    name: 'Create Invite',\n                    description: 'Create an invite link',\n                    category: 'action'\n                }\n            ],\n            setup_instructions: [\n                'Go to Discord Developer Portal',\n                'Create a new application',\n                'Add a bot to your application',\n                'Configure bot permissions',\n                'Invite the bot to your server'\n            ],\n            documentation_url: 'https://discord.com/developers/docs',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    },\n    google_sheets: {\n        success: true,\n        toolkit: {\n            id: 'google_sheets',\n            name: 'Google Sheets',\n            slug: 'google_sheets',\n            category: 'productivity',\n            description: 'Read, write, and manage Google Sheets spreadsheets. Automate data entry and analysis.',\n            long_description: 'Google Sheets integration provides comprehensive spreadsheet management capabilities including reading data, writing data, creating sheets, formatting cells, and managing formulas. Perfect for automating data workflows, building reports, and integrating spreadsheet functionality into applications.',\n            icon: 'https://composio.dev/icons/google-sheets.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 22,\n            popularity: 92,\n            tags: [\n                'spreadsheet',\n                'google',\n                'data',\n                'productivity',\n                'automation'\n            ],\n            features: [\n                'read_data',\n                'write_data',\n                'create_sheet',\n                'format_cells',\n                'manage_formulas',\n                'create_charts',\n                'manage_permissions'\n            ],\n            auth_config: {\n                oauth2: {\n                    client_id: 'mock-google-sheets-client-id',\n                    scopes: [\n                        'https://www.googleapis.com/auth/spreadsheets',\n                        'https://www.googleapis.com/auth/drive.file'\n                    ],\n                    authorize_url: 'https://accounts.google.com/oauth/authorize',\n                    token_url: 'https://oauth2.googleapis.com/token'\n                }\n            },\n            tools: [\n                {\n                    id: 'read_range',\n                    name: 'Read Range',\n                    description: 'Read data from a specific range in a spreadsheet',\n                    category: 'query'\n                },\n                {\n                    id: 'write_range',\n                    name: 'Write Range',\n                    description: 'Write data to a specific range in a spreadsheet',\n                    category: 'action'\n                },\n                {\n                    id: 'create_spreadsheet',\n                    name: 'Create Spreadsheet',\n                    description: 'Create a new Google Sheets spreadsheet',\n                    category: 'action'\n                },\n                {\n                    id: 'add_sheet',\n                    name: 'Add Sheet',\n                    description: 'Add a new sheet to an existing spreadsheet',\n                    category: 'action'\n                },\n                {\n                    id: 'format_cells',\n                    name: 'Format Cells',\n                    description: 'Apply formatting to cells in a spreadsheet',\n                    category: 'action'\n                },\n                {\n                    id: 'create_chart',\n                    name: 'Create Chart',\n                    description: 'Create a chart from spreadsheet data',\n                    category: 'action'\n                },\n                {\n                    id: 'apply_formula',\n                    name: 'Apply Formula',\n                    description: 'Apply formulas to cells',\n                    category: 'action'\n                },\n                {\n                    id: 'batch_update',\n                    name: 'Batch Update',\n                    description: 'Perform multiple updates in a single request',\n                    category: 'action'\n                }\n            ],\n            setup_instructions: [\n                'Go to Google Cloud Console and create a new project',\n                'Enable Google Sheets API and Google Drive API',\n                'Create OAuth 2.0 credentials',\n                'Add authorized redirect URIs',\n                'Configure the integration with your credentials',\n                'Grant necessary permissions to access spreadsheets'\n            ],\n            documentation_url: 'https://developers.google.com/sheets/api',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    }\n};\n// Default mock data for unknown toolkits\nconst DEFAULT_MOCK_TOOLKIT = (slug)=>({\n        success: true,\n        toolkit: {\n            id: slug,\n            name: slug.charAt(0).toUpperCase() + slug.slice(1),\n            slug: slug,\n            category: 'other',\n            description: `${slug.charAt(0).toUpperCase() + slug.slice(1)} integration toolkit`,\n            long_description: `This is a mock toolkit for ${slug}. In production, this would contain detailed information about the ${slug} integration.`,\n            icon: `https://composio.dev/icons/${slug}.svg`,\n            status: 'active',\n            supported_auth: [\n                'api_key'\n            ],\n            tools_count: 10,\n            popularity: 50,\n            tags: [\n                slug,\n                'integration'\n            ],\n            features: [\n                'basic_operations'\n            ],\n            auth_config: {\n                api_key: {\n                    header_name: 'Authorization',\n                    header_value_prefix: 'Bearer '\n                }\n            },\n            tools: [\n                {\n                    id: 'action_1',\n                    name: 'Action 1',\n                    description: 'Perform action 1',\n                    category: 'action'\n                },\n                {\n                    id: 'query_1',\n                    name: 'Query 1',\n                    description: 'Query data',\n                    category: 'query'\n                }\n            ],\n            setup_instructions: [\n                'Configure API credentials',\n                'Set up authentication',\n                'Test the connection'\n            ],\n            documentation_url: `https://docs.example.com/${slug}`,\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z'\n        }\n    });\nasync function GET(request, { params }) {\n    try {\n        const { slug } = await params;\n        if (!slug) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Toolkit slug is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get the current session for authentication (optional for this endpoint)\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        // If API_URL is not configured or empty, return mock data\n        if (!API_URL || API_URL.trim() === '') {\n            console.log(`Using mock data for toolkit details: ${slug}`);\n            const mockData = MOCK_TOOLKIT_DETAILS[slug] || DEFAULT_MOCK_TOOLKIT(slug);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(mockData);\n        }\n        // Validate URL format\n        try {\n            new URL(`${API_URL}/api/composio/toolkits/${slug}/details`);\n        } catch (urlError) {\n            console.log('Invalid backend URL format, returning mock data');\n            const mockData = MOCK_TOOLKIT_DETAILS[slug] || DEFAULT_MOCK_TOOLKIT(slug);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(mockData);\n        }\n        // Build headers\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Add authorization header if session exists\n        if (session?.access_token) {\n            headers['Authorization'] = `Bearer ${session.access_token}`;\n        }\n        // Call the backend API with timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n        try {\n            const response = await fetch(`${API_URL}/api/composio/toolkits/${slug}/details`, {\n                method: 'GET',\n                headers,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Backend API error:', response.status, errorText);\n                console.log('Backend failed, returning mock data as fallback');\n                const mockData = MOCK_TOOLKIT_DETAILS[slug] || DEFAULT_MOCK_TOOLKIT(slug);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    ...mockData,\n                    _fallback: true,\n                    _error: `Backend API error: ${response.status}`\n                });\n            }\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            if (fetchError.name === 'AbortError') {\n                console.log('Backend API timeout, returning mock data');\n            } else {\n                console.log('Backend API unavailable, returning mock data');\n            }\n            const mockData = MOCK_TOOLKIT_DETAILS[slug] || DEFAULT_MOCK_TOOLKIT(slug);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...mockData,\n                _fallback: true,\n                _error: 'Backend unavailable, using mock data'\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching toolkit details:', error);\n        // Return mock data on any error\n        const { slug } = await params;\n        const mockData = MOCK_TOOLKIT_DETAILS[slug] || DEFAULT_MOCK_TOOLKIT(slug);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...mockData,\n            _fallback: true,\n            _error: 'Failed to fetch toolkit details, using mock data'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/composio/toolkits/[slug]/details/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00c3b743d074a69244eab1d948789d902598d3c17c\":\"createClient\"} */ \n\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://llliyihxaeakffunjjcb.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxsbGl5aWh4YWVha2ZmdW5qamNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4NDMxOTAsImV4cCI6MjA3MjQxOTE5MH0.Cu0nxk1MregjKOTlXNU4S0NXZlTiaRsPEmiK4isI04M\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    createClient\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(createClient, \"00c3b743d074a69244eab1d948789d902598d3c17c\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2F%5Bslug%5D%2Fdetails%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();