{"event": "No API key found for provider: OPENAI", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.791440Z"}
{"event": "No API key found for provider: ANTHROPIC", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.791605Z"}
{"event": "No API key found for provider: GROQ", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.791686Z"}
{"event": "No API key found for provider: OPENROUTER", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.791755Z"}
{"event": "No API key found for provider: XAI", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.791821Z"}
{"event": "No API key found for provider: MORPH", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.791887Z"}
{"event": "No API key found for provider: GEMINI", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 49, "timestamp": "2025-09-19T04:22:16.792039Z"}
{"event": "Missing AWS credentials for Bedrock integration - access_key: False, secret_key: False, region: us-east-1", "level": "warning", "func_name": "setup_api_keys", "filename": "llm.py", "lineno": 68, "timestamp": "2025-09-19T04:22:16.792141Z"}
MAILTRAP_API_TOKEN not found in environment variables
{"event": "Default circuit breakers initialized", "level": "info", "func_name": "create_default_breakers", "filename": "circuit_breaker.py", "lineno": 938, "timestamp": "2025-09-19T04:22:20.013801Z"}
INFO:     Started server process [92808]
INFO:     Waiting for application startup.
{"event": "Starting up FastAPI application with instance ID: single in local mode", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 47, "timestamp": "2025-09-19T04:22:20.172946Z"}
{"event": "Versioning API initialized", "level": "info", "func_name": "initialize", "filename": "api.py", "lineno": 228, "timestamp": "2025-09-19T04:22:20.198307Z"}
{"event": "Initialized agent API with instance ID: single", "level": "info", "func_name": "initialize", "filename": "api.py", "lineno": 202, "timestamp": "2025-09-19T04:22:20.198516Z"}
{"event": "Initialized sandbox API with database connection", "level": "info", "func_name": "initialize", "filename": "api.py", "lineno": 23, "timestamp": "2025-09-19T04:22:20.198665Z"}
{"event": "Initializing Redis connection", "level": "info", "func_name": "initialize_async", "filename": "redis.py", "lineno": 65, "timestamp": "2025-09-19T04:22:20.198784Z"}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "func_name": "initialize", "filename": "redis.py", "lineno": 37, "timestamp": "2025-09-19T04:22:20.206730Z"}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "func_name": "initialize_async", "filename": "redis.py", "lineno": 80, "timestamp": "2025-09-19T04:22:20.207618Z"}
{"event": "Redis connection initialized successfully", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 63, "timestamp": "2025-09-19T04:22:20.207765Z"}
{"event": "Performance monitoring started", "level": "info", "func_name": "start_monitoring", "filename": "performance_monitor.py", "lineno": 249, "timestamp": "2025-09-19T04:22:20.207884Z"}
{"event": "Monitoring and self-healing systems initialized", "level": "info", "func_name": "initialize", "filename": "api.py", "lineno": 411, "timestamp": "2025-09-19T04:22:20.207968Z"}
{"event": "Monitoring system initialized", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 79, "timestamp": "2025-09-19T04:22:20.208044Z"}
{"event": "Self-healing system started with 6 rules", "level": "info", "func_name": "start_monitoring", "filename": "self_healing.py", "lineno": 583, "timestamp": "2025-09-19T04:22:20.310219Z"}
INFO:     Application startup complete.
ERROR:    [Errno 98] error while attempting to bind on address ('0.0.0.0', 8001): address already in use
INFO:     Waiting for application shutdown.
{"event": "Performance monitoring stopped", "level": "info", "func_name": "stop_monitoring", "filename": "performance_monitor.py", "lineno": 268, "timestamp": "2025-09-19T04:22:20.312595Z"}
{"event": "Self-healing system stopped", "level": "info", "func_name": "stop_monitoring", "filename": "self_healing.py", "lineno": 590, "timestamp": "2025-09-19T04:22:20.313062Z"}
{"event": "Monitoring and self-healing systems cleaned up", "level": "info", "func_name": "cleanup", "filename": "api.py", "lineno": 419, "timestamp": "2025-09-19T04:22:20.313291Z"}
{"event": "Cleaning up agent resources", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 87, "timestamp": "2025-09-19T04:22:20.313485Z"}
{"event": "Starting cleanup of agent API resources", "level": "info", "func_name": "cleanup", "filename": "api.py", "lineno": 206, "timestamp": "2025-09-19T04:22:20.313680Z"}
{"event": "Initializing Redis connection", "level": "info", "func_name": "initialize_async", "filename": "redis.py", "lineno": 65, "timestamp": "2025-09-19T04:22:20.313877Z"}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "func_name": "initialize", "filename": "redis.py", "lineno": 37, "timestamp": "2025-09-19T04:22:20.318855Z"}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "func_name": "initialize_async", "filename": "redis.py", "lineno": 80, "timestamp": "2025-09-19T04:22:20.319441Z"}
{"event": "Failed to clean up running agent runs: 'NoneType' object has no attribute 'keys'", "level": "error", "func_name": "cleanup", "filename": "api.py", "lineno": 226, "timestamp": "2025-09-19T04:22:20.319541Z"}
{"event": "Closing Redis connection pool", "level": "info", "func_name": "close", "filename": "redis.py", "lineno": 104, "timestamp": "2025-09-19T04:22:20.319621Z"}
{"event": "Redis connection and pool closed", "level": "info", "func_name": "close", "filename": "redis.py", "lineno": 115, "timestamp": "2025-09-19T04:22:20.319746Z"}
{"event": "Completed cleanup of agent API resources", "level": "info", "func_name": "cleanup", "filename": "api.py", "lineno": 230, "timestamp": "2025-09-19T04:22:20.319815Z"}
{"event": "Closing Redis connection", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 92, "timestamp": "2025-09-19T04:22:20.319885Z"}
{"event": "Redis connection and pool closed", "level": "info", "func_name": "close", "filename": "redis.py", "lineno": 115, "timestamp": "2025-09-19T04:22:20.319952Z"}
{"event": "Redis connection closed successfully", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 94, "timestamp": "2025-09-19T04:22:20.320018Z"}
{"event": "Disconnecting from database", "level": "info", "func_name": "lifespan", "filename": "api.py", "lineno": 99, "timestamp": "2025-09-19T04:22:20.320085Z"}
{"event": "Disconnecting from Supabase database", "level": "info", "func_name": "disconnect", "filename": "supabase.py", "lineno": 68, "timestamp": "2025-09-19T04:22:20.320154Z"}
{"event": "Database disconnected successfully", "level": "info", "func_name": "disconnect", "filename": "supabase.py", "lineno": 79, "timestamp": "2025-09-19T04:22:20.320221Z"}
INFO:     Application shutdown complete.
