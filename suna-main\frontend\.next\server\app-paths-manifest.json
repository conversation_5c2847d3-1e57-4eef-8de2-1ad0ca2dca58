{"/api/edge-flags/route": "app/api/edge-flags/route.js", "/api/composio/toolkits/route": "app/api/composio/toolkits/route.js", "/api/feature-flags/[flag]/route": "app/api/feature-flags/[flag]/route.js", "/api/health/route": "app/api/health/route.js", "/api/triggers/workflows/agents/[agentId]/workflows/route": "app/api/triggers/workflows/agents/[agentId]/workflows/route.js", "/api/composio/toolkits/[slug]/details/route": "app/api/composio/toolkits/[slug]/details/route.js", "/api/composio/profiles/route": "app/api/composio/profiles/route.js", "/(home)/page": "app/(home)/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js"}