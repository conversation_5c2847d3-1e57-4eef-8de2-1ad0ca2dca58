self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00c3b743d074a69244eab1d948789d902598d3c17c\": {\n      \"workers\": {\n        \"app/api/health/route\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/api/composio/toolkits/route\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/api/triggers/workflows/agents/[agentId]/workflows/route\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/api/composio/toolkits/[slug]/details/route\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/api/composio/profiles/route\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/api/health/route\": \"rsc\",\n        \"app/api/composio/toolkits/route\": \"rsc\",\n        \"app/api/triggers/workflows/agents/[agentId]/workflows/route\": \"rsc\",\n        \"app/api/composio/toolkits/[slug]/details/route\": \"rsc\",\n        \"app/api/composio/profiles/route\": \"rsc\"\n      },\n      \"filename\": \"lib/supabase/server.ts\",\n      \"exportedName\": \"createClient\"\n    },\n    \"604dd6d07fdf496a736b1b4821809a57bd54cef081\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Factions%2Fteams.ts%22%2C%5B%7B%22id%22%3A%22604dd6d07fdf496a736b1b4821809a57bd54cef081%22%2C%22exportedName%22%3A%22editTeamName%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%2C%7B%22id%22%3A%2260d245bb1ac0a6d38a9d7f782b5b37e319c5619830%22%2C%22exportedName%22%3A%22editTeamSlug%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%2C%7B%22id%22%3A%2260d31bdb98f361353a0386110345e03560b1db2046%22%2C%22exportedName%22%3A%22createTeam%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/page\": \"action-browser\"\n      },\n      \"filename\": \"lib/actions/teams.ts\",\n      \"exportedName\": \"editTeamName\"\n    },\n    \"60d245bb1ac0a6d38a9d7f782b5b37e319c5619830\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Factions%2Fteams.ts%22%2C%5B%7B%22id%22%3A%22604dd6d07fdf496a736b1b4821809a57bd54cef081%22%2C%22exportedName%22%3A%22editTeamName%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%2C%7B%22id%22%3A%2260d245bb1ac0a6d38a9d7f782b5b37e319c5619830%22%2C%22exportedName%22%3A%22editTeamSlug%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%2C%7B%22id%22%3A%2260d31bdb98f361353a0386110345e03560b1db2046%22%2C%22exportedName%22%3A%22createTeam%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/page\": \"action-browser\"\n      },\n      \"filename\": \"lib/actions/teams.ts\",\n      \"exportedName\": \"editTeamSlug\"\n    },\n    \"60d31bdb98f361353a0386110345e03560b1db2046\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Factions%2Fteams.ts%22%2C%5B%7B%22id%22%3A%22604dd6d07fdf496a736b1b4821809a57bd54cef081%22%2C%22exportedName%22%3A%22editTeamName%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%2C%7B%22id%22%3A%2260d245bb1ac0a6d38a9d7f782b5b37e319c5619830%22%2C%22exportedName%22%3A%22editTeamSlug%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%2C%7B%22id%22%3A%2260d31bdb98f361353a0386110345e03560b1db2046%22%2C%22exportedName%22%3A%22createTeam%22%2C%22filename%22%3A%22lib%2Factions%2Fteams.ts%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/page\": \"action-browser\"\n      },\n      \"filename\": \"lib/actions/teams.ts\",\n      \"exportedName\": \"createTeam\"\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"