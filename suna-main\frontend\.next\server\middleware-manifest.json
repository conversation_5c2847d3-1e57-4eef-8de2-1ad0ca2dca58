{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "I0i8OvsFGlcCADCJq3nX6V/UAKNGGJNI1688rAgfAyw=", "__NEXT_PREVIEW_MODE_ID": "d45044c330bbe752d19fc70c52f643bd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b0a356e9b0770d4c70a9cd6801dd2baaac59e0f1636ae63b20d58255f7ded09a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "706c2dc965f4ef4bf961b8800f6d0e1fe4a78ca2fb1d2c34162768970923f985"}}}, "functions": {}, "sortedMiddleware": ["/"]}