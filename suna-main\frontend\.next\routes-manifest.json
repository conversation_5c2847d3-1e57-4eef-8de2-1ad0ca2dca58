{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": [{"source": "/health", "destination": "http://localhost:8003/health", "regex": "^\\/health(?:\\/)?$", "check": true}, {"source": "/api/health", "destination": "http://localhost:8003/api/health", "regex": "^\\/api\\/health(?:\\/)?$", "check": true}, {"source": "/api/feature-flags/:path*", "destination": "http://localhost:8003/api/feature-flags/:path*", "regex": "^\\/api\\/feature-flags(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}, {"source": "/feature-flags/:path*", "destination": "http://localhost:8003/feature-flags/:path*", "regex": "^\\/feature-flags(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}, {"source": "/composio/:path*", "destination": "http://localhost:8003/api/composio/:path*", "regex": "^\\/composio(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}, {"source": "/api/composio/:path*", "destination": "http://localhost:8003/api/composio/:path*", "regex": "^\\/api\\/composio(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}, {"source": "/api/billing/stripe/webhook", "destination": "http://localhost:8003/api/billing/stripe/webhook", "regex": "^\\/api\\/billing\\/stripe\\/webhook(?:\\/)?$", "check": true}, {"source": "/api/billing/checkout", "destination": "http://localhost:8003/api/billing/checkout", "regex": "^\\/api\\/billing\\/checkout(?:\\/)?$", "check": true}, {"source": "/api/billing/portal", "destination": "http://localhost:8003/api/billing/portal", "regex": "^\\/api\\/billing\\/portal(?:\\/)?$", "check": true}, {"source": "/api/templates/:path*", "destination": "http://localhost:8003/api/templates/:path*", "regex": "^\\/api\\/templates(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}, {"source": "/api/edge-flags", "destination": "http://localhost:8003/api/edge-flags", "regex": "^\\/api\\/edge-flags(?:\\/)?$", "check": true}]}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "X-API-Version", "value": "1.0.0"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^\\/api(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}]}