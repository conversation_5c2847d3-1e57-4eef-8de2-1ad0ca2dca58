#!/usr/bin/env python3
"""
Test the Composio endpoint to verify the OAuth URL fix is applied
"""

import requests
import json

def test_composio_endpoint():
    """Test if the backend is returning the correct OAuth URLs"""
    
    print("\n🔍 Testing Composio Profile Creation Endpoint")
    print("=" * 60)
    
    # First, let's check if the backend is healthy
    health_url = "http://localhost:8003/api/health"
    try:
        response = requests.get(health_url)
        if response.status_code == 200:
            print(f"✅ Backend is healthy: {response.json()}")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return
    
    print("\n📝 To fully test the OAuth URL fix:")
    print("1. Open your browser and go to http://localhost:3000")
    print("2. Log in to your account")
    print("3. Go to Agents or Settings > Composio Integration")
    print("4. Try to connect a new app (e.g., Gmail, Slack)")
    print("5. When the popup opens, check the URL:")
    print("   - ✅ GOOD: app.composio.dev/auth?...")
    print("   - ❌ BAD: backend.composio.dev/api/v3/s/...")
    
    print("\n🔧 The fix has been applied to these files:")
    print("   - /suna-main/backend/composio_integration/connected_account_service.py")
    print("   - Methods: create_connected_account(), get_connected_account(), list_connected_accounts()")
    
    print("\n📊 What the fix does:")
    print("   1. Detects backend short links (backend.composio.dev/api/v3/s/...)")
    print("   2. Replaces them with proper OAuth URLs (app.composio.dev/auth)")
    print("   3. Includes necessary OAuth parameters")
    
    print("\n💡 If you're still seeing the old URLs:")
    print("   1. Make sure the backend was restarted after the fix")
    print("   2. Clear your browser cache")
    print("   3. Try a hard refresh (Ctrl+Shift+R or Cmd+Shift+R)")
    
    # Check if our fix is in the code
    print("\n🔍 Verifying fix is in the code...")
    try:
        with open('/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/connected_account_service.py', 'r') as f:
            content = f.read()
            if 'backend.composio.dev/api/v3/s/' in content and 'app.composio.dev/auth' in content:
                print("✅ Fix code is present in connected_account_service.py")
                
                # Count occurrences
                fix_count = content.count('backend.composio.dev/api/v3/s/')
                oauth_count = content.count('app.composio.dev/auth')
                print(f"   - Found {fix_count} checks for backend short links")
                print(f"   - Found {oauth_count} OAuth URL constructions")
            else:
                print("❌ Fix code not found in the file!")
    except Exception as e:
        print(f"❌ Could not verify fix: {e}")

if __name__ == "__main__":
    test_composio_endpoint()