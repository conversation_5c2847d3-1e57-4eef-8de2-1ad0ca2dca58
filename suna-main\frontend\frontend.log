
> Kortix@0.1.0 dev
> next dev

 ⚠ Warning: Next.js inferred your workspace root, but it may not be correct.
 We detected multiple lockfiles and selected the directory of /mnt/c/Users/<USER>/package-lock.json as the root directory.
 To silence this warning, set `outputFileTracingRoot` in your Next.js config, or consider removing one of the lockfiles if it's not needed.
   See https://nextjs.org/docs/app/api-reference/config/next-config-js/output#caveats for more information.
 Detected additional lockfiles: 
   * /mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/package-lock.json
   * /mnt/c/Users/<USER>/Downloads/olari/suna-main/package-lock.json

   ▲ Next.js 15.5.3
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local
   - Experiments (use with caution):
     · optimizePackageImports

 ✓ Starting...
Warning: Reverting webpack devtool to 'false'.
Changing the webpack devtool in development mode will cause severe performance regressions.
Read more: https://nextjs.org/docs/messages/improper-devtool
 ✓ Ready in 18.3s
 ✓ Compiled /middleware in 146ms (114 modules)
 ○ Compiling /dashboard ...
 ✓ Compiled /dashboard in 6.5s (3805 modules)
 GET /api/edge-flags 200 in 8352ms
 GET /dashboard 200 in 11655ms
 GET /api/edge-flags 200 in 207ms
 ○ Compiling /api/health ...
 ✓ Compiled /api/health in 1482ms (3888 modules)
 GET /api/health 200 in 2078ms
 GET /api/edge-flags 200 in 1659ms
 GET /api/health 200 in 215ms
 GET /api/edge-flags 200 in 156ms
 ○ Compiling /api/composio/toolkits ...
 ✓ Compiled /api/composio/toolkits in 1136ms (3890 modules)
 GET /api/composio/toolkits 200 in 2477ms
 ○ Compiling /api/composio/toolkits/[slug]/details ...
 ✓ Compiled /api/composio/toolkits/[slug]/details in 757ms (3892 modules)
 GET /api/composio/toolkits/googlesheets/details 200 in 5875ms
 ○ Compiling /api/composio/profiles ...
 ✓ Compiled /api/composio/profiles in 636ms (3894 modules)
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'googlesheets', profile_name: 'Googlesheets Profile' }
 POST /api/composio/profiles 500 in 3431ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'googlesheets', profile_name: 'Googlesheets Profile' }
 POST /api/composio/profiles 500 in 2577ms
 GET /api/edge-flags 200 in 229ms
 ○ Compiling /api/triggers/workflows/agents/[agentId]/workflows ...
 ✓ Compiled /api/triggers/workflows/agents/[agentId]/workflows in 920ms (3896 modules)
 GET /api/health 200 in 164ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 6609ms
 GET /api/health 200 in 133ms
 GET /api/health 200 in 125ms
 GET /api/health 200 in 126ms
 ○ Compiling /api/feature-flags/[flag] ...
 ✓ Compiled /api/feature-flags/[flag] in 1364ms (3890 modules)
 GET /api/edge-flags 200 in 1667ms
 ✓ Compiled /api/composio/profiles in 412ms (2004 modules)
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'googlesheets', profile_name: 'Googlesheets Profile' }
 POST /api/composio/profiles 500 in 3126ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 8097ms
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'googlesheets', profile_name: 'Googlesheets Profile' }
 POST /api/composio/profiles 500 in 2405ms
 GET /api/health 200 in 162ms
 GET /api/health 200 in 160ms
 GET /api/health 200 in 170ms
 GET /api/health 200 in 99ms
 GET /api/health 200 in 153ms
 GET /api/health 200 in 130ms
 GET /api/health 200 in 143ms
 GET /api/edge-flags 200 in 168ms
 GET /api/edge-flags 200 in 305ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 412ms
 ○ Compiling /api/triggers/workflows/agents/[agentId]/workflows ...
 ✓ Compiled /api/triggers/workflows/agents/[agentId]/workflows in 720ms (2004 modules)
 ✓ Compiled in 0ms (2006 modules)
 ✓ Compiled in 0ms (2006 modules)
 ✓ Compiled in 0ms (2006 modules)
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 3754ms
 GET /api/composio/toolkits 200 in 4094ms
 GET /api/health 200 in 1752ms
 GET /dashboard 200 in 3177ms
 GET /dashboard 200 in 307ms
 GET /api/health 200 in 176ms
 GET /api/edge-flags 200 in 180ms
 GET /api/composio/toolkits 200 in 644ms
 ✓ Compiled /api/composio/toolkits/[slug]/details in 478ms (2008 modules)
 GET /api/composio/toolkits/gmail/details 200 in 1004ms
 ○ Compiling /api/composio/profiles ...
 ✓ Compiled /api/composio/profiles in 597ms (2010 modules)
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' }
 POST /api/composio/profiles 500 in 3325ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' }
 POST /api/composio/profiles 500 in 2309ms
 GET /api/health 200 in 195ms
 GET /api/edge-flags 200 in 176ms
 GET /api/health 200 in 163ms
 GET /api/edge-flags 200 in 168ms
 GET /api/health 200 in 118ms
 GET /api/health 200 in 141ms
 GET /api/health 200 in 137ms
 GET /api/health 200 in 131ms
 GET /api/health 200 in 138ms
 GET /api/health 200 in 136ms
 GET /api/health 200 in 145ms
 GET /api/edge-flags 200 in 299ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 401ms
 GET /api/health 200 in 393ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 444ms
GET request: Using authorization token from session
 GET /api/composio/toolkits 200 in 2144ms
 GET /api/composio/profiles 200 in 2414ms
 GET /api/composio/toolkits?cursor=Mi0xMDA%3D 200 in 557ms
 GET /api/composio/toolkits?cursor=My0xMDA%3D 200 in 617ms
 GET /api/composio/toolkits/github/details 200 in 435ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'github', profile_name: 'GitHub Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'github', profile_name: 'GitHub Profile' }
 POST /api/composio/profiles 500 in 3412ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'github', profile_name: 'GitHub Profile' },
  session: 'present',
  access_token: 'present'
}
 GET /api/composio/toolkits/keap/details 200 in 1079ms
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: `{'message': 'new row violates row-level security policy for table "user_mcp_credential_profiles"', 'code': '42501', 'hint': None, 'details': None}`
}
Request body was: { toolkit_slug: 'github', profile_name: 'GitHub Profile' }
 POST /api/composio/profiles 500 in 3482ms
 GET /api/edge-flags 200 in 172ms
 GET /api/edge-flags 200 in 176ms
 GET /api/health 200 in 145ms
 GET /api/health 200 in 250ms
 GET /api/health 200 in 149ms
 GET /api/health 200 in 153ms
 GET /api/edge-flags 200 in 269ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 377ms
 GET /api/health 200 in 376ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 454ms
 GET /dashboard 200 in 659ms
 GET /api/health 200 in 192ms
 GET /api/edge-flags 200 in 161ms
 GET /api/composio/toolkits 200 in 832ms
 GET /api/composio/toolkits/composio/details 200 in 332ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'composio', profile_name: 'Composio Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: "Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}"
}
Request body was: { toolkit_slug: 'composio', profile_name: 'Composio Profile' }
 POST /api/composio/profiles 500 in 817ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8002/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'composio', profile_name: 'Composio Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: {
  detail: "Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}"
}
Request body was: { toolkit_slug: 'composio', profile_name: 'Composio Profile' }
 POST /api/composio/profiles 500 in 731ms
 GET /api/health 200 in 1115ms
 GET /api/health 200 in 179ms
 GET /api/edge-flags 200 in 170ms
 GET /api/edge-flags 200 in 188ms
 GET /api/health 200 in 148ms
 GET /api/edge-flags 200 in 161ms
   Reload env: .env.local
 ✓ Compiled in 3.5s (4002 modules)
 GET /api/health 200 in 847ms
 GET /dashboard 200 in 408ms
 ✓ Compiled /api/composio/toolkits/[slug]/details in 436ms (2002 modules)
 GET /api/composio/toolkits/perplexityai/details 200 in 1147ms
 GET /api/composio/toolkits/supabase/details 200 in 426ms
 ✓ Compiled /api/composio/profiles in 459ms (2004 modules)
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'supabase', profile_name: 'Supabase Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: { detail: '500: Profile created but not found' }
Request body was: { toolkit_slug: 'supabase', profile_name: 'Supabase Profile' }
 POST /api/composio/profiles 500 in 3505ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'supabase', profile_name: 'Supabase Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: { detail: '500: Profile created but not found' }
Request body was: { toolkit_slug: 'supabase', profile_name: 'Supabase Profile' }
 POST /api/composio/profiles 500 in 2756ms
[?25h
