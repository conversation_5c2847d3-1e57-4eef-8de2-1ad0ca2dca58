# Integration Rules & Guidelines

## 🚨 CRITICAL RULE #1: DO NOT RECREATE EXISTING CODE
**This is the most important rule. We use the ACTUAL code from the three repositories.**

### ❌ WRONG Approach:
```python
# DON'T DO THIS - Creating new functionality
class MyOwnResearchAgent:
    def research(self, query):
        # Custom implementation
        return "some result"
```

### ✅ CORRECT Approach:
```python
# DO THIS - Import and use existing code
from src.agent.deep_researcher_agent import DeepResearcherAgent
agent = DeepResearcherAgent()  # Use as-is
```

## 📁 Repository Boundaries

### Protected Directories (DO NOT MODIFY)
```
/autogen-main/          ← NO CHANGES
/DeepResearchAgent-main/ ← NO CHANGES  
/llama_index-main/      ← NO CHANGES
```

### Modifiable Directories (OUR CODE)
```
/olari/backend/         ← Integration code only
/olari/config/          ← Configuration files
/olari/tests/           ← Integration tests
```

## 🔌 Integration Patterns

### Pattern 1: Wrapper Classes
```python
# Wrap existing agents for compatibility
class ExistingAgentWrapper(ConversableAgent):
    def __init__(self, original_agent):
        self.original = original_agent  # Keep reference
    
    def generate_reply(self, messages, sender, config):
        # Call ORIGINAL agent's methods
        return self.original.existing_method(messages[-1])
```

### Pattern 2: Direct Import & Use
```python
# Import directly from repositories
import sys
sys.path.append('/path/to/repository')
from existing.module import ExistingClass

# Use without modification
instance = ExistingClass()
result = instance.original_method()
```

### Pattern 3: Configuration Injection
```python
# Pass our config to existing classes
from autogen import GroupChat

# Use their class with our config
group_chat = GroupChat(
    agents=our_agent_list,  # Our combination
    max_round=20,           # Our settings
    speaker_selection_method="auto"
)
```

## 🎯 Agent Responsibilities

### AutoGen Agents
- **Purpose**: Orchestration and conversation management
- **Use**: GroupChat, GroupChatManager, speaker selection
- **Don't**: Recreate conversation logic

### DeepResearchAgent
- **Purpose**: External research, web browsing, data gathering
- **Use**: planning_agent, deep_researcher_agent, browser_use_agent
- **Don't**: Build new search functionality

### LlamaIndex
- **Purpose**: Internal RAG, vector search, knowledge retrieval
- **Use**: VectorStoreIndex, QueryEngine, SubQuestionQueryEngine
- **Don't**: Create new embedding systems

### Composio
- **Purpose**: External tool integration (250+ apps)
- **Use**: API connections, OAuth handling, action execution
- **Don't**: Build API integrations from scratch

## 💬 Message Protocol

### Standard Message Format
```python
{
    "role": "agent_name",
    "name": "AgentIdentifier",
    "content": "Message content",
    "metadata": {
        "timestamp": "2024-01-20T10:00:00Z",
        "agent_type": "research|analysis|synthesis|tool",
        "confidence": 0.95,
        "sources": ["source1", "source2"],
        "tokens_used": 150,
        "processing_time": 2.3
    }
}
```

### Inter-Agent Communication Rules
1. **Observe Full History**: Every agent sees all previous messages
2. **Build on Others**: Reference and extend previous agent outputs
3. **Self-Correct**: Agents can correct their own or others' errors
4. **Cite Sources**: Always indicate where information comes from
5. **Maintain Context**: Don't repeat what's already been said

## 🔄 Conversation Flow Rules

### Speaker Selection
```python
# The GroupChatManager's LLM decides who speaks next
# Based on:
# 1. Query requirements
# 2. Agent capabilities  
# 3. Conversation progress
# 4. Previous speaker patterns

# NO predetermined sequences like:
# ❌ Agent A → Agent B → Agent C (fixed order)
# ✅ Dynamic selection based on need
```

### Conversation Termination
```python
# Conversation ends when:
1. User query is fully answered
2. Max rounds reached (configurable)
3. All agents agree no more input needed
4. Error threshold exceeded
5. User interrupts
```

## 🛡️ Error Handling Protocol

### Agent Failure Handling
```python
try:
    response = agent.process(query)
except AgentError as e:
    # 1. Log error with full context
    logger.error(f"Agent {agent.name} failed: {e}")
    
    # 2. Try fallback agent
    fallback = get_fallback_agent(agent.type)
    response = fallback.process(query)
    
    # 3. If all fail, graceful degradation
    if not response:
        response = "Unable to process. Here's what we know..."
```

### Response Validation
```python
def validate_agent_response(response):
    # Check response quality
    assert len(response) > 10, "Response too short"
    assert response != prev_response, "Duplicate response"
    assert not contains_errors(response), "Response has errors"
    assert has_sources(response), "No sources cited"
    return response
```

## 🚀 Performance Guidelines

### Token Optimization
```python
# Monitor token usage per agent
TOKEN_LIMITS = {
    "AutoGenAssistant": 2000,
    "DeepResearcher": 3000,
    "LlamaIndexRAG": 1500,
    "Synthesizer": 2500
}

def check_token_usage(agent, tokens):
    if tokens > TOKEN_LIMITS[agent]:
        logger.warning(f"{agent} exceeded token limit")
        return truncate_context(tokens)
```

### Caching Strategy
```python
# Cache at multiple levels
CACHE_LAYERS = {
    "response_cache": 3600,    # 1 hour for full responses
    "embedding_cache": 86400,  # 24 hours for embeddings
    "search_cache": 1800,      # 30 mins for search results
    "tool_cache": 600         # 10 mins for tool calls
}
```

## 🔐 Security Rules

### API Key Management
```python
# Never hardcode keys
# ❌ WRONG
api_key = "sk-abc123..."  

# ✅ CORRECT
api_key = os.environ.get("OPENAI_API_KEY")
```

### Data Access Control
```python
AGENT_PERMISSIONS = {
    "UserProxy": ["read_query", "write_response"],
    "DataAnalyst": ["read_internal", "query_vectors"],
    "ExternalResearcher": ["web_search", "public_apis"],
    "ToolAgent": ["approved_tools_only"],
    "Synthesizer": ["read_all", "write_final"]
}

def check_permission(agent, action):
    return action in AGENT_PERMISSIONS.get(agent, [])
```

## 🧪 Testing Requirements

### Unit Test Coverage
```python
# Minimum coverage requirements
COVERAGE_REQUIREMENTS = {
    "agent_wrappers": 90,     # Critical integration points
    "orchestration": 85,      # Core logic
    "utilities": 80,          # Helper functions
    "config": 70              # Configuration handling
}
```

## 📈 Monitoring Rules

### Required Metrics
```python
REQUIRED_METRICS = [
    "response_time_ms",
    "tokens_per_query",
    "agents_per_conversation",
    "cache_hit_rate",
    "error_rate"
]
```

## 🚫 Anti-Patterns to Avoid

### ❌ Don't Do These:
1. **Modify original repositories** - They're read-only
2. **Copy-paste repository code** - Import instead
3. **Hardcode configurations** - Use config files
4. **Skip error handling** - Always handle failures
5. **Create tight coupling** - Keep agents independent

### ✅ Always Do These:
1. **Import existing code** - Use what's built
2. **Wrap for compatibility** - Adapt interfaces
3. **Configure externally** - Environment/files
4. **Handle errors gracefully** - Fallback plans
5. **Maintain loose coupling** - Replaceable agents

## 🎯 Summary

**Remember**: We're building an integration layer, not recreating existing systems. Every line of code should either:
1. Import from existing repositories
2. Configure existing components
3. Connect different frameworks
4. Handle integration concerns

**Never**: Recreate functionality that already exists in the three repositories.

---

*These rules are mandatory for all development on this project.*