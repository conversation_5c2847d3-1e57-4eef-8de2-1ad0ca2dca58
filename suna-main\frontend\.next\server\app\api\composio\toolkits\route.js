/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/composio/toolkits/route";
exports.ids = ["app/api/composio/toolkits/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_composio_toolkits_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/composio/toolkits/route.ts */ \"(rsc)/./src/app/api/composio/toolkits/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/composio/toolkits/route\",\n        pathname: \"/api/composio/toolkits\",\n        filename: \"route\",\n        bundlePath: \"app/api/composio/toolkits/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/api/composio/toolkits/route.ts\",\n    nextConfigOutput,\n    userland: _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_composio_toolkits_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/composio/toolkits/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00c3b743d074a69244eab1d948789d902598d3c17c\": () => (/* reexport safe */ _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__.createClient)\n/* harmony export */ });\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/lib/supabase/server.ts */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyJTJGbW50JTJGYyUyRlVzZXJzJTJGSWJyYWhpbSUyRkRvd25sb2FkcyUyRm9sYXJpJTJGc3VuYS1tYWluJTJGZnJvbnRlbmQlMkZzcmMlMkZsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiUyQyU1QiU3QiUyMmlkJTIyJTNBJTIyMDBjM2I3NDNkMDc0YTY5MjQ0ZWFiMWQ5NDg3ODlkOTAyNTk4ZDNjMTdjJTIyJTJDJTIyZXhwb3J0ZWROYW1lJTIyJTNBJTIyY3JlYXRlQ2xpZW50JTIyJTJDJTIyZmlsZW5hbWUlMjIlM0ElMjJsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiU3RCU1RCU1RCU1RCZfX2NsaWVudF9pbXBvcnRlZF9fPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ2lLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBjcmVhdGVDbGllbnQgYXMgXCIwMGMzYjc0M2QwNzRhNjkyNDRlYWIxZDk0ODc4OWQ5MDI1OThkM2MxN2NcIiB9IGZyb20gXCIvbW50L2MvVXNlcnMvSWJyYWhpbS9Eb3dubG9hZHMvb2xhcmkvc3VuYS1tYWluL2Zyb250ZW5kL3NyYy9saWIvc3VwYWJhc2Uvc2VydmVyLnRzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/composio/toolkits/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/composio/toolkits/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nconst API_URL = \"http://localhost:8003\" || 0;\n// Mock data for Composio toolkits\nconst MOCK_TOOLKITS = {\n    success: true,\n    toolkits: [\n        {\n            id: 'gmail',\n            name: 'Gmail',\n            slug: 'gmail',\n            category: 'communication',\n            description: 'Send, receive, and manage emails with Gmail',\n            icon: 'https://composio.dev/icons/gmail.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 15,\n            popularity: 95,\n            tags: [\n                'email',\n                'google',\n                'messaging'\n            ],\n            features: [\n                'send_email',\n                'receive_email',\n                'manage_labels',\n                'search_emails'\n            ]\n        },\n        {\n            id: 'slack',\n            name: 'Slack',\n            slug: 'slack',\n            category: 'communication',\n            description: 'Connect with Slack to send messages, manage channels, and interact with workspaces',\n            icon: 'https://composio.dev/icons/slack.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 20,\n            popularity: 90,\n            tags: [\n                'messaging',\n                'team',\n                'collaboration'\n            ],\n            features: [\n                'send_message',\n                'create_channel',\n                'upload_file',\n                'manage_users'\n            ]\n        },\n        {\n            id: 'github',\n            name: 'GitHub',\n            slug: 'github',\n            category: 'developer_tools',\n            description: 'Manage repositories, issues, pull requests, and more on GitHub',\n            icon: 'https://composio.dev/icons/github.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2',\n                'api_key'\n            ],\n            tools_count: 35,\n            popularity: 98,\n            tags: [\n                'git',\n                'version_control',\n                'development',\n                'code'\n            ],\n            features: [\n                'create_issue',\n                'create_pr',\n                'manage_repos',\n                'code_review'\n            ]\n        },\n        {\n            id: 'notion',\n            name: 'Notion',\n            slug: 'notion',\n            category: 'productivity',\n            description: 'Create, update, and manage pages and databases in Notion',\n            icon: 'https://composio.dev/icons/notion.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 18,\n            popularity: 85,\n            tags: [\n                'notes',\n                'documentation',\n                'knowledge_base'\n            ],\n            features: [\n                'create_page',\n                'update_database',\n                'search_content',\n                'manage_workspace'\n            ]\n        },\n        {\n            id: 'linear',\n            name: 'Linear',\n            slug: 'linear',\n            category: 'project_management',\n            description: 'Manage issues, projects, and workflows in Linear',\n            icon: 'https://composio.dev/icons/linear.svg',\n            status: 'active',\n            supported_auth: [\n                'api_key'\n            ],\n            tools_count: 22,\n            popularity: 75,\n            tags: [\n                'issues',\n                'project_management',\n                'agile'\n            ],\n            features: [\n                'create_issue',\n                'update_project',\n                'manage_cycles',\n                'track_progress'\n            ]\n        },\n        {\n            id: 'discord',\n            name: 'Discord',\n            slug: 'discord',\n            category: 'communication',\n            description: 'Send messages, manage servers, and interact with Discord communities',\n            icon: 'https://composio.dev/icons/discord.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2',\n                'bot_token'\n            ],\n            tools_count: 25,\n            popularity: 80,\n            tags: [\n                'messaging',\n                'community',\n                'gaming'\n            ],\n            features: [\n                'send_message',\n                'manage_server',\n                'create_channel',\n                'moderate_users'\n            ]\n        },\n        {\n            id: 'twitter',\n            name: 'Twitter/X',\n            slug: 'twitter',\n            category: 'social_media',\n            description: 'Post tweets, manage timeline, and interact with Twitter/X platform',\n            icon: 'https://composio.dev/icons/twitter.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 12,\n            popularity: 88,\n            tags: [\n                'social',\n                'microblogging',\n                'news'\n            ],\n            features: [\n                'post_tweet',\n                'like_tweet',\n                'follow_user',\n                'search_tweets'\n            ]\n        },\n        {\n            id: 'google_calendar',\n            name: 'Google Calendar',\n            slug: 'google-calendar',\n            category: 'productivity',\n            description: 'Manage events, schedule meetings, and organize your calendar',\n            icon: 'https://composio.dev/icons/google-calendar.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 10,\n            popularity: 92,\n            tags: [\n                'calendar',\n                'scheduling',\n                'google',\n                'events'\n            ],\n            features: [\n                'create_event',\n                'update_event',\n                'get_availability',\n                'send_invites'\n            ]\n        },\n        {\n            id: 'jira',\n            name: 'Jira',\n            slug: 'jira',\n            category: 'project_management',\n            description: 'Track issues, manage projects, and collaborate with Jira',\n            icon: 'https://composio.dev/icons/jira.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2',\n                'api_key'\n            ],\n            tools_count: 30,\n            popularity: 82,\n            tags: [\n                'issues',\n                'agile',\n                'project_management',\n                'atlassian'\n            ],\n            features: [\n                'create_issue',\n                'update_sprint',\n                'manage_board',\n                'generate_reports'\n            ]\n        },\n        {\n            id: 'figma',\n            name: 'Figma',\n            slug: 'figma',\n            category: 'design',\n            description: 'Access and manage design files, components, and teams in Figma',\n            icon: 'https://composio.dev/icons/figma.svg',\n            status: 'active',\n            supported_auth: [\n                'oauth2'\n            ],\n            tools_count: 8,\n            popularity: 78,\n            tags: [\n                'design',\n                'ui',\n                'collaboration',\n                'prototyping'\n            ],\n            features: [\n                'get_file',\n                'export_assets',\n                'manage_team',\n                'create_comment'\n            ]\n        }\n    ],\n    total: 10,\n    has_more: false,\n    cursor: null\n};\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Get the current session for authentication (optional for this endpoint)\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        // Extract query parameters for filtering\n        const category = searchParams.get('category');\n        const search = searchParams.get('search');\n        const status = searchParams.get('status');\n        const cursor = searchParams.get('cursor');\n        // If API_URL is not configured or empty, return mock data\n        if (!API_URL || API_URL.trim() === '') {\n            console.log('Using mock data for Composio toolkits (API_URL not configured)');\n            // Filter mock data based on query parameters\n            let filteredToolkits = [\n                ...MOCK_TOOLKITS.toolkits\n            ];\n            // Filter by category\n            if (category && category !== 'all') {\n                filteredToolkits = filteredToolkits.filter((toolkit)=>toolkit.category === category);\n            }\n            // Filter by search term\n            if (search) {\n                const searchLower = search.toLowerCase();\n                filteredToolkits = filteredToolkits.filter((toolkit)=>toolkit.name.toLowerCase().includes(searchLower) || toolkit.description.toLowerCase().includes(searchLower) || toolkit.tags.some((tag)=>tag.toLowerCase().includes(searchLower)));\n            }\n            // Filter by status\n            if (status) {\n                filteredToolkits = filteredToolkits.filter((toolkit)=>toolkit.status === status);\n            }\n            // Return filtered mock data\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                toolkits: filteredToolkits,\n                total: filteredToolkits.length,\n                has_more: false,\n                cursor: null\n            });\n        }\n        // Validate URL format before attempting to fetch\n        try {\n            new URL(`${API_URL}/api/composio/toolkits`);\n        } catch (urlError) {\n            console.log('Invalid backend URL format, returning mock data');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(MOCK_TOOLKITS);\n        }\n        // Build query parameters for real backend call\n        const queryParams = new URLSearchParams();\n        if (category) queryParams.append('category', category);\n        if (search) queryParams.append('search', search);\n        if (status) queryParams.append('status', status);\n        if (cursor) queryParams.append('cursor', cursor);\n        // Build headers\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Add authorization header if session exists\n        if (session?.access_token) {\n            headers['Authorization'] = `Bearer ${session.access_token}`;\n        }\n        // Call the backend API with timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n        const backendUrl = `${API_URL}/api/composio/toolkits${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\n        const response = await fetch(backendUrl, {\n            method: 'GET',\n            headers,\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Backend API error:', response.status, errorText);\n            // Try to parse error response\n            let errorMessage = `Backend API error: ${response.status}`;\n            try {\n                const errorData = JSON.parse(errorText);\n                if (errorData.error) {\n                    errorMessage = errorData.error;\n                }\n            } catch  {\n            // Use default error message if parsing fails\n            }\n            // Return mock data as fallback\n            console.log('Backend API failed, returning mock data as fallback');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...MOCK_TOOLKITS,\n                _fallback: true,\n                _error: errorMessage\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        // Handle timeout error specifically\n        if (error.name === 'AbortError') {\n            console.log('Backend API timeout, returning mock data');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...MOCK_TOOLKITS,\n                _fallback: true,\n                _error: 'Backend API timeout, using mock data'\n            });\n        }\n        console.error('Error fetching toolkits:', error);\n        // Return mock data as fallback on any error\n        console.log('Error occurred, returning mock data as fallback');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...MOCK_TOOLKITS,\n            _fallback: true,\n            _error: 'Failed to fetch from backend, using mock data'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jb21wb3Npby90b29sa2l0cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFDSDtBQUVyRCxNQUFNRSxVQUFVQyx1QkFBbUMsSUFBSSxDQUFFO0FBRXpELGtDQUFrQztBQUNsQyxNQUFNRyxnQkFBZ0I7SUFDcEJDLFNBQVM7SUFDVEMsVUFBVTtRQUNSO1lBQ0VDLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsZ0JBQWdCO2dCQUFDO2FBQVM7WUFDMUJDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxNQUFNO2dCQUFDO2dCQUFTO2dCQUFVO2FBQVk7WUFDdENDLFVBQVU7Z0JBQUM7Z0JBQWM7Z0JBQWlCO2dCQUFpQjthQUFnQjtRQUM3RTtRQUNBO1lBQ0VYLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsZ0JBQWdCO2dCQUFDO2FBQVM7WUFDMUJDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxNQUFNO2dCQUFDO2dCQUFhO2dCQUFRO2FBQWdCO1lBQzVDQyxVQUFVO2dCQUFDO2dCQUFnQjtnQkFBa0I7Z0JBQWU7YUFBZTtRQUM3RTtRQUNBO1lBQ0VYLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsZ0JBQWdCO2dCQUFDO2dCQUFVO2FBQVU7WUFDckNDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxNQUFNO2dCQUFDO2dCQUFPO2dCQUFtQjtnQkFBZTthQUFPO1lBQ3ZEQyxVQUFVO2dCQUFDO2dCQUFnQjtnQkFBYTtnQkFBZ0I7YUFBYztRQUN4RTtRQUNBO1lBQ0VYLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsZ0JBQWdCO2dCQUFDO2FBQVM7WUFDMUJDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxNQUFNO2dCQUFDO2dCQUFTO2dCQUFpQjthQUFpQjtZQUNsREMsVUFBVTtnQkFBQztnQkFBZTtnQkFBbUI7Z0JBQWtCO2FBQW1CO1FBQ3BGO1FBQ0E7WUFDRVgsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxnQkFBZ0I7Z0JBQUM7YUFBVTtZQUMzQkMsYUFBYTtZQUNiQyxZQUFZO1lBQ1pDLE1BQU07Z0JBQUM7Z0JBQVU7Z0JBQXNCO2FBQVE7WUFDL0NDLFVBQVU7Z0JBQUM7Z0JBQWdCO2dCQUFrQjtnQkFBaUI7YUFBaUI7UUFDakY7UUFDQTtZQUNFWCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLGdCQUFnQjtnQkFBQztnQkFBVTthQUFZO1lBQ3ZDQyxhQUFhO1lBQ2JDLFlBQVk7WUFDWkMsTUFBTTtnQkFBQztnQkFBYTtnQkFBYTthQUFTO1lBQzFDQyxVQUFVO2dCQUFDO2dCQUFnQjtnQkFBaUI7Z0JBQWtCO2FBQWlCO1FBQ2pGO1FBQ0E7WUFDRVgsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxnQkFBZ0I7Z0JBQUM7YUFBUztZQUMxQkMsYUFBYTtZQUNiQyxZQUFZO1lBQ1pDLE1BQU07Z0JBQUM7Z0JBQVU7Z0JBQWlCO2FBQU87WUFDekNDLFVBQVU7Z0JBQUM7Z0JBQWM7Z0JBQWM7Z0JBQWU7YUFBZ0I7UUFDeEU7UUFDQTtZQUNFWCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLGdCQUFnQjtnQkFBQzthQUFTO1lBQzFCQyxhQUFhO1lBQ2JDLFlBQVk7WUFDWkMsTUFBTTtnQkFBQztnQkFBWTtnQkFBYztnQkFBVTthQUFTO1lBQ3BEQyxVQUFVO2dCQUFDO2dCQUFnQjtnQkFBZ0I7Z0JBQW9CO2FBQWU7UUFDaEY7UUFDQTtZQUNFWCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLGdCQUFnQjtnQkFBQztnQkFBVTthQUFVO1lBQ3JDQyxhQUFhO1lBQ2JDLFlBQVk7WUFDWkMsTUFBTTtnQkFBQztnQkFBVTtnQkFBUztnQkFBc0I7YUFBWTtZQUM1REMsVUFBVTtnQkFBQztnQkFBZ0I7Z0JBQWlCO2dCQUFnQjthQUFtQjtRQUNqRjtRQUNBO1lBQ0VYLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsZ0JBQWdCO2dCQUFDO2FBQVM7WUFDMUJDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxNQUFNO2dCQUFDO2dCQUFVO2dCQUFNO2dCQUFpQjthQUFjO1lBQ3REQyxVQUFVO2dCQUFDO2dCQUFZO2dCQUFpQjtnQkFBZTthQUFpQjtRQUMxRTtLQUNEO0lBQ0RDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxRQUFRO0FBQ1Y7QUFFTyxlQUFlQyxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRRyxHQUFHO1FBRTVDLDBFQUEwRTtRQUMxRSxNQUFNQyxXQUFXLE1BQU01QixrRUFBWUE7UUFDbkMsTUFBTSxFQUFFNkIsTUFBTSxFQUFFQyxPQUFPLEVBQUUsRUFBRSxHQUFHLE1BQU1GLFNBQVNHLElBQUksQ0FBQ0MsVUFBVTtRQUU1RCx5Q0FBeUM7UUFDekMsTUFBTXJCLFdBQVdjLGFBQWFRLEdBQUcsQ0FBQztRQUNsQyxNQUFNQyxTQUFTVCxhQUFhUSxHQUFHLENBQUM7UUFDaEMsTUFBTW5CLFNBQVNXLGFBQWFRLEdBQUcsQ0FBQztRQUNoQyxNQUFNWCxTQUFTRyxhQUFhUSxHQUFHLENBQUM7UUFFaEMsMERBQTBEO1FBQzFELElBQUksQ0FBQ2hDLFdBQVdBLFFBQVFrQyxJQUFJLE9BQU8sSUFBSTtZQUNyQ0MsUUFBUUMsR0FBRyxDQUFDO1lBRVosNkNBQTZDO1lBQzdDLElBQUlDLG1CQUFtQjttQkFBSWpDLGNBQWNFLFFBQVE7YUFBQztZQUVsRCxxQkFBcUI7WUFDckIsSUFBSUksWUFBWUEsYUFBYSxPQUFPO2dCQUNsQzJCLG1CQUFtQkEsaUJBQWlCQyxNQUFNLENBQ3hDQyxDQUFBQSxVQUFXQSxRQUFRN0IsUUFBUSxLQUFLQTtZQUVwQztZQUVBLHdCQUF3QjtZQUN4QixJQUFJdUIsUUFBUTtnQkFDVixNQUFNTyxjQUFjUCxPQUFPUSxXQUFXO2dCQUN0Q0osbUJBQW1CQSxpQkFBaUJDLE1BQU0sQ0FDeENDLENBQUFBLFVBQ0VBLFFBQVEvQixJQUFJLENBQUNpQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsZ0JBQ3BDRCxRQUFRNUIsV0FBVyxDQUFDOEIsV0FBVyxHQUFHQyxRQUFRLENBQUNGLGdCQUMzQ0QsUUFBUXRCLElBQUksQ0FBQzBCLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUgsV0FBVyxHQUFHQyxRQUFRLENBQUNGO1lBRTFEO1lBRUEsbUJBQW1CO1lBQ25CLElBQUkzQixRQUFRO2dCQUNWd0IsbUJBQW1CQSxpQkFBaUJDLE1BQU0sQ0FDeENDLENBQUFBLFVBQVdBLFFBQVExQixNQUFNLEtBQUtBO1lBRWxDO1lBRUEsNEJBQTRCO1lBQzVCLE9BQU9mLHFEQUFZQSxDQUFDK0MsSUFBSSxDQUFDO2dCQUN2QnhDLFNBQVM7Z0JBQ1RDLFVBQVUrQjtnQkFDVmxCLE9BQU9rQixpQkFBaUJTLE1BQU07Z0JBQzlCMUIsVUFBVTtnQkFDVkMsUUFBUTtZQUNWO1FBQ0Y7UUFFQSxpREFBaUQ7UUFDakQsSUFBSTtZQUNGLElBQUlJLElBQUksR0FBR3pCLFFBQVEsc0JBQXNCLENBQUM7UUFDNUMsRUFBRSxPQUFPK0MsVUFBVTtZQUNqQlosUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBT3RDLHFEQUFZQSxDQUFDK0MsSUFBSSxDQUFDekM7UUFDM0I7UUFFQSwrQ0FBK0M7UUFDL0MsTUFBTTRDLGNBQWMsSUFBSUM7UUFFeEIsSUFBSXZDLFVBQVVzQyxZQUFZRSxNQUFNLENBQUMsWUFBWXhDO1FBQzdDLElBQUl1QixRQUFRZSxZQUFZRSxNQUFNLENBQUMsVUFBVWpCO1FBQ3pDLElBQUlwQixRQUFRbUMsWUFBWUUsTUFBTSxDQUFDLFVBQVVyQztRQUN6QyxJQUFJUSxRQUFRMkIsWUFBWUUsTUFBTSxDQUFDLFVBQVU3QjtRQUV6QyxnQkFBZ0I7UUFDaEIsTUFBTThCLFVBQXVCO1lBQzNCLGdCQUFnQjtRQUNsQjtRQUVBLDZDQUE2QztRQUM3QyxJQUFJdEIsU0FBU3VCLGNBQWM7WUFDekJELE9BQU8sQ0FBQyxnQkFBZ0IsR0FBRyxDQUFDLE9BQU8sRUFBRXRCLFFBQVF1QixZQUFZLEVBQUU7UUFDN0Q7UUFFQSxvQ0FBb0M7UUFDcEMsTUFBTUMsYUFBYSxJQUFJQztRQUN2QixNQUFNQyxZQUFZQyxXQUFXLElBQU1ILFdBQVdJLEtBQUssSUFBSSxPQUFPLG1CQUFtQjtRQUVqRixNQUFNQyxhQUFhLEdBQUcxRCxRQUFRLHNCQUFzQixFQUFFZ0QsWUFBWVcsUUFBUSxLQUFLLENBQUMsQ0FBQyxFQUFFWCxZQUFZVyxRQUFRLElBQUksR0FBRyxJQUFJO1FBQ2xILE1BQU1DLFdBQVcsTUFBTUMsTUFBTUgsWUFBWTtZQUN2Q0ksUUFBUTtZQUNSWDtZQUNBWSxRQUFRVixXQUFXVSxNQUFNO1FBQzNCO1FBRUFDLGFBQWFUO1FBRWIsSUFBSSxDQUFDSyxTQUFTSyxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNTixTQUFTTyxJQUFJO1lBQ3JDaEMsUUFBUWlDLEtBQUssQ0FBQyxzQkFBc0JSLFNBQVMvQyxNQUFNLEVBQUVxRDtZQUVyRCw4QkFBOEI7WUFDOUIsSUFBSUcsZUFBZSxDQUFDLG1CQUFtQixFQUFFVCxTQUFTL0MsTUFBTSxFQUFFO1lBQzFELElBQUk7Z0JBQ0YsTUFBTXlELFlBQVlDLEtBQUtDLEtBQUssQ0FBQ047Z0JBQzdCLElBQUlJLFVBQVVGLEtBQUssRUFBRTtvQkFDbkJDLGVBQWVDLFVBQVVGLEtBQUs7Z0JBQ2hDO1lBQ0YsRUFBRSxPQUFNO1lBQ04sNkNBQTZDO1lBQy9DO1lBRUEsK0JBQStCO1lBQy9CakMsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBT3RDLHFEQUFZQSxDQUFDK0MsSUFBSSxDQUFDO2dCQUN2QixHQUFHekMsYUFBYTtnQkFDaEJxRSxXQUFXO2dCQUNYQyxRQUFRTDtZQUNWO1FBQ0Y7UUFFQSxNQUFNekMsT0FBTyxNQUFNZ0MsU0FBU2YsSUFBSTtRQUNoQyxPQUFPL0MscURBQVlBLENBQUMrQyxJQUFJLENBQUNqQjtJQUUzQixFQUFFLE9BQU93QyxPQUFZO1FBQ25CLG9DQUFvQztRQUNwQyxJQUFJQSxNQUFNNUQsSUFBSSxLQUFLLGNBQWM7WUFDL0IyQixRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPdEMscURBQVlBLENBQUMrQyxJQUFJLENBQUM7Z0JBQ3ZCLEdBQUd6QyxhQUFhO2dCQUNoQnFFLFdBQVc7Z0JBQ1hDLFFBQVE7WUFDVjtRQUNGO1FBRUF2QyxRQUFRaUMsS0FBSyxDQUFDLDRCQUE0QkE7UUFFMUMsNENBQTRDO1FBQzVDakMsUUFBUUMsR0FBRyxDQUFDO1FBQ1osT0FBT3RDLHFEQUFZQSxDQUFDK0MsSUFBSSxDQUFDO1lBQ3ZCLEdBQUd6QyxhQUFhO1lBQ2hCcUUsV0FBVztZQUNYQyxRQUFRO1FBQ1Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvbW50L2MvVXNlcnMvSWJyYWhpbS9Eb3dubG9hZHMvb2xhcmkvc3VuYS1tYWluL2Zyb250ZW5kL3NyYy9hcHAvYXBpL2NvbXBvc2lvL3Rvb2xraXRzL3JvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9zZXJ2ZXInO1xuXG5jb25zdCBBUElfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwgfHwgJyc7XG5cbi8vIE1vY2sgZGF0YSBmb3IgQ29tcG9zaW8gdG9vbGtpdHNcbmNvbnN0IE1PQ0tfVE9PTEtJVFMgPSB7XG4gIHN1Y2Nlc3M6IHRydWUsXG4gIHRvb2xraXRzOiBbXG4gICAge1xuICAgICAgaWQ6ICdnbWFpbCcsXG4gICAgICBuYW1lOiAnR21haWwnLFxuICAgICAgc2x1ZzogJ2dtYWlsJyxcbiAgICAgIGNhdGVnb3J5OiAnY29tbXVuaWNhdGlvbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ1NlbmQsIHJlY2VpdmUsIGFuZCBtYW5hZ2UgZW1haWxzIHdpdGggR21haWwnLFxuICAgICAgaWNvbjogJ2h0dHBzOi8vY29tcG9zaW8uZGV2L2ljb25zL2dtYWlsLnN2ZycsXG4gICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgc3VwcG9ydGVkX2F1dGg6IFsnb2F1dGgyJ10sXG4gICAgICB0b29sc19jb3VudDogMTUsXG4gICAgICBwb3B1bGFyaXR5OiA5NSxcbiAgICAgIHRhZ3M6IFsnZW1haWwnLCAnZ29vZ2xlJywgJ21lc3NhZ2luZyddLFxuICAgICAgZmVhdHVyZXM6IFsnc2VuZF9lbWFpbCcsICdyZWNlaXZlX2VtYWlsJywgJ21hbmFnZV9sYWJlbHMnLCAnc2VhcmNoX2VtYWlscyddXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3NsYWNrJyxcbiAgICAgIG5hbWU6ICdTbGFjaycsXG4gICAgICBzbHVnOiAnc2xhY2snLFxuICAgICAgY2F0ZWdvcnk6ICdjb21tdW5pY2F0aW9uJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29ubmVjdCB3aXRoIFNsYWNrIHRvIHNlbmQgbWVzc2FnZXMsIG1hbmFnZSBjaGFubmVscywgYW5kIGludGVyYWN0IHdpdGggd29ya3NwYWNlcycsXG4gICAgICBpY29uOiAnaHR0cHM6Ly9jb21wb3Npby5kZXYvaWNvbnMvc2xhY2suc3ZnJyxcbiAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICBzdXBwb3J0ZWRfYXV0aDogWydvYXV0aDInXSxcbiAgICAgIHRvb2xzX2NvdW50OiAyMCxcbiAgICAgIHBvcHVsYXJpdHk6IDkwLFxuICAgICAgdGFnczogWydtZXNzYWdpbmcnLCAndGVhbScsICdjb2xsYWJvcmF0aW9uJ10sXG4gICAgICBmZWF0dXJlczogWydzZW5kX21lc3NhZ2UnLCAnY3JlYXRlX2NoYW5uZWwnLCAndXBsb2FkX2ZpbGUnLCAnbWFuYWdlX3VzZXJzJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnZ2l0aHViJyxcbiAgICAgIG5hbWU6ICdHaXRIdWInLFxuICAgICAgc2x1ZzogJ2dpdGh1YicsXG4gICAgICBjYXRlZ29yeTogJ2RldmVsb3Blcl90b29scycsXG4gICAgICBkZXNjcmlwdGlvbjogJ01hbmFnZSByZXBvc2l0b3JpZXMsIGlzc3VlcywgcHVsbCByZXF1ZXN0cywgYW5kIG1vcmUgb24gR2l0SHViJyxcbiAgICAgIGljb246ICdodHRwczovL2NvbXBvc2lvLmRldi9pY29ucy9naXRodWIuc3ZnJyxcbiAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICBzdXBwb3J0ZWRfYXV0aDogWydvYXV0aDInLCAnYXBpX2tleSddLFxuICAgICAgdG9vbHNfY291bnQ6IDM1LFxuICAgICAgcG9wdWxhcml0eTogOTgsXG4gICAgICB0YWdzOiBbJ2dpdCcsICd2ZXJzaW9uX2NvbnRyb2wnLCAnZGV2ZWxvcG1lbnQnLCAnY29kZSddLFxuICAgICAgZmVhdHVyZXM6IFsnY3JlYXRlX2lzc3VlJywgJ2NyZWF0ZV9wcicsICdtYW5hZ2VfcmVwb3MnLCAnY29kZV9yZXZpZXcnXVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdub3Rpb24nLFxuICAgICAgbmFtZTogJ05vdGlvbicsXG4gICAgICBzbHVnOiAnbm90aW9uJyxcbiAgICAgIGNhdGVnb3J5OiAncHJvZHVjdGl2aXR5JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRlLCB1cGRhdGUsIGFuZCBtYW5hZ2UgcGFnZXMgYW5kIGRhdGFiYXNlcyBpbiBOb3Rpb24nLFxuICAgICAgaWNvbjogJ2h0dHBzOi8vY29tcG9zaW8uZGV2L2ljb25zL25vdGlvbi5zdmcnLFxuICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICAgIHN1cHBvcnRlZF9hdXRoOiBbJ29hdXRoMiddLFxuICAgICAgdG9vbHNfY291bnQ6IDE4LFxuICAgICAgcG9wdWxhcml0eTogODUsXG4gICAgICB0YWdzOiBbJ25vdGVzJywgJ2RvY3VtZW50YXRpb24nLCAna25vd2xlZGdlX2Jhc2UnXSxcbiAgICAgIGZlYXR1cmVzOiBbJ2NyZWF0ZV9wYWdlJywgJ3VwZGF0ZV9kYXRhYmFzZScsICdzZWFyY2hfY29udGVudCcsICdtYW5hZ2Vfd29ya3NwYWNlJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnbGluZWFyJyxcbiAgICAgIG5hbWU6ICdMaW5lYXInLFxuICAgICAgc2x1ZzogJ2xpbmVhcicsXG4gICAgICBjYXRlZ29yeTogJ3Byb2plY3RfbWFuYWdlbWVudCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ01hbmFnZSBpc3N1ZXMsIHByb2plY3RzLCBhbmQgd29ya2Zsb3dzIGluIExpbmVhcicsXG4gICAgICBpY29uOiAnaHR0cHM6Ly9jb21wb3Npby5kZXYvaWNvbnMvbGluZWFyLnN2ZycsXG4gICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgc3VwcG9ydGVkX2F1dGg6IFsnYXBpX2tleSddLFxuICAgICAgdG9vbHNfY291bnQ6IDIyLFxuICAgICAgcG9wdWxhcml0eTogNzUsXG4gICAgICB0YWdzOiBbJ2lzc3VlcycsICdwcm9qZWN0X21hbmFnZW1lbnQnLCAnYWdpbGUnXSxcbiAgICAgIGZlYXR1cmVzOiBbJ2NyZWF0ZV9pc3N1ZScsICd1cGRhdGVfcHJvamVjdCcsICdtYW5hZ2VfY3ljbGVzJywgJ3RyYWNrX3Byb2dyZXNzJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnZGlzY29yZCcsXG4gICAgICBuYW1lOiAnRGlzY29yZCcsXG4gICAgICBzbHVnOiAnZGlzY29yZCcsXG4gICAgICBjYXRlZ29yeTogJ2NvbW11bmljYXRpb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdTZW5kIG1lc3NhZ2VzLCBtYW5hZ2Ugc2VydmVycywgYW5kIGludGVyYWN0IHdpdGggRGlzY29yZCBjb21tdW5pdGllcycsXG4gICAgICBpY29uOiAnaHR0cHM6Ly9jb21wb3Npby5kZXYvaWNvbnMvZGlzY29yZC5zdmcnLFxuICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICAgIHN1cHBvcnRlZF9hdXRoOiBbJ29hdXRoMicsICdib3RfdG9rZW4nXSxcbiAgICAgIHRvb2xzX2NvdW50OiAyNSxcbiAgICAgIHBvcHVsYXJpdHk6IDgwLFxuICAgICAgdGFnczogWydtZXNzYWdpbmcnLCAnY29tbXVuaXR5JywgJ2dhbWluZyddLFxuICAgICAgZmVhdHVyZXM6IFsnc2VuZF9tZXNzYWdlJywgJ21hbmFnZV9zZXJ2ZXInLCAnY3JlYXRlX2NoYW5uZWwnLCAnbW9kZXJhdGVfdXNlcnMnXVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICd0d2l0dGVyJyxcbiAgICAgIG5hbWU6ICdUd2l0dGVyL1gnLFxuICAgICAgc2x1ZzogJ3R3aXR0ZXInLFxuICAgICAgY2F0ZWdvcnk6ICdzb2NpYWxfbWVkaWEnLFxuICAgICAgZGVzY3JpcHRpb246ICdQb3N0IHR3ZWV0cywgbWFuYWdlIHRpbWVsaW5lLCBhbmQgaW50ZXJhY3Qgd2l0aCBUd2l0dGVyL1ggcGxhdGZvcm0nLFxuICAgICAgaWNvbjogJ2h0dHBzOi8vY29tcG9zaW8uZGV2L2ljb25zL3R3aXR0ZXIuc3ZnJyxcbiAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICBzdXBwb3J0ZWRfYXV0aDogWydvYXV0aDInXSxcbiAgICAgIHRvb2xzX2NvdW50OiAxMixcbiAgICAgIHBvcHVsYXJpdHk6IDg4LFxuICAgICAgdGFnczogWydzb2NpYWwnLCAnbWljcm9ibG9nZ2luZycsICduZXdzJ10sXG4gICAgICBmZWF0dXJlczogWydwb3N0X3R3ZWV0JywgJ2xpa2VfdHdlZXQnLCAnZm9sbG93X3VzZXInLCAnc2VhcmNoX3R3ZWV0cyddXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2dvb2dsZV9jYWxlbmRhcicsXG4gICAgICBuYW1lOiAnR29vZ2xlIENhbGVuZGFyJyxcbiAgICAgIHNsdWc6ICdnb29nbGUtY2FsZW5kYXInLFxuICAgICAgY2F0ZWdvcnk6ICdwcm9kdWN0aXZpdHknLFxuICAgICAgZGVzY3JpcHRpb246ICdNYW5hZ2UgZXZlbnRzLCBzY2hlZHVsZSBtZWV0aW5ncywgYW5kIG9yZ2FuaXplIHlvdXIgY2FsZW5kYXInLFxuICAgICAgaWNvbjogJ2h0dHBzOi8vY29tcG9zaW8uZGV2L2ljb25zL2dvb2dsZS1jYWxlbmRhci5zdmcnLFxuICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICAgIHN1cHBvcnRlZF9hdXRoOiBbJ29hdXRoMiddLFxuICAgICAgdG9vbHNfY291bnQ6IDEwLFxuICAgICAgcG9wdWxhcml0eTogOTIsXG4gICAgICB0YWdzOiBbJ2NhbGVuZGFyJywgJ3NjaGVkdWxpbmcnLCAnZ29vZ2xlJywgJ2V2ZW50cyddLFxuICAgICAgZmVhdHVyZXM6IFsnY3JlYXRlX2V2ZW50JywgJ3VwZGF0ZV9ldmVudCcsICdnZXRfYXZhaWxhYmlsaXR5JywgJ3NlbmRfaW52aXRlcyddXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2ppcmEnLFxuICAgICAgbmFtZTogJ0ppcmEnLFxuICAgICAgc2x1ZzogJ2ppcmEnLFxuICAgICAgY2F0ZWdvcnk6ICdwcm9qZWN0X21hbmFnZW1lbnQnLFxuICAgICAgZGVzY3JpcHRpb246ICdUcmFjayBpc3N1ZXMsIG1hbmFnZSBwcm9qZWN0cywgYW5kIGNvbGxhYm9yYXRlIHdpdGggSmlyYScsXG4gICAgICBpY29uOiAnaHR0cHM6Ly9jb21wb3Npby5kZXYvaWNvbnMvamlyYS5zdmcnLFxuICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICAgIHN1cHBvcnRlZF9hdXRoOiBbJ29hdXRoMicsICdhcGlfa2V5J10sXG4gICAgICB0b29sc19jb3VudDogMzAsXG4gICAgICBwb3B1bGFyaXR5OiA4MixcbiAgICAgIHRhZ3M6IFsnaXNzdWVzJywgJ2FnaWxlJywgJ3Byb2plY3RfbWFuYWdlbWVudCcsICdhdGxhc3NpYW4nXSxcbiAgICAgIGZlYXR1cmVzOiBbJ2NyZWF0ZV9pc3N1ZScsICd1cGRhdGVfc3ByaW50JywgJ21hbmFnZV9ib2FyZCcsICdnZW5lcmF0ZV9yZXBvcnRzJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnZmlnbWEnLFxuICAgICAgbmFtZTogJ0ZpZ21hJyxcbiAgICAgIHNsdWc6ICdmaWdtYScsXG4gICAgICBjYXRlZ29yeTogJ2Rlc2lnbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ0FjY2VzcyBhbmQgbWFuYWdlIGRlc2lnbiBmaWxlcywgY29tcG9uZW50cywgYW5kIHRlYW1zIGluIEZpZ21hJyxcbiAgICAgIGljb246ICdodHRwczovL2NvbXBvc2lvLmRldi9pY29ucy9maWdtYS5zdmcnLFxuICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICAgIHN1cHBvcnRlZF9hdXRoOiBbJ29hdXRoMiddLFxuICAgICAgdG9vbHNfY291bnQ6IDgsXG4gICAgICBwb3B1bGFyaXR5OiA3OCxcbiAgICAgIHRhZ3M6IFsnZGVzaWduJywgJ3VpJywgJ2NvbGxhYm9yYXRpb24nLCAncHJvdG90eXBpbmcnXSxcbiAgICAgIGZlYXR1cmVzOiBbJ2dldF9maWxlJywgJ2V4cG9ydF9hc3NldHMnLCAnbWFuYWdlX3RlYW0nLCAnY3JlYXRlX2NvbW1lbnQnXVxuICAgIH1cbiAgXSxcbiAgdG90YWw6IDEwLFxuICBoYXNfbW9yZTogZmFsc2UsXG4gIGN1cnNvcjogbnVsbFxufTtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBcbiAgICAvLyBHZXQgdGhlIGN1cnJlbnQgc2Vzc2lvbiBmb3IgYXV0aGVudGljYXRpb24gKG9wdGlvbmFsIGZvciB0aGlzIGVuZHBvaW50KVxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KCk7XG4gICAgY29uc3QgeyBkYXRhOiB7IHNlc3Npb24gfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XG4gICAgXG4gICAgLy8gRXh0cmFjdCBxdWVyeSBwYXJhbWV0ZXJzIGZvciBmaWx0ZXJpbmdcbiAgICBjb25zdCBjYXRlZ29yeSA9IHNlYXJjaFBhcmFtcy5nZXQoJ2NhdGVnb3J5Jyk7XG4gICAgY29uc3Qgc2VhcmNoID0gc2VhcmNoUGFyYW1zLmdldCgnc2VhcmNoJyk7XG4gICAgY29uc3Qgc3RhdHVzID0gc2VhcmNoUGFyYW1zLmdldCgnc3RhdHVzJyk7XG4gICAgY29uc3QgY3Vyc29yID0gc2VhcmNoUGFyYW1zLmdldCgnY3Vyc29yJyk7XG4gICAgXG4gICAgLy8gSWYgQVBJX1VSTCBpcyBub3QgY29uZmlndXJlZCBvciBlbXB0eSwgcmV0dXJuIG1vY2sgZGF0YVxuICAgIGlmICghQVBJX1VSTCB8fCBBUElfVVJMLnRyaW0oKSA9PT0gJycpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdVc2luZyBtb2NrIGRhdGEgZm9yIENvbXBvc2lvIHRvb2xraXRzIChBUElfVVJMIG5vdCBjb25maWd1cmVkKScpO1xuICAgICAgXG4gICAgICAvLyBGaWx0ZXIgbW9jayBkYXRhIGJhc2VkIG9uIHF1ZXJ5IHBhcmFtZXRlcnNcbiAgICAgIGxldCBmaWx0ZXJlZFRvb2xraXRzID0gWy4uLk1PQ0tfVE9PTEtJVFMudG9vbGtpdHNdO1xuICAgICAgXG4gICAgICAvLyBGaWx0ZXIgYnkgY2F0ZWdvcnlcbiAgICAgIGlmIChjYXRlZ29yeSAmJiBjYXRlZ29yeSAhPT0gJ2FsbCcpIHtcbiAgICAgICAgZmlsdGVyZWRUb29sa2l0cyA9IGZpbHRlcmVkVG9vbGtpdHMuZmlsdGVyKFxuICAgICAgICAgIHRvb2xraXQgPT4gdG9vbGtpdC5jYXRlZ29yeSA9PT0gY2F0ZWdvcnlcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gRmlsdGVyIGJ5IHNlYXJjaCB0ZXJtXG4gICAgICBpZiAoc2VhcmNoKSB7XG4gICAgICAgIGNvbnN0IHNlYXJjaExvd2VyID0gc2VhcmNoLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgIGZpbHRlcmVkVG9vbGtpdHMgPSBmaWx0ZXJlZFRvb2xraXRzLmZpbHRlcihcbiAgICAgICAgICB0b29sa2l0ID0+IFxuICAgICAgICAgICAgdG9vbGtpdC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpIHx8XG4gICAgICAgICAgICB0b29sa2l0LmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpIHx8XG4gICAgICAgICAgICB0b29sa2l0LnRhZ3Muc29tZSh0YWcgPT4gdGFnLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpKVxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBGaWx0ZXIgYnkgc3RhdHVzXG4gICAgICBpZiAoc3RhdHVzKSB7XG4gICAgICAgIGZpbHRlcmVkVG9vbGtpdHMgPSBmaWx0ZXJlZFRvb2xraXRzLmZpbHRlcihcbiAgICAgICAgICB0b29sa2l0ID0+IHRvb2xraXQuc3RhdHVzID09PSBzdGF0dXNcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gUmV0dXJuIGZpbHRlcmVkIG1vY2sgZGF0YVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgdG9vbGtpdHM6IGZpbHRlcmVkVG9vbGtpdHMsXG4gICAgICAgIHRvdGFsOiBmaWx0ZXJlZFRvb2xraXRzLmxlbmd0aCxcbiAgICAgICAgaGFzX21vcmU6IGZhbHNlLFxuICAgICAgICBjdXJzb3I6IG51bGxcbiAgICAgIH0pO1xuICAgIH1cbiAgICBcbiAgICAvLyBWYWxpZGF0ZSBVUkwgZm9ybWF0IGJlZm9yZSBhdHRlbXB0aW5nIHRvIGZldGNoXG4gICAgdHJ5IHtcbiAgICAgIG5ldyBVUkwoYCR7QVBJX1VSTH0vYXBpL2NvbXBvc2lvL3Rvb2xraXRzYCk7XG4gICAgfSBjYXRjaCAodXJsRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdJbnZhbGlkIGJhY2tlbmQgVVJMIGZvcm1hdCwgcmV0dXJuaW5nIG1vY2sgZGF0YScpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKE1PQ0tfVE9PTEtJVFMpO1xuICAgIH1cbiAgICBcbiAgICAvLyBCdWlsZCBxdWVyeSBwYXJhbWV0ZXJzIGZvciByZWFsIGJhY2tlbmQgY2FsbFxuICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIFxuICAgIGlmIChjYXRlZ29yeSkgcXVlcnlQYXJhbXMuYXBwZW5kKCdjYXRlZ29yeScsIGNhdGVnb3J5KTtcbiAgICBpZiAoc2VhcmNoKSBxdWVyeVBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIHNlYXJjaCk7XG4gICAgaWYgKHN0YXR1cykgcXVlcnlQYXJhbXMuYXBwZW5kKCdzdGF0dXMnLCBzdGF0dXMpO1xuICAgIGlmIChjdXJzb3IpIHF1ZXJ5UGFyYW1zLmFwcGVuZCgnY3Vyc29yJywgY3Vyc29yKTtcbiAgICBcbiAgICAvLyBCdWlsZCBoZWFkZXJzXG4gICAgY29uc3QgaGVhZGVyczogSGVhZGVyc0luaXQgPSB7XG4gICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgIH07XG4gICAgXG4gICAgLy8gQWRkIGF1dGhvcml6YXRpb24gaGVhZGVyIGlmIHNlc3Npb24gZXhpc3RzXG4gICAgaWYgKHNlc3Npb24/LmFjY2Vzc190b2tlbikge1xuICAgICAgaGVhZGVyc1snQXV0aG9yaXphdGlvbiddID0gYEJlYXJlciAke3Nlc3Npb24uYWNjZXNzX3Rva2VufWA7XG4gICAgfVxuICAgIFxuICAgIC8vIENhbGwgdGhlIGJhY2tlbmQgQVBJIHdpdGggdGltZW91dFxuICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDUwMDApOyAvLyA1IHNlY29uZCB0aW1lb3V0XG4gICAgXG4gICAgY29uc3QgYmFja2VuZFVybCA9IGAke0FQSV9VUkx9L2FwaS9jb21wb3Npby90b29sa2l0cyR7cXVlcnlQYXJhbXMudG9TdHJpbmcoKSA/IGA/JHtxdWVyeVBhcmFtcy50b1N0cmluZygpfWAgOiAnJ31gO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYmFja2VuZFVybCwge1xuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGhlYWRlcnMsXG4gICAgICBzaWduYWw6IGNvbnRyb2xsZXIuc2lnbmFsXG4gICAgfSk7XG4gICAgXG4gICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG4gICAgXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgY29uc29sZS5lcnJvcignQmFja2VuZCBBUEkgZXJyb3I6JywgcmVzcG9uc2Uuc3RhdHVzLCBlcnJvclRleHQpO1xuICAgICAgXG4gICAgICAvLyBUcnkgdG8gcGFyc2UgZXJyb3IgcmVzcG9uc2VcbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBgQmFja2VuZCBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWA7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBKU09OLnBhcnNlKGVycm9yVGV4dCk7XG4gICAgICAgIGlmIChlcnJvckRhdGEuZXJyb3IpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvckRhdGEuZXJyb3I7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2gge1xuICAgICAgICAvLyBVc2UgZGVmYXVsdCBlcnJvciBtZXNzYWdlIGlmIHBhcnNpbmcgZmFpbHNcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gUmV0dXJuIG1vY2sgZGF0YSBhcyBmYWxsYmFja1xuICAgICAgY29uc29sZS5sb2coJ0JhY2tlbmQgQVBJIGZhaWxlZCwgcmV0dXJuaW5nIG1vY2sgZGF0YSBhcyBmYWxsYmFjaycpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgLi4uTU9DS19UT09MS0lUUyxcbiAgICAgICAgX2ZhbGxiYWNrOiB0cnVlLFxuICAgICAgICBfZXJyb3I6IGVycm9yTWVzc2FnZVxuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGRhdGEpO1xuICAgIFxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgLy8gSGFuZGxlIHRpbWVvdXQgZXJyb3Igc3BlY2lmaWNhbGx5XG4gICAgaWYgKGVycm9yLm5hbWUgPT09ICdBYm9ydEVycm9yJykge1xuICAgICAgY29uc29sZS5sb2coJ0JhY2tlbmQgQVBJIHRpbWVvdXQsIHJldHVybmluZyBtb2NrIGRhdGEnKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIC4uLk1PQ0tfVE9PTEtJVFMsXG4gICAgICAgIF9mYWxsYmFjazogdHJ1ZSxcbiAgICAgICAgX2Vycm9yOiAnQmFja2VuZCBBUEkgdGltZW91dCwgdXNpbmcgbW9jayBkYXRhJ1xuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHRvb2xraXRzOicsIGVycm9yKTtcbiAgICBcbiAgICAvLyBSZXR1cm4gbW9jayBkYXRhIGFzIGZhbGxiYWNrIG9uIGFueSBlcnJvclxuICAgIGNvbnNvbGUubG9nKCdFcnJvciBvY2N1cnJlZCwgcmV0dXJuaW5nIG1vY2sgZGF0YSBhcyBmYWxsYmFjaycpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAuLi5NT0NLX1RPT0xLSVRTLFxuICAgICAgX2ZhbGxiYWNrOiB0cnVlLFxuICAgICAgX2Vycm9yOiAnRmFpbGVkIHRvIGZldGNoIGZyb20gYmFja2VuZCwgdXNpbmcgbW9jayBkYXRhJ1xuICAgIH0pO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlQ2xpZW50IiwiQVBJX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQUNLRU5EX1VSTCIsIk1PQ0tfVE9PTEtJVFMiLCJzdWNjZXNzIiwidG9vbGtpdHMiLCJpZCIsIm5hbWUiLCJzbHVnIiwiY2F0ZWdvcnkiLCJkZXNjcmlwdGlvbiIsImljb24iLCJzdGF0dXMiLCJzdXBwb3J0ZWRfYXV0aCIsInRvb2xzX2NvdW50IiwicG9wdWxhcml0eSIsInRhZ3MiLCJmZWF0dXJlcyIsInRvdGFsIiwiaGFzX21vcmUiLCJjdXJzb3IiLCJHRVQiLCJyZXF1ZXN0Iiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwic3VwYWJhc2UiLCJkYXRhIiwic2Vzc2lvbiIsImF1dGgiLCJnZXRTZXNzaW9uIiwiZ2V0Iiwic2VhcmNoIiwidHJpbSIsImNvbnNvbGUiLCJsb2ciLCJmaWx0ZXJlZFRvb2xraXRzIiwiZmlsdGVyIiwidG9vbGtpdCIsInNlYXJjaExvd2VyIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNvbWUiLCJ0YWciLCJqc29uIiwibGVuZ3RoIiwidXJsRXJyb3IiLCJxdWVyeVBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsImFwcGVuZCIsImhlYWRlcnMiLCJhY2Nlc3NfdG9rZW4iLCJjb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImFib3J0IiwiYmFja2VuZFVybCIsInRvU3RyaW5nIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsInNpZ25hbCIsImNsZWFyVGltZW91dCIsIm9rIiwiZXJyb3JUZXh0IiwidGV4dCIsImVycm9yIiwiZXJyb3JNZXNzYWdlIiwiZXJyb3JEYXRhIiwiSlNPTiIsInBhcnNlIiwiX2ZhbGxiYWNrIiwiX2Vycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/composio/toolkits/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00c3b743d074a69244eab1d948789d902598d3c17c\":\"createClient\"} */ \n\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://llliyihxaeakffunjjcb.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxsbGl5aWh4YWVha2ZmdW5qamNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4NDMxOTAsImV4cCI6MjA3MjQxOTE5MH0.Cu0nxk1MregjKOTlXNU4S0NXZlTiaRsPEmiK4isI04M\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    createClient\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(createClient, \"00c3b743d074a69244eab1d948789d902598d3c17c\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDWjtBQUUvQixlQUFlRTtJQUNwQixNQUFNQyxjQUFjLE1BQU1GLHFEQUFPQTtJQUVqQyxPQUFPRCxpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLGtOQUF5QyxFQUN6QztRQUNFSCxTQUFTO1lBQ1BPO2dCQUNFLE9BQU9MLFlBQVlLLE1BQU07WUFDM0I7WUFDQUMsUUFBT0MsWUFBWTtnQkFDakIsSUFBSTtvQkFDRkEsYUFBYUMsT0FBTyxDQUFDLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLE9BQU8sRUFBRSxHQUM1Q1gsWUFBWVksR0FBRyxDQUFDSCxNQUFNQyxPQUFPQztnQkFFakMsRUFBRSxPQUFNO2dCQUNOLDBEQUEwRDtnQkFDMUQsd0RBQXdEO2dCQUN4RCxpQkFBaUI7Z0JBQ25CO1lBQ0Y7UUFDRjtJQUNGO0FBRUo7OztJQXpCc0JaOztBQUFBQSwwRkFBQUEsQ0FBQUEiLCJzb3VyY2VzIjpbIi9tbnQvYy9Vc2Vycy9JYnJhaGltL0Rvd25sb2Fkcy9vbGFyaS9zdW5hLW1haW4vZnJvbnRlbmQvc3JjL2xpYi9zdXBhYmFzZS9zZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzZXJ2ZXInXG5pbXBvcnQgeyBjcmVhdGVTZXJ2ZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJ1xuaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycydcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgY29uc3QgY29va2llU3RvcmUgPSBhd2FpdCBjb29raWVzKClcblxuICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50KFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhLFxuICAgIHtcbiAgICAgIGNvb2tpZXM6IHtcbiAgICAgICAgZ2V0QWxsKCkge1xuICAgICAgICAgIHJldHVybiBjb29raWVTdG9yZS5nZXRBbGwoKVxuICAgICAgICB9LFxuICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZXNUb1NldC5mb3JFYWNoKCh7IG5hbWUsIHZhbHVlLCBvcHRpb25zIH0pID0+XG4gICAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgIClcbiAgICAgICAgICB9IGNhdGNoIHtcbiAgICAgICAgICAgIC8vIFRoZSBgc2V0QWxsYCBtZXRob2Qgd2FzIGNhbGxlZCBmcm9tIGEgU2VydmVyIENvbXBvbmVudC5cbiAgICAgICAgICAgIC8vIFRoaXMgY2FuIGJlIGlnbm9yZWQgaWYgeW91IGhhdmUgbWlkZGxld2FyZSByZWZyZXNoaW5nXG4gICAgICAgICAgICAvLyB1c2VyIHNlc3Npb25zLlxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfVxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlU2VydmVyQ2xpZW50IiwiY29va2llcyIsImNyZWF0ZUNsaWVudCIsImNvb2tpZVN0b3JlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiZ2V0QWxsIiwic2V0QWxsIiwiY29va2llc1RvU2V0IiwiZm9yRWFjaCIsIm5hbWUiLCJ2YWx1ZSIsIm9wdGlvbnMiLCJzZXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Ftoolkits%2Froute&page=%2Fapi%2Fcomposio%2Ftoolkits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Ftoolkits%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();