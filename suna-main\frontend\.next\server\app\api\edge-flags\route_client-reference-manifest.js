globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/edge-flags/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/global-error.tsx":{"*":{"id":"(ssr)/./src/app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js":{"*":{"id":"(ssr)/./node_modules/@next/third-parties/dist/google/ga.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js":{"*":{"id":"(ssr)/./node_modules/@next/third-parties/dist/google/gtm.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":{"*":{"id":"(ssr)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/posthog-identify.tsx":{"*":{"id":"(ssr)/./src/components/posthog-identify.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>/navbar.tsx":{"*":{"id":"(ssr)/./src/components/home/<USER>/navbar.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/app/(home)/page.tsx":{"*":{"id":"(ssr)/./src/app/(home)/page.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/layout-content.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/layout-content.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/components/auth/background-aal-checker.tsx":{"*":{"id":"(ssr)/./src/components/auth/background-aal-checker.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/enhanced-dashboard.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/enhanced-dashboard.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/components/ui/skeleton.tsx":{"*":{"id":"(ssr)/./src/components/ui/skeleton.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/global-error.tsx":{"id":"(app-pages-browser)/./src/app/global-error.tsx","name":"*","chunks":["app/global-error","static/chunks/app/global-error.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/@next/third-parties/dist/google/ga.js":{"id":"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/@next/third-parties/dist/google/gtm.js":{"id":"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":{"id":"(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/@vercel/analytics/dist/react/index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/@vercel/speed-insights/dist/next/index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/home/<USER>":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/posthog-identify.tsx":{"id":"(app-pages-browser)/./src/components/posthog-identify.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/ui/sonner.tsx":{"id":"(app-pages-browser)/./src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/home/<USER>/navbar.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>/navbar.tsx","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":true},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/(home)/page.tsx":{"id":"(app-pages-browser)/./src/app/(home)/page.tsx","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":true},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/lib/framework/boundary-components.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/lib/framework/boundary-components.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/lib/metadata/generate/icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/node_modules/next/dist/esm/next-devtools/userspace/app/segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/dashboard/layout-content.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/layout-content.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":true},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/auth/background-aal-checker.tsx":{"id":"(app-pages-browser)/./src/components/auth/background-aal-checker.tsx","name":"*","chunks":[],"async":false},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/dashboard/enhanced-dashboard.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/enhanced-dashboard.tsx","name":"*","chunks":[],"async":true},"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/components/ui/skeleton.tsx":{"id":"(app-pages-browser)/./src/components/ui/skeleton.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/":[],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/global-error":[],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/not-found":[],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/(home)/layout":[],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/(home)/page":[{"inlined":false,"path":"static/css/app/(home)/page.css"}],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/(dashboard)/layout":[],"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/api/edge-flags/route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/global-error.tsx":{"*":{"id":"(rsc)/./src/app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js":{"*":{"id":"(rsc)/./node_modules/@next/third-parties/dist/google/ga.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js":{"*":{"id":"(rsc)/./node_modules/@next/third-parties/dist/google/gtm.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":{"*":{"id":"(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":{"*":{"id":"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":{"*":{"id":"(rsc)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(rsc)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/posthog-identify.tsx":{"*":{"id":"(rsc)/./src/components/posthog-identify.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(rsc)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>/navbar.tsx":{"*":{"id":"(rsc)/./src/components/home/<USER>/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(home)/page.tsx":{"*":{"id":"(rsc)/./src/app/(home)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/layout-content.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/layout-content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/background-aal-checker.tsx":{"*":{"id":"(rsc)/./src/components/auth/background-aal-checker.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/enhanced-dashboard.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/enhanced-dashboard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/skeleton.tsx":{"*":{"id":"(rsc)/./src/components/ui/skeleton.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}