-- Additional tables needed by <PERSON><PERSON> frontend

-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'active',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create agents table
CREATE TABLE IF NOT EXISTS public.agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT DEFAULT 'custom',
    config JSONB DEFAULT '{}',
    status TEXT DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create agent_runs table
CREATE TABLE IF NOT EXISTS public.agent_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE,
    thread_id UUID REFERENCES public.threads(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending',
    input JSONB,
    output JSONB,
    error TEXT,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for new tables
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_runs ENABLE ROW LEVEL SECURITY;

-- RLS policies for projects
CREATE POLICY "Users can view their projects" ON public.projects
    FOR SELECT USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create projects" ON public.projects
    FOR INSERT WITH CHECK (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their projects" ON public.projects
    FOR UPDATE USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their projects" ON public.projects
    FOR DELETE USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

-- RLS policies for agents
CREATE POLICY "Users can view their agents" ON public.agents
    FOR SELECT USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create agents" ON public.agents
    FOR INSERT WITH CHECK (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their agents" ON public.agents
    FOR UPDATE USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their agents" ON public.agents
    FOR DELETE USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        )
    );

-- RLS policies for agent_runs
CREATE POLICY "Users can view their agent runs" ON public.agent_runs
    FOR SELECT USING (
        agent_id IN (
            SELECT id FROM public.agents WHERE account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create agent runs" ON public.agent_runs
    FOR INSERT WITH CHECK (
        agent_id IN (
            SELECT id FROM public.agents WHERE account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            )
        )
    );

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_projects_account_id ON public.projects(account_id);
CREATE INDEX IF NOT EXISTS idx_agents_account_id ON public.agents(account_id);
CREATE INDEX IF NOT EXISTS idx_agent_runs_agent_id ON public.agent_runs(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_runs_thread_id ON public.agent_runs(thread_id);
CREATE INDEX IF NOT EXISTS idx_threads_account_id ON public.threads(account_id);