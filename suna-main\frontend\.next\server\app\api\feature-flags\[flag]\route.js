/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/feature-flags/[flag]/route";
exports.ids = ["app/api/feature-flags/[flag]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&page=%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&page=%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_feature_flags_flag_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/feature-flags/[flag]/route.ts */ \"(rsc)/./src/app/api/feature-flags/[flag]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/feature-flags/[flag]/route\",\n        pathname: \"/api/feature-flags/[flag]\",\n        filename: \"route\",\n        bundlePath: \"app/api/feature-flags/[flag]/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/api/feature-flags/[flag]/route.ts\",\n    nextConfigOutput,\n    userland: _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_feature_flags_flag_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/feature-flags/[flag]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&page=%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/feature-flags/[flag]/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/feature-flags/[flag]/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_URL = \"http://localhost:8003\" || 0;\nconst FEATURE_FLAG_DEFINITIONS = {\n    custom_agents: {\n        enabled: true,\n        description: 'Allows users to create and customize AI agents',\n        category: 'core'\n    },\n    advanced_analytics: {\n        enabled: true,\n        description: 'Advanced analytics and reporting features',\n        category: 'premium'\n    },\n    team_collaboration: {\n        enabled: true,\n        description: 'Team collaboration and sharing features',\n        category: 'core'\n    },\n    api_access: {\n        enabled: true,\n        description: 'API access and integration features',\n        category: 'premium'\n    },\n    export_reports: {\n        enabled: true,\n        description: 'Export reports and data features',\n        category: 'core'\n    },\n    billing_alerts: {\n        enabled: true,\n        description: 'Billing notifications and usage alerts',\n        category: 'core'\n    },\n    maintenance_mode: {\n        enabled: false,\n        description: 'System maintenance mode',\n        category: 'core'\n    },\n    beta_features: {\n        enabled: false,\n        description: 'Beta features access',\n        category: 'beta',\n        rolloutPercentage: 10\n    },\n    premium_features: {\n        enabled: false,\n        description: 'Premium features access',\n        category: 'premium'\n    },\n    chat_history: {\n        enabled: true,\n        description: 'Chat history and conversation persistence',\n        category: 'core'\n    },\n    file_uploads: {\n        enabled: true,\n        description: 'File upload capabilities',\n        category: 'core'\n    },\n    voice_input: {\n        enabled: true,\n        description: 'Voice input and speech-to-text',\n        category: 'beta'\n    },\n    dark_mode: {\n        enabled: true,\n        description: 'Dark mode theme support',\n        category: 'core'\n    },\n    notifications: {\n        enabled: true,\n        description: 'Push notifications and alerts',\n        category: 'core'\n    },\n    auto_save: {\n        enabled: true,\n        description: 'Automatic saving of work',\n        category: 'core'\n    }\n};\n// Cache with TTL\nconst cache = new Map();\nconst CACHE_TTL = 5 * 60 * 1000; // 5 minutes\nconst REQUEST_TIMEOUT = 5000; // 5 seconds\n// Circuit breaker state\nlet circuitBreakerState = {\n    failures: 0,\n    lastFailure: 0,\n    state: 'closed'\n};\nconst FAILURE_THRESHOLD = 3;\nconst RECOVERY_TIMEOUT = 30000; // 30 seconds\nfunction isCircuitBreakerOpen() {\n    if (circuitBreakerState.state === 'open') {\n        if (Date.now() - circuitBreakerState.lastFailure > RECOVERY_TIMEOUT) {\n            circuitBreakerState.state = 'half-open';\n            return false;\n        }\n        return true;\n    }\n    return false;\n}\nfunction recordSuccess() {\n    circuitBreakerState.failures = 0;\n    circuitBreakerState.state = 'closed';\n}\nfunction recordFailure() {\n    circuitBreakerState.failures++;\n    circuitBreakerState.lastFailure = Date.now();\n    if (circuitBreakerState.failures >= FAILURE_THRESHOLD) {\n        circuitBreakerState.state = 'open';\n    }\n}\nasync function fetchWithTimeout(url, options, timeout = REQUEST_TIMEOUT) {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(()=>controller.abort(), timeout);\n    try {\n        const response = await fetch(url, {\n            ...options,\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        return response;\n    } catch (error) {\n        clearTimeout(timeoutId);\n        throw error;\n    }\n}\nfunction generateETag(flag, enabled) {\n    return `\"${Buffer.from(flag + enabled.toString()).toString('base64')}\"`;\n}\nasync function GET(request, { params }) {\n    const { flag } = await params;\n    const ifNoneMatch = request.headers.get('if-none-match');\n    // Validate flag name\n    if (!flag || typeof flag !== 'string' || flag.length > 100) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid flag name',\n            message: 'Flag name must be a non-empty string with maximum 100 characters',\n            flag_name: flag\n        }, {\n            status: 400\n        });\n    }\n    // Check cache first\n    const cacheKey = `flag:${flag}`;\n    const cached = cache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {\n        // Check ETag\n        if (ifNoneMatch && ifNoneMatch === cached.etag) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 304,\n                headers: {\n                    'ETag': cached.etag,\n                    'Cache-Control': 'public, max-age=300'\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(cached.data, {\n            headers: {\n                'ETag': cached.etag,\n                'Cache-Control': 'public, max-age=300',\n                'X-Cache': 'HIT'\n            }\n        });\n    }\n    let flagData;\n    let source = 'fallback';\n    // Try to fetch from backend if circuit breaker allows\n    if (!isCircuitBreakerOpen() && API_URL) {\n        try {\n            const response = await fetchWithTimeout(`${API_URL}/feature-flags/${flag}`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'User-Agent': 'Olari-Frontend/1.0'\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                flagData = {\n                    flag_name: flag,\n                    enabled: data.enabled ?? false,\n                    details: {\n                        description: data.details?.description || FEATURE_FLAG_DEFINITIONS[flag]?.description || 'No description available',\n                        category: FEATURE_FLAG_DEFINITIONS[flag]?.category || 'core',\n                        updated_at: data.details?.updated_at || new Date().toISOString(),\n                        source: 'backend',\n                        rolloutPercentage: data.details?.rolloutPercentage || FEATURE_FLAG_DEFINITIONS[flag]?.rolloutPercentage\n                    },\n                    metadata: {\n                        cached_at: new Date().toISOString(),\n                        cache_ttl: CACHE_TTL / 1000,\n                        circuit_breaker_state: circuitBreakerState.state\n                    }\n                };\n                source = 'backend';\n                recordSuccess();\n            } else {\n                throw new Error(`Backend returned ${response.status}: ${response.statusText}`);\n            }\n        } catch (error) {\n            console.warn(`Failed to fetch feature flag ${flag} from backend:`, error instanceof Error ? error.message : error);\n            recordFailure();\n            flagData = null; // Will use fallback\n        }\n    }\n    // Use fallback if backend failed or circuit breaker is open\n    if (!flagData) {\n        const definition = FEATURE_FLAG_DEFINITIONS[flag];\n        if (!definition) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Feature flag not found',\n                message: `Feature flag '${flag}' is not defined in the system`,\n                flag_name: flag,\n                available_flags: Object.keys(FEATURE_FLAG_DEFINITIONS)\n            }, {\n                status: 404\n            });\n        }\n        flagData = {\n            flag_name: flag,\n            enabled: definition.enabled,\n            details: {\n                description: definition.description,\n                category: definition.category,\n                updated_at: new Date().toISOString(),\n                source: 'fallback',\n                rolloutPercentage: definition.rolloutPercentage,\n                dependencies: definition.dependencies\n            },\n            metadata: {\n                cached_at: new Date().toISOString(),\n                cache_ttl: CACHE_TTL / 1000,\n                circuit_breaker_state: circuitBreakerState.state,\n                backend_unavailable: isCircuitBreakerOpen()\n            }\n        };\n    }\n    // Generate ETag\n    const etag = generateETag(flag, flagData.enabled);\n    // Check ETag again\n    if (ifNoneMatch && ifNoneMatch === etag) {\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n            status: 304,\n            headers: {\n                'ETag': etag,\n                'Cache-Control': 'public, max-age=300'\n            }\n        });\n    }\n    // Cache the result\n    cache.set(cacheKey, {\n        data: flagData,\n        timestamp: Date.now(),\n        etag\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(flagData, {\n        headers: {\n            'ETag': etag,\n            'Cache-Control': 'public, max-age=300',\n            'X-Cache': 'MISS',\n            'X-Source': source,\n            'X-Circuit-Breaker': circuitBreakerState.state\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/feature-flags/[flag]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&page=%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffeature-flags%2F%5Bflag%5D%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();