/* THIS FILE IS GENERATED - DO NOT EDIT */
var OpenJPEG = (() => {
  
  return (
function(moduleArg = {}) {
  var moduleRtn;

var Module=moduleArg;var readyPromiseResolve,readyPromiseReject;var readyPromise=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var _scriptName=import.meta.url;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){try{scriptDirectory=new URL(".",_scriptName).href}catch{}{readAsync=async url=>{var response=await fetch(url,{credentials:"same-origin"});if(response.ok){return response.arrayBuffer()}throw new Error(response.status+" : "+response.url)}}}else{}var out=console.log.bind(console);var err=console.error.bind(console);var wasmBinary;var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536)},Module:function(binary){},Instance:function(module,info){this.exports=(
// EMSCRIPTEN_START_ASM
function instantiate(Ba){var a;var b=new Uint8Array(123);for(var c=25;c>=0;--c){b[48+c]=52+c;b[65+c]=c;b[97+c]=26+c}b[43]=62;b[47]=63;function i(j,k,l){var d,e,c=0,f=k,g=l.length,h=k+(g*3>>2)-(l[g-2]=="=")-(l[g-1]=="=");for(;c<g;c+=4){d=b[l.charCodeAt(c+1)];e=b[l.charCodeAt(c+2)];j[f++]=b[l.charCodeAt(c)]<<2|d>>4;if(f<h)j[f++]=d<<4|e>>2;if(f<h)j[f++]=e<<6|b[l.charCodeAt(c+3)]}return j}function m(n){i(a,1024,"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");i(a,16656,"IwClAEMAZgCDAO6oFADf2CMAvhBDAP/1gwB+IFUAX1EjADUAQwBORIMAzsQUAM/MIwD+4kMA/5mDAJYAxQA/MSMApQBDAF5EgwDOyBQA3xEjAP70QwD//IMAngBVAHcAIwA1AEMA//GDAK6IFAC3ACMA/vhDAO/kgwCOiMUAHxEjAKUAQwBmAIMA7qgUAN9UIwC+EEMA7yKDAH4gVQB/IiMANQBDAE5EgwDOxBQAvxEjAP7iQwD3AIMAlgDFAD8iIwClAEMAXkSDAM7IFADXACMA/vRDAP+6gwCeAFUAbwAjADUAQwD/5oMArogUAK+iIwD++EMA5wCDAI6IxQAvIgIAxQCEAH4gAgDOxCQA9wACAP6iRABWAAIAngAUANcAAgC+EIQAZgACAK6IJADfEQIA7qhEADYAAgCOiBQAHxECAMUAhABuAAIAzogkAP+IAgD+uEQATkQCAJYAFAC3AAIA/uSEAF5EAgCmACQA5wACAN5URAAuIgIAPgAUAHcAAgDFAIQAfiACAM7EJAD/8QIA/qJEAFYAAgCeABQAvxECAL4QhABmAAIArogkAO8iAgDuqEQANgACAI6IFAB/IgIAxQCEAG4AAgDOiCQA7+QCAP64RABORAIAlgAUAK+iAgD+5IQAXkQCAKYAJADf2AIA3lREAC4iAgA+ABQAX1ECAFUAhABmAAIA3ogkAP8yAgD+EUQATkQCAK4AFAC3AAIAfjGEAF5RAgDGACQA1wACAO4gRAAeEQIAngAUAHcAAgBVAIQAXlQCAM5EJADnAAIA/vFEADYAAgCmABQAX1UCAP50hAA+EQIAviAkAH90AgDexEQA//gCAJYAFAAvIgIAVQCEAGYAAgDeiCQA9wACAP4RRABORAIArgAUAI+IAgB+MYQAXlECAMYAJADPyAIA7iBEAB4RAgCeABQAbwACAFUAhABeVAIAzkQkAN/RAgD+8UQANgACAKYAFAB/IgIA/nSEAD4RAgC+ICQAvyICAN7ERADvIgIAlgAUAD8yAwDe1P30//wUAD4RVQCPiAMAvjKFAOcAJQBeUf6qf3IDAM5E/fjvRBQAfmRFAK+iAwCmAF1V35n98TYA/vVvYgMA3tH99P/mFAB+cVUAv7EDAK6IhQDf1SUATkT+8n9mAwDGAP347+IUAF5URQCfEQMAlgBdVc/I/fEeEe7IZwADAN7U/fT/8xQAPhFVAL8RAwC+MoUA39glAF5R/qovIgMAzkT9+PcAFAB+ZEUAn5gDAKYAXVXXAP3xNgD+9W9EAwDe0f30/7kUAH5xVQC3AAMAroiFAN/cJQBORP7ydwADAMYA/fjv5BQAXlRFAH9zAwCWAF1Vv7j98R4R7sg/MgIApQCEAH5AAgDeECQA3xECAP5yRABWAAIArqgUAL+yAgCWAIQAZgACAMYAJADnAAIA7shEAC4iAgCOiBQAdwACAKUAhABuAAIAzogkAPcAAgD+kUQANgACAK6iFACvqgIA/riEAF4AAgC+ACQAz8QCAO5ERAD/9AIAPiIUAB8RAgClAIQAfkACAN4QJAD/mQIA/nJEAFYAAgCuqBQAtwACAJYAhABmAAIAxgAkANcAAgDuyEQALiICAI6IFABPRAIApQCEAG4AAgDOiCQA7+ICAP6RRAA2AAIArqIUAH9EAgD+uIQAXgACAL4AJACfAAIA7kREAP92AgA+IhQAPzEDAMYAhQD/2f3yfmT+8b+ZAwCuoiUA72b99FYA7uJ/cwMAvphFAPcA/fhmAP52n4gDAI6IFQDf1aUALiLemE9EAwC+soUA//z98m4ilgC3AAMArqolAN/R/fQ2AN7Ub2QDAK6oRQDv6v34XkTu6H9xAwA+MhUAz8SlAP/6zog/MQMAxgCFAP93/fJ+ZP7xv7MDAK6iJQDnAP30VgDu4ncAAwC+mEUA7+T9+GYA/nZ/ZgMAjogVANcApQAuIt6YPzMDAL6yhQD/df3ybiKWAJ+RAwCuqiUA35n99DYA3tRfUQMArqhFAO/s/fheRO7of3IDAD4yFQC/saUA//POiB8RAwDeVP3yHhEUAH5k/vjPzAMAvpFFAO8iJQAuIv7zj4gDAMYAhQD3ABQAXhH+/K+oAwCmADUA38j98T4x/mZvZAMAzsj98v/1FABmAP70v7oDAK4iRQDnACUAPjL+6n9zAwC+soUA31UUAFYAfnGfEQMAlgA1AM/E/fE+M+7oT0QDAN5U/fIeERQAfmT++L+ZAwC+kUUA7+IlAC4i/vN/ZgMAxgCFAO/kFABeEf78n5gDAKYANQDXAP3xPjH+Zm8iAwDOyP3y/7kUAGYA/vS3AAMAriJFAN/RJQA+Mv7qdwADAL6yhQDv7BQAVgB+cX9yAwCWADUAv7j98T4z7uhfVPzx3tH9+tcA/PgWAP3/f3T89H5x/fO/s/zy7+ru6E9E/PGuIgUAv7j8+PcA/vx3APz0XhH99X91/PLf2O7iPzP88b6y/frPiPz4//v9/39z/PRuAP3ztwD88u9m/vk/MfzxngAFAL+6/Pj//f72ZwD89CYA/fWPiPzy39ze1C8i/PHe0f36z8T8+BYA/f9/cvz0fnH987+Z/PLv7O7oRwD88a4iBQCnAPz4//f+/FcA/PReEf31lwD88t/V7uI3APzxvrL9+scA/Pj//v3/f2b89G4A/fOvqPzy5wD++T8y/PGeAAUAv7H8+O/k/vZfVPz0JgD99YcA/PLfmd7UHxETAGUAQwDeAIMAjYgjAE5EEwClAEMAroiDADUAIwDXABMAxQBDAJ4AgwBVACMALiITAJUAQwB+AIMA/hAjAHcAEwBlAEMAzoiDAI2IIwAeERMApQBDAF4AgwA1ACMA5wATAMUAQwC+AIMAVQAjAP8REwCVAEMAPgCDAO5AIwCvohMAZQBDAN4AgwCNiCMATkQTAKUAQwCuiIMANQAjAO9EEwDFAEMAngCDAFUAIwAuIhMAlQBDAH4AgwD+ECMAtwATAGUAQwDOiIMAjYgjAB4REwClAEMAXgCDADUAIwDPxBMAxQBDAL4AgwBVACMA9wATAJUAQwA+AIMA7kAjAG8AAQCEAAEAVgABABQAAQDXAAEAJAABAJYAAQBFAAEAdwABAIQAAQDGAAEAFAABAI+IAQAkAAEA9wABADUAAQAvIgEAhAABAP5AAQAUAAEAtwABACQAAQC/AAEARQABAGcAAQCEAAEApgABABQAAQBPRAEAJAABAOcAAQA1AAEAPxEBAIQAAQBWAAEAFAABAM8AAQAkAAEAlgABAEUAAQBvAAEAhAABAMYAAQAUAAEAnwABACQAAQDvAAEANQABAD8yAQCEAAEA/kABABQAAQCvAAEAJAABAP9EAQBFAAEAXwABAIQAAQCmAAEAFAABAH8AAQAkAAEA3wABADUAAQAfEQEAJAABAFYAAQCFAAEAvwABABQAAQD3AAEAxgABAHcAAQAkAAEA//gBAEUAAQB/AAEAFAABAN8AAQCmAAEAPzEBACQAAQAuIgEAhQABALcAAQAUAAEA70QBAK6iAQBnAAEAJAABAP9RAQBFAAEAlwABABQAAQDPAAEANgABAD8iAQAkAAEAVgABAIUAAQC/sgEAFAABAO9AAQDGAAEAbwABACQAAQD/cgEARQABAJ8AAQAUAAEA1wABAKYAAQBPRAEAJAABAC4iAQCFAAEAr6gBABQAAQDnAAEArqIBAF8AAQAkAAEA/0QBAEUAAQCPiAEAFAABAK+qAQA2AAEAHxECAP74JABWAAIAtgCFAP9mAgDOABQAHhECAJYANQCvqAIA9gAkAD4xAgCmAEUAv7MCAL6yFAD/9QIAZgB+UV9UAgD+8iQALiICAK4ihQDvRAIAxgAUAP/0AgB2ADUAf0QCAN5AJAA+MgIAngBFANcAAgC+iBQA//oCAF4R/vFPRAIA/vgkAFYAAgC2AIUA78gCAM4AFAAeEQIAlgA1AI+IAgD2ACQAPjECAKYARQDfRAIAvrIUAP+oAgBmAH5RbwACAP7yJAAuIgIAriKFAOcAAgDGABQA7+ICAHYANQB/cgIA3kAkAD4yAgCeAEUAv7ECAL6IFAD/cwIAXhH+8T8zAQCEAAEA7iABAMUAAQDPxAEARAABAP8yAQAVAAEAj4gBAIQAAQBmAAEAJQABAK8AAQBEAAEA7yIBAKYAAQBfAAEAhAABAE5EAQDFAAEAz8wBAEQAAQD3AAEAFQABAG8AAQCEAAEAVgABACUAAQCfAAEARAABAN8AAQD+MAEALyIBAIQAAQDuIAEAxQABAM/IAQBEAAEA/xEBABUAAQB3AAEAhAABAGYAAQAlAAEAfwABAEQAAQDnAAEApgABADcAAQCEAAEATkQBAMUAAQC3AAEARAABAL8AAQAVAAEAPwABAIQAAQBWAAEAJQABAJcAAQBEAAEA1wABAP4wAQAfEQIA7qhEAI6IAgDWAMUA//MCAP78JQA+AAIAtgBVAN/YAgD++EQAZgACAH4ghQD/mQIA5gD1ADYAAgCmABUAnwACAP7yRAB2AAIAzkTFAP92AgD+8SUATkQCAK4AVQDPyAIA/vREAF5EAgC+EIUA7+QCAN5U9QAeEQIAlgAVAC8iAgDuqEQAjogCANYAxQD/+gIA/vwlAD4AAgC2AFUAvxECAP74RABmAAIAfiCFAO8iAgDmAPUANgACAKYAFQB/IgIA/vJEAHYAAgDORMUA/9UCAP7xJQBORAIArgBVAG8AAgD+9EQAXkQCAL4QhQDfEQIA3lT1AB4RAgCWABUAX1EDAPYAFAAeEUQAjoilAN/UAwCuolUA/3YkAD4itgCvqgMA5gAUAP/1RABmAIUAz8wDAJ4AxQDvRCQANgD++H8xAwDu6BQA//FEAHYApQDPxAMAfiJVAN/RJABORP70X1EDANYAFADv4kQAXkSFAL8iAwCWAMUA38gkAC4i/vJvIgMA9gAUAB4RRACOiKUAv7EDAK6iVQD/MyQAPiK2AK+oAwDmABQA/7lEAGYAhQC/qAMAngDFAO/kJAA2AP74b2QDAO7oFAD//EQAdgClAM/IAwB+IlUA7+okAE5E/vR/dAMA1gAUAP/6RABeRIUAv7IDAJYAxQDfRCQALiL+8j8x8wD++v3xNgAEAL4ydQDfEfMA3lT98u/k1QB+cf78f3PzAP7z/fgeEQQAlgBVAL+x8wDOALUA39j99GYA/rlfVPMA/nb98SYABACmAHUAnwDzAK4A/fL/99UARgD+9X908wDmAP34FgAEAIYAVQCPiPMAxgC1AO/i/fReEe6oPxHzAP76/fE2AAQAvjJ1AN/R8wDeVP3y//vVAH5x/vx/RPMA/vP9+B4RBACWAFUAf3LzAM4AtQDvIv30ZgD+uU9E8wD+dv3xJgAEAKYAdQC/EfMArgD98v//1QBGAP71PzLzAOYA/fgWAAQAhgBVAG8A8wDGALUAv7j99F4R7qgvIg==");i(a,20764,"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");i(a,24649,"AQABAAEAAQAAAQEAAAEBAAEAAQABAAEAAAAAAQEBAQAAAAAAAQABAAAAAAEBAQEAAAABAAEBAQ==");i(a,24713,"AQABAAEAAQAAAQEAAAEBAAEAAQABAAEAAAAAAQEBAQAAAAAAAQABAAAAAAEBAQEAAAABAAEBAQ==");i(a,24777,"AQABAAEAAQ==");i(a,24793,"AQABAAEAAQAAAAABAQEBAAAAAAABAAEAAAAAAQEBAQAAAAAAAQABAQEAAAEBAAAAAQABAAEAAQEBAQEBAQEBAAEAAQABAAEAAAAAAQEBAQABAAABAQABAAAAAAEBAQEAAQABAQEBAQIAAAAEAAAABAAAAAgAAACQ/wAADAAAABkAAABS/wAAFAAAABoAAABT/wAAFAAAABsAAABe/wAAFAAAABwAAABc/wAAFAAAAB0AAABd/wAAFAAAAB4AAABf/wAAFAAAAB8AAABR/wAAAgAAACAAAABV/wAABAAAACEAAABX/wAABAAAACIAAABY/wAAEAAAACMAAABg/wAABAAAACQAAABh/wAAEAAAACUAAACR/w==");i(a,25080,"Y/8AAAQAAAAmAAAAZP8AABQAAAAnAAAAdP8AABQAAAAoAAAAeP8AAAQAAAApAAAAUP8AAAQAAAAqAAAAWf8AAAQAAAArAAAAdf8AABQAAAAsAAAAd/8AABQAAAAtAAAAAAAAABQ=");i(a,25200,"LgAAAC8AAAAwAAAAMQAAADIAAAAzAAAANAAAADUAAAAgIFBqNwAAAHB5dGY4AAAAaDJwajk=");i(a,25264,"cmRoaToAAABybG9jOwAAAGNjcGI8AAAAcmxjcD0AAABwYW1jPgAAAGZlZGM/AAAAcGY=");i(a,25328,"GQALABkZGQAAAAAFAAAAAAAACQAAAAALAAAAAAAAAAAZAAoKGRkZAwoHAAEACQsYAAAJBgsAAAsABhkAAAAZGRk=");i(a,25409,"DgAAAAAAAAAAGQALDRkZGQANAAACAAkOAAAACQAOAAAO");i(a,25467,"DA==");i(a,25479,"EwAAAAATAAAAAAkMAAAAAAAMAAAM");i(a,25525,"EA==");i(a,25537,"DwAAAAQPAAAAAAkQAAAAAAAQAAAQ");i(a,25583,"Eg==");i(a,25595,"EQAAAAARAAAAAAkSAAAAAAASAAASAAAaAAAAGhoa");i(a,25650,"GgAAABoaGgAAAAAAAAk=");i(a,25699,"FA==");i(a,25711,"FwAAAAAXAAAAAAkUAAAAAAAUAAAU");i(a,25757,"Fg==");i(a,25769,"FQAAAAAVAAAAAAkWAAAAAAAWAAAWAAAwMTIzNDU2Nzg5QUJDREVGAAAAAHAAAABwAAAAcQAAAHEAAABxAAAAcQAAAHEAAABxAAAAcAAAAHAAAABxAAAAcAAAAHAAAABwAAAAcA==");i(a,25904,"cQAAAHEAAABwAAAAcAAAAAAAAABwAAAAAAAAAHE=");i(a,26072,"kHABAAAAAAAF");i(a,26092,"aw==");i(a,26116,"bAAAAG0AAAD4aw==");i(a,26140,"Ag==");i(a,26156,"//////////8=");i(a,26224,"BQ==");i(a,26236,"bg==");i(a,26260,"bAAAAG8AAAAIbAAAAAQ=");i(a,26284,"AQ==");i(a,26300,"/////wo=")}var o=new ArrayBuffer(16);var p=new Int32Array(o);var q=new Float32Array(o);var r=new Float64Array(o);function s(t){return p[t]}function u(t,v){p[t]=v}function w(){return r[0]}function x(v){r[0]=v}function y(z,v,A){z=z>>>0;A=A>>>0;if(z+A>a.length)throw"trap: invalid memory.fill";a.fill(v,z,z+A)}function B(z,C,A){a.copyWithin(z,C,C+A)}function D(){throw new Error("abort")}function Aa(n){var E=new ArrayBuffer(16777216);var F=new Int8Array(E);var G=new Int16Array(E);var H=new Int32Array(E);var I=new Uint8Array(E);var J=new Uint16Array(E);var K=new Uint32Array(E);var L=new Float32Array(E);var M=new Float64Array(E);var N=Math.imul;var O=Math.fround;var P=Math.abs;var Q=Math.clz32;var R=Math.min;var S=Math.max;var T=Math.floor;var U=Math.ceil;var V=Math.trunc;var W=Math.sqrt;var X=n.a;var Y=X.a;var Z=X.b;var _=X.c;var $=X.d;var aa=X.e;var ba=X.f;var ca=X.g;var da=X.h;var ea=X.i;var fa=X.j;var ga=X.k;var ha=X.l;var ia=X.m;var ja=X.n;var ka=X.o;var la=X.p;var ma=X.q;var na=X.r;var oa=94352;var pa=0;var qa=0;var ra=0;
// EMSCRIPTEN_START_FUNCS
function fd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,z=0,A=0,C=0,D=0,E=0,M=0,P=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=O(0),ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,pa=0,qa=0,ra=0,ta=0,ua=0,va=0,wa=0,xa=0;aa=oa-96|0;oa=aa;E=H[a+8>>2];a:{b:{c:{if(!H[a>>2]){g=N(H[E+16>>2]-H[E+8>>2]|0,H[E+20>>2]-H[E+12>>2]|0)<<2;c=Ja(g);H[E+60>>2]=c;if(!c){Ca(H[a+32>>2],1,8023,0);d=a+28|0;break b}if(!g){break c}y(c,0,g);break c}c=H[E+60>>2];if(!c){break c}Da(c);H[E+60>>2]=0}if(!H[H[a+28>>2]>>2]){break a}qa=H[a+16>>2];c=H[qa+28>>2]+N(H[qa+24>>2],152)|0;wa=H[c-152>>2];xa=H[c-144>>2];ra=H[a+20>>2];ta=H[a+12>>2];va=H[a+4>>2];d=a+28|0;d:{q=H[b+4>>2];e=0;e:{if((q|0)<=0){break e}l=H[b>>2];c=0;f:{while(1){g=l+N(c,12)|0;if(!H[g>>2]){break f}c=c+1|0;if((q|0)!=(c|0)){continue}break}e=0;break e}e=H[g+4>>2]}if(e){break d}e=Fa(1,156);if(!e){Ca(H[a+32>>2],1,6313,0);break b}H[e+140>>2]=0;c=0;l=H[b+4>>2];g:{if((l|0)==2147483647){break g}g=H[b>>2];if((l|0)>0){while(1){q=g+N(c,12)|0;if(!H[q>>2]){l=H[q+8>>2];if(l){sa[l|0](H[q+4>>2]);g=H[b>>2]}b=g+N(c,12)|0;H[b+8>>2]=15;H[b+4>>2]=e;c=1;break g}c=c+1|0;if((l|0)!=(c|0)){continue}break}}g=Ia(g,N(l,12)+12|0);c=0;if(!g){break g}H[b>>2]=g;c=H[b+4>>2];g=g+N(c,12)|0;H[g+8>>2]=15;H[g+4>>2]=e;H[g>>2]=0;H[b+4>>2]=c+1;c=1}if(c){break d}Ca(H[a+32>>2],1,8338,0);b=H[e+116>>2];if(b){Da(b);H[e+116>>2]=0}b=H[e+120>>2];if(b){Da(b);H[e+120>>2]=0}Da(H[e+148>>2]);Da(e);break b}H[e+144>>2]=H[a+24>>2];W=H[a+40>>2];ba=H[a+36>>2];R=H[a+32>>2];P=H[ra+808>>2];b=H[ta+16>>2];h:{Z=H[ra+16>>2];i:{if(Z&64){l=oa-304|0;oa=l;j:{if(P){if(ba){Ca(R,1,3219,0);break j}Ca(R,1,3219,0);break j}j=H[e+116>>2];c=H[E+20>>2]-H[E+12>>2]|0;b=H[E+16>>2]-H[E+8>>2]|0;g=N(c,b);k:{l:{if(g>>>0>K[e+132>>2]){Da(j);f=g<<2;j=Ja(f);H[e+116>>2]=j;if(!j){j=0;break j}H[e+132>>2]=g;break l}if(!j){break k}f=g<<2}if(!f){break k}y(j,0,f)}j=H[e+120>>2];m:{if(K[e+136>>2]>2639){break m}Da(j);j=Ja(10560);H[e+120>>2]=j;if(j){break m}j=0;break j}H[e+136>>2]=2640;y(j,0,10560);H[e+128>>2]=c;H[e+124>>2]=b;n=H[E+24>>2];if(!n){j=1;break j}q=H[E+28>>2];j=1;n:{o:{p:{q:{f=H[E+52>>2];r:{if(f){c=H[E+4>>2];j=0;if(f>>>0>=4){b=f&-4;while(1){g=c+(m<<3)|0;j=H[g+28>>2]+(H[g+20>>2]+(H[g+12>>2]+(H[g+4>>2]+j|0)|0)|0)|0;m=m+4|0;z=z+4|0;if((b|0)!=(z|0)){continue}break}}b=f&3;if(b){while(1){j=H[(c+(m<<3)|0)+4>>2]+j|0;m=m+1|0;k=k+1|0;if((b|0)!=(k|0)){continue}break}}if(!H[e+144>>2]&(f|0)==1){break o}if(K[e+152>>2]>=j>>>0){break r}z=Ia(H[e+148>>2],j);if(z){break q}j=0;break j}if(!H[e+144>>2]){break j}}z=H[e+148>>2];if(z){break p}j=0;break j}H[e+152>>2]=j;H[e+148>>2]=z}if(!H[E+52>>2]){j=0;break n}f=H[E+4>>2];j=0;m=0;while(1){g=m<<3;c=g+f|0;b=H[c+4>>2];if(b){B(j+z|0,H[c>>2],b)}f=H[E+4>>2];j=H[(g+f|0)+4>>2]+j|0;m=m+1|0;if(m>>>0<K[E+52>>2]){continue}break}break n}z=H[H[E+4>>2]>>2]}m=0;f=0;c=H[E+40>>2];g=0;s:{if(!c){break s}b=H[E>>2];f=H[b+8>>2];g=0;if((c|0)==1){break s}g=H[b+32>>2]}c=n-q|0;f=f+g|0;t:{if(!f){k=0;break t}m=1;b=H[E>>2];r=H[b>>2];k=0;if((f|0)==1){m=0;break t}k=H[b+24>>2]}S=c+1|0;ia=H[e+116>>2];$=H[e+120>>2];W=H[E+12>>2];t=H[E+20>>2];M=H[E+8>>2];ja=H[E+16>>2];u:{v:{w:{x:{y:{z:{A:{B:{if(!(!m|k)){if(!ba){break B}Ca(R,2,10806,0);f=1;break A}if(f>>>0<4){break A}if(ba){H[l+112>>2]=f;Ca(R,1,9590,l+112|0);break u}H[l+96>>2]=f;Ca(R,1,9590,l+96|0);j=0;break j}Ca(R,2,10806,0);m=H[E+24>>2];if(m>>>0>30){break z}P=1;if(m>>>0>=S>>>0){break x}break v}m=H[E+24>>2];if(m>>>0<=30){break y}if(!ba){break z}H[l+32>>2]=H[E+24>>2];Ca(R,1,12302,l+32|0);break u}H[l>>2]=m;Ca(R,1,12302,l);j=0;break j}if(m>>>0<S>>>0){break w}if(f>>>0<2){P=f;break x}if((m|0)!=(S|0)){P=f;break x}P=1;if(I[26384]){break x}if(!ba){F[26384]=1;H[l+64>>2]=f;Ca(R,2,10299,l- -64|0);break x}if(!I[26384]){F[26384]=1;H[l+80>>2]=f;Ca(R,2,10299,l+80|0)}}if(!(!(r>>>0<2|j>>>0<r>>>0)&k+r>>>0<=j>>>0)){if(ba){j=0;Ca(R,1,9532,0);break j}j=0;Ca(R,1,9532,0);break j}T=r+z|0;b=I[T-1|0];j=b<<4|I[T-2|0]&15;if(!(!(j>>>0<2|(b|0)==255)&(j|0)<=(r|0))){if(ba){j=0;Ca(R,1,15305,0);break j}j=0;Ca(R,1,15305,0);break j}ua=H[E+28>>2];H[l+272>>2]=0;H[l+280>>2]=0;H[l+264>>2]=0;H[l+268>>2]=0;H[l+296>>2]=0;H[l+300>>2]=0;H[l+284>>2]=0;H[l+288>>2]=0;c=j-1|0;H[l+276>>2]=c;f=(r+z|0)-j|0;H[l+256>>2]=f;q=I[f|0];b=8;H[l+272>>2]=8;i=f+1|0;H[l+256>>2]=i;g=j-2|0;H[l+276>>2]=g;n=(c|0)==1?q|15:q;c=0;q=c;H[l+264>>2]=n;H[l+268>>2]=c;H[l+280>>2]=!c&(n|0)==255;h=f&3;C:{D:{if((h|0)==3){break D}w=0;if(!((n|0)!=255|(c|0)!=0|I[i|0]<=143)){break C}c=255;c=j>>>0>=3?I[i|0]:c;m=j-3|0;H[l+276>>2]=m;f=!q&(n|0)==255;b=f?15:16;H[l+272>>2]=b;V=i+(j>>>0>2)|0;H[l+256>>2]=V;c=(g|0)==1?c|15:c;g=0;H[l+280>>2]=!g&(c|0)==255;g=c;i=n;c=f?7:8;f=c&31;if((c&63)>>>0>=32){U=i<<f;c=0}else{U=(1<<f)-1&i>>>32-f|q<<f;c=i<<f}n=c|g;c=x|U;q=c;H[l+264>>2]=n;H[l+268>>2]=c;if((h|0)==2){break D}f=255;w=0;if(!((g|0)!=255|(x|0)!=0|I[V|0]<=143)){break C}f=j>>>0>=4?I[V|0]:f;i=j-4|0;H[l+276>>2]=i;u=V+(j>>>0>3)|0;H[l+256>>2]=u;c=(m|0)==1?f|15:f;f=0;V=f;H[l+280>>2]=!f&(c|0)==255;f=!x&(g|0)==255;b=(f?7:8)+b|0;H[l+272>>2]=b;g=c;m=n;c=f?7:8;f=c&31;if((c&63)>>>0>=32){x=m<<f;c=0}else{x=(1<<f)-1&m>>>32-f|q<<f;c=m<<f}n=c|g;c=x|V;q=c;H[l+264>>2]=n;H[l+268>>2]=c;if((h|0)==1){break D}w=0;if(!((g|0)!=255|(V|0)!=0|I[u|0]<=143)){break C}c=255;c=j>>>0>=5?I[u|0]:c;H[l+276>>2]=j-5;H[l+256>>2]=u+(j>>>0>4);f=0;c=(i|0)==1?c|15:c;H[l+280>>2]=!f&(c|0)==255;g=!V&(g|0)==255;b=(g?7:8)+b|0;H[l+272>>2]=b;i=n;g=g?7:8;m=g&31;if((g&63)>>>0>=32){x=i<<m;g=0}else{x=(1<<m)-1&i>>>32-m|q<<m;g=i<<m}n=g|c;c=f|x;q=c;H[l+264>>2]=n;H[l+268>>2]=c}c=64-b|0;b=n;g=c&31;if((c&63)>>>0>=32){i=b<<g;b=0}else{i=(1<<g)-1&b>>>32-g|q<<g;b=b<<g}H[l+264>>2]=b;H[l+268>>2]=i;w=1}if(!w){if(ba){j=0;Ca(R,1,11470,0);break j}j=0;Ca(R,1,11470,0);break j}C=ja-M|0;i=j;u=i-2|0;H[l+244>>2]=u;V=r+z|0;c=V-3|0;H[l+224>>2]=c;b=I[V-2|0];f=b>>>0>143;H[l+248>>2]=f;q=0;n=b>>>4|0;H[l+232>>2]=n;H[l+236>>2]=0;v=(n&7)==7?3:4;H[l+240>>2]=v;b=(c&3)+1|0;w=b>>>0<u>>>0?b:u;E:{F:{if(!u){j=0;H[l+244>>2]=u-w;break F}b=V-4|0;H[l+224>>2]=b;g=I[c|0];j=g>>>0>143;H[l+248>>2]=j;q=v&31;if((v&63)>>>0>=32){x=g<<q;q=0}else{x=(1<<q)-1&g>>>32-q;q=g<<q}n=q|n;H[l+232>>2]=n;q=x;H[l+236>>2]=q;v=(f?(g&127)==127?7:8:8)+v|0;H[l+240>>2]=v;G:{if(w>>>0<2){f=j;break G}j=V-5|0;H[l+224>>2]=j;c=I[b|0];f=c>>>0>143;H[l+248>>2]=f;m=v&31;if((v&63)>>>0>=32){U=c<<m;s=0}else{U=(1<<m)-1&c>>>32-m;s=c<<m}n=s|n;H[l+232>>2]=n;q=q|U;H[l+236>>2]=q;v=(g>>>0<=143?8:(c&127)==127?7:8)+v|0;H[l+240>>2]=v;if((w|0)==2){c=b;b=j;break G}g=V-6|0;H[l+224>>2]=g;b=I[j|0];m=b;f=b>>>0>143;H[l+248>>2]=f;h=v&31;if((v&63)>>>0>=32){x=b<<h;s=0}else{x=(1<<h)-1&b>>>32-h;s=b<<h}n=s|n;H[l+232>>2]=n;q=q|x;H[l+236>>2]=q;v=(c>>>0<=143?8:(b&127)==127?7:8)+v|0;H[l+240>>2]=v;if((w|0)==3){c=j;b=g;break G}b=V-7|0;H[l+224>>2]=b;c=I[g|0];f=c>>>0>143;H[l+248>>2]=f;j=v&31;if((v&63)>>>0>=32){U=c<<j;j=0}else{U=(1<<j)-1&c>>>32-j;j=c<<j}n=j|n;j=q|U;q=j;H[l+232>>2]=n;H[l+236>>2]=j;v=(m>>>0<=143?8:(c&127)==127?7:8)+v|0;H[l+240>>2]=v;c=g}g=u-w|0;H[l+244>>2]=g;if(v>>>0>32){break E}if((g|0)>=4){j=H[c-4>>2];H[l+224>>2]=c-5;H[l+244>>2]=g-4;break F}if((g|0)<=0){j=0;break F}u=g&1;H:{if((w|0)==(i-3|0)){h=24;j=0;break H}V=g&2147483646;h=24;j=0;c=b;w=0;while(1){x=c-1|0;H[l+224>>2]=x;m=I[c|0];b=c-2|0;H[l+224>>2]=b;H[l+244>>2]=g-1;c=I[x|0];g=g-2|0;H[l+244>>2]=g;j=m<<h|j|c<<h-8;h=h-16|0;c=b;w=w+2|0;if((V|0)!=(w|0)){continue}break}}if(!u){break F}H[l+224>>2]=b-1;b=I[b|0];H[l+244>>2]=g-1;j=b<<h|j}h=j&255;H[l+248>>2]=h>>>0>143;g=f?(j&2130706432)==2130706432?7:8:8;c=g+(j>>>0<=2415919103?8:(j&8323072)==8323072?7:8)|0;m=j>>>16&255;b=c+(m>>>0<=143?8:(j&32512)==32512?7:8)|0;x=j>>>8&255;H[l+240>>2]=b+((x>>>0<=143?8:(j&127)==127?7:8)+v|0);b=m<<g|j>>>24|x<<c|h<<b;c=v&31;if((v&63)>>>0>=32){x=b<<c;b=0}else{x=(1<<c)-1&b>>>32-c;b=b<<c}H[l+232>>2]=b|n;H[l+236>>2]=q|x}jc(l+192|0,z,r-i|0,255);V=0;I:{if(P>>>0<2){break I}jc(l+160|0,T,k,0);V=0;if((P|0)==2){break I}n=0;q=0;f=0;H[l+152>>2]=1;H[l+144>>2]=0;H[l+136>>2]=0;H[l+140>>2]=0;b=k-1|0;H[l+148>>2]=b;c=(r+z|0)+k|0;g=c-1|0;H[l+128>>2]=g;m=g&3;J:{if((k|0)<=0){c=g;break J}c=c-2|0;H[l+128>>2]=c;n=I[g|0]}H[l+136>>2]=n;H[l+140>>2]=0;h=n>>>0>143;H[l+152>>2]=h;v=(n&127)==127?7:8;H[l+144>>2]=v;K:{if(!m){break K}r=k-2|0;H[l+148>>2]=r;L:{if((k|0)<2){j=c;break L}j=c-1|0;H[l+128>>2]=j;f=I[c|0]}h=f>>>0>143;H[l+152>>2]=h;c=v&31;if((v&63)>>>0>=32){i=f<<c;c=0}else{i=(1<<c)-1&f>>>32-c;c=f<<c}q=c|n;H[l+136>>2]=q;c=i;H[l+140>>2]=c;v=(n>>>0<=143?8:(f&127)==127?7:8)+v|0;H[l+144>>2]=v;if((m|0)==1){c=j;n=q;q=i;k=b;b=r;break K}i=k-3|0;H[l+148>>2]=i;M:{if((k|0)<3){g=j;break M}g=j-1|0;H[l+128>>2]=g;o=I[j|0]}h=o>>>0>143;H[l+152>>2]=h;b=v&31;if((v&63)>>>0>=32){U=o<<b;b=0}else{U=(1<<b)-1&o>>>32-b;b=o<<b}n=b|q;b=c|U;q=b;H[l+136>>2]=n;H[l+140>>2]=b;v=(f>>>0<=143?8:(o&127)==127?7:8)+v|0;H[l+144>>2]=v;if((m|0)==2){c=g;k=r;b=i;break K}b=k-4|0;H[l+148>>2]=b;f=0;N:{if((k|0)<4){c=g;break N}c=g-1|0;H[l+128>>2]=c;f=I[g|0]}h=f>>>0>143;H[l+152>>2]=h;g=v&31;if((v&63)>>>0>=32){x=f<<g;g=0}else{x=(1<<g)-1&f>>>32-g;g=f<<g}n=g|n;g=q|x;q=g;H[l+136>>2]=n;H[l+140>>2]=g;v=(o>>>0<=143?8:(f&127)==127?7:8)+v|0;H[l+144>>2]=v;k=i}if(v>>>0<=32){O:{if((k|0)>=5){j=H[c-3>>2];H[l+148>>2]=k-5;H[l+128>>2]=c-4;break O}j=0;if((k|0)<2){break O}k=24;while(1){f=c-1|0;H[l+128>>2]=f;c=I[c|0];g=b-1|0;H[l+148>>2]=g;j=c<<k|j;i=b>>>0>1;c=f;k=k-8|0;b=g;if(i){continue}break}}i=j&255;H[l+152>>2]=i>>>0>143;g=h?(j&2130706432)==2130706432?7:8:8;c=g+(j>>>0<=2415919103?8:(j&8323072)==8323072?7:8)|0;k=j>>>16&255;b=c+(k>>>0<=143?8:(j&32512)==32512?7:8)|0;f=j>>>8&255;H[l+144>>2]=b+((f>>>0<=143?8:(j&127)==127?7:8)+v|0);b=k<<g|j>>>24|f<<c|i<<b;c=v&31;if((v&63)>>>0>=32){i=b<<c;b=0}else{i=(1<<c)-1&b>>>32-c;b=b<<c}H[l+136>>2]=b|n;H[l+140>>2]=i|q}V=1}ca=t-W|0;X=S+1|0;F[$+2112|0]=0;o=$+2112|0;g=$a(l+256|0);if((C|0)>0){T=ua-1|0;c=$;f=o;b=ia;z=0;while(1){r=z;m=J[(p<<8|(ob(l+224|0)&127)<<1)+16656>>1];P:{if(p){break P}j=g-2|0;m=(j|0)==-1?m:0;if((g|0)>1){g=j;break P}g=$a(l+256|0)}q=H[l+236>>2];n=H[l+232>>2];j=H[l+240>>2];t=m>>>4|0;u=H[c>>2]|(t&3|m>>>2&48)<<_;H[c>>2]=u;h=m&16;p=m>>>5&7|h>>>4;k=j;j=m&7;z=k-j|0;n=((1<<j)-1&q)<<32-j|n>>>j;q=q>>>j|0;k=n;j=0;if((C|0)>(r|2)){j=J[(p<<8|(k&127)<<1)+16656>>1];Q:{if(p){break Q}k=g-2|0;j=(k|0)==-1?j:0;if((g|0)>1){g=k;break Q}g=$a(l+256|0)}k=j&7;z=z-k|0;p=j>>>4&1|j>>>5&7;n=((1<<k)-1&q)<<32-k|n>>>k;q=q>>>k|0;k=n}H[c>>2]=u|(j<<2&768|j&48)<<_+4;v=j>>>2&2|m>>>3&1;R:{if((v|0)!=3){break R}i=g-2|0;v=(i|0)==-1?4:3;if((g|0)>1){g=i;break R}g=$a(l+256|0)}S:{if(!v){H[l+120>>2]=1;H[l+124>>2]=1;k=0;break S}if(v>>>0<=2){i=I[(k&7)+20804|0];u=i>>>2&7;x=i&3;i=(((-1<<u^-1)&k>>>x)+(i>>>5|0)|0)+1|0;k=(v|0)==1;H[l+124>>2]=k?1:i;H[l+120>>2]=k?i:1;k=u+x|0;break S}i=k;k=I[(k&7)+20804|0];W=k&3;i=i>>>W|0;if((v|0)==3){v=(k>>>5|0)+1|0;if((W|0)==3){H[l+124>>2]=i&1|2;k=k>>>2&7;H[l+120>>2]=v+((-1<<k^-1)&i>>>1);k=k+4|0;break S}u=I[(i&7)+20804|0];x=u&3;i=i>>>x|0;w=k>>>2&7;H[l+120>>2]=v+(i&(-1<<w^-1));k=u>>>2&7;H[l+124>>2]=(((-1<<k^-1)&i>>>w)+(u>>>5|0)|0)+1;k=k+(x+(w+W|0)|0)|0;break S}u=I[(i&7)+20804|0];x=u&3;i=i>>>x|0;w=k>>>2&7;H[l+120>>2]=((i&(-1<<w^-1))+(k>>>5|0)|0)+3;k=u>>>2&7;H[l+124>>2]=(((-1<<k^-1)&i>>>w)+(u>>>5|0)|0)+3;k=k+(w+(x+W|0)|0)|0}T:{w=H[l+120>>2];if(w>>>0<=X>>>0){u=H[l+124>>2];if(u>>>0<=X>>>0){break T}}if(ba){j=0;Ca(R,1,15756,0);break j}j=0;Ca(R,1,15756,0);break j}H[l+240>>2]=z-k;i=k&31;if((k&63)>>>0>=32){x=0;q=q>>>i|0}else{x=q>>>i|0;q=((1<<i)-1&q)<<32-i|n>>>i}H[l+232>>2]=q;H[l+236>>2]=x;z=r+4|0;q=(z|0)<=(C|0)?255:255>>>(z-C<<1)|0;W=(ca|0)>1?q:q&85;if((j&240|t&15)&(W^-1)){if(ba){j=0;Ca(R,1,12194,0);break j}j=0;Ca(R,1,12194,0);break j}U:{V:{if(h){n=Na(l+192|0);i=w+(m<<19>>31)|0;H[l+208>>2]=H[l+208>>2]-i;k=H[l+204>>2];q=H[l+200>>2];h=i&31;if((i&63)>>>0>=32){x=0;q=k>>>h|0}else{x=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}H[l+200>>2]=q;H[l+204>>2]=x;v=(n&(-1<<i^-1)|(m>>>8&1)<<i|1)+2<<T|n<<31;break V}v=0;if(!(W&1)){break U}}H[b>>2]=v}W:{if(m&32){n=Na(l+192|0);i=w+(m<<18>>31)|0;H[l+208>>2]=H[l+208>>2]-i;k=H[l+204>>2];q=H[l+200>>2];h=i&31;if((i&63)>>>0>=32){x=0;q=k>>>h|0}else{x=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}H[l+200>>2]=q;H[l+204>>2]=x;q=n&(-1<<i^-1)|(m>>>9&1)<<i|1;H[(C<<2)+b>>2]=q+2<<T|n<<31;n=32-Q(q)|0;q=I[f|0]&127;F[f|0]=(n>>>0>q>>>0?n:q)|128;break W}if(!(W&2)){break W}H[(C<<2)+b>>2]=0}i=b+4|0;X:{Y:{if(m&64){n=Na(l+192|0);h=w+(m<<17>>31)|0;H[l+208>>2]=H[l+208>>2]-h;k=H[l+204>>2];q=H[l+200>>2];t=h&31;if((h&63)>>>0>=32){x=0;q=k>>>t|0}else{x=k>>>t|0;q=((1<<t)-1&k)<<32-t|q>>>t}H[l+200>>2]=q;H[l+204>>2]=x;v=(n&(-1<<h^-1)|(m>>>10&1)<<h|1)+2<<T|n<<31;break Y}v=0;if(!(W&4)){break X}}H[i>>2]=v}F[f+1|0]=0;Z:{if(m&128){n=Na(l+192|0);h=w+(m<<16>>31)|0;H[l+208>>2]=H[l+208>>2]-h;k=H[l+204>>2];q=H[l+200>>2];t=h&31;if((h&63)>>>0>=32){x=0;q=k>>>t|0}else{x=k>>>t|0;q=((1<<t)-1&k)<<32-t|q>>>t}H[l+200>>2]=q;H[l+204>>2]=x;q=n&(-1<<h^-1)|(m>>>11&1)<<h|1;H[i+(C<<2)>>2]=q+2<<T|n<<31;F[f+1|0]=-96-Q(q);break Z}if(!(W&8)){break Z}H[i+(C<<2)>>2]=0}i=b+8|0;_:{$:{if(j&16){n=Na(l+192|0);m=u+(j<<19>>31)|0;H[l+208>>2]=H[l+208>>2]-m;k=H[l+204>>2];q=H[l+200>>2];h=m&31;if((m&63)>>>0>=32){x=0;q=k>>>h|0}else{x=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}H[l+200>>2]=q;H[l+204>>2]=x;v=(n&(-1<<m^-1)|(j>>>8&1)<<m|1)+2<<T|n<<31;break $}v=0;if(!(W&16)){break _}}H[i>>2]=v}aa:{if(j&32){n=Na(l+192|0);m=u+(j<<18>>31)|0;H[l+208>>2]=H[l+208>>2]-m;k=H[l+204>>2];q=H[l+200>>2];h=m&31;if((m&63)>>>0>=32){x=0;q=k>>>h|0}else{x=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}H[l+200>>2]=q;H[l+204>>2]=x;q=n&(-1<<m^-1)|(j>>>9&1)<<m|1;H[i+(C<<2)>>2]=q+2<<T|n<<31;n=32-Q(q)|0;q=I[f+1|0]&127;F[f+1|0]=(n>>>0>q>>>0?n:q)|128;break aa}if(!(W&32)){break aa}H[i+(C<<2)>>2]=0}i=b+12|0;ba:{ca:{if(j&64){n=Na(l+192|0);m=u+(j<<17>>31)|0;H[l+208>>2]=H[l+208>>2]-m;k=H[l+204>>2];q=H[l+200>>2];h=m&31;if((m&63)>>>0>=32){x=0;q=k>>>h|0}else{x=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}H[l+200>>2]=q;H[l+204>>2]=x;v=(n&(-1<<m^-1)|(j>>>10&1)<<m|1)+2<<T|n<<31;break ca}v=0;if(!(W&64)){break ba}}H[i>>2]=v}f=f+2|0;F[f|0]=0;da:{if(j&128){n=Na(l+192|0);m=u+(j<<16>>31)|0;H[l+208>>2]=H[l+208>>2]-m;k=H[l+204>>2];q=H[l+200>>2];h=m&31;if((m&63)>>>0>=32){x=0;q=k>>>h|0}else{x=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}H[l+200>>2]=q;H[l+204>>2]=x;j=n&(-1<<m^-1)|(j>>>11&1)<<m|1;H[i+(C<<2)>>2]=j+2<<T|n<<31;F[f|0]=-96-Q(j);break da}if(W>>>0<128){break da}H[i+(C<<2)>>2]=0}_=_^16;c=(r&4)+c|0;b=b+16|0;if((z|0)<(C|0)){continue}break}}ma=Z&8;ka=$+1584|0;la=$+1056|0;fa=$+528|0;if((ca|0)>=3){na=N(C,12);s=C<<3;ea=ua-1|0;b=ua-2|0;A=3<<b;D=1<<b;ha=(C+7>>>1&2147483644)+4|0;u=2;while(1){Z=u;v=I[o|0];F[o|0]=0;_=_&-17^2;ea:{if((C|0)<=0){u=u+2|0;break ea}p=Z&4?fa:$;u=Z+2|0;f=ia+(N(C,Z)<<2)|0;t=0;b=o;r=0;while(1){h=r;v=v&255;c=I[b+1|0]>>>5&4|(v>>>7|t);m=J[(c<<8|(ob(l+224|0)&127)<<1)+18704>>1];fa:{if(c){break fa}c=g-2|0;m=(c|0)==-1?m:0;if((g|0)>1){g=c;break fa}g=$a(l+256|0)}j=H[l+236>>2];q=H[l+232>>2];c=H[l+240>>2];k=H[p>>2]|(m>>>4&3|m>>>2&48)<<_;H[p>>2]=k;T=m&64;W=m&128;t=T>>>5|W>>>6;n=c;c=m&7;w=n-c|0;n=((1<<c)-1&j)<<32-c|q>>>c;q=j>>>c|0;r=n;j=0;if((C|0)>(h|2)){c=I[b+2|0]>>>5&4|I[b+1|0]>>>7|t;j=J[(c<<8|(n&127)<<1)+18704>>1];ga:{if(c){break ga}c=g-2|0;j=(c|0)==-1?j:0;if((g|0)>1){g=c;break ga}g=$a(l+256|0)}c=j&7;w=w-c|0;t=(j>>>5|j>>>6)&2;n=((1<<c)-1&q)<<32-c|n>>>c;r=n;q=q>>>c|0}H[p>>2]=k|(j<<2&768|j&48)<<_+4;k=1;c=1;ha:{ia:{ja:{z=j>>>2&2|m>>>3&1;switch(z|0){case 0:break ha;case 3:break ia;default:break ja}}c=I[(r&7)+20804|0];x=c>>>2&7;k=r;r=c&3;i=(((-1<<x^-1)&k>>>r)+(c>>>5|0)|0)+1|0;c=(z|0)==1;k=c?1:i;c=c?i:1;z=r+x|0;break ha}U=I[(r&7)+20804|0];k=U&3;c=r>>>k|0;S=I[(c&7)+20804|0];x=S&3;i=S>>>2&7;r=U>>>2&7;z=i+(r+(k+x|0)|0)|0;k=c>>>x|0;c=((k&(-1<<r^-1))+(U>>>5|0)|0)+1|0;k=(((-1<<i^-1)&k>>>r)+(S>>>5|0)|0)+1|0}H[l+240>>2]=w-z;i=z&31;if((z&63)>>>0>=32){x=0;q=q>>>i|0}else{x=q>>>i|0;q=((1<<i)-1&q)<<32-i|n>>>i}H[l+232>>2]=q;H[l+236>>2]=x;r=m&240;if(r-1&r){n=c;q=v&127;c=I[b+1|0]&127;q=c>>>0<q>>>0?q:c;c=q-2|0;c=n+(c>>>0<=q>>>0?c:0)|0}i=j&240;if(i-1&i){n=I[b+1|0]&127;q=I[b+2|0]&127;q=n>>>0>q>>>0?n:q;k=(q>>>0>2?q-2|0:0)+k|0}if(!(c>>>0<=X>>>0&k>>>0<=X>>>0)){if(ba){j=0;Ca(R,1,15856,0);break j}j=0;Ca(R,1,15856,0);break j}v=I[b+2|0];F[b+1|0]=0;F[b+2|0]=0;n=i|r>>>4;r=h+4|0;q=(r|0)<=(C|0)?255:255>>>(r-C<<1)|0;S=(u|0)>(ca|0)?q&85:q;if(n&(S^-1)){if(ba){j=0;Ca(R,1,12194,0);break j}j=0;Ca(R,1,12194,0);break j}ka:{la:{if(m&16){n=Na(l+192|0);w=(m<<19>>31)+c|0;H[l+208>>2]=H[l+208>>2]-w;i=H[l+204>>2];q=H[l+200>>2];z=w&31;if((w&63)>>>0>=32){x=0;q=i>>>z|0}else{x=i>>>z|0;q=((1<<z)-1&i)<<32-z|q>>>z}H[l+200>>2]=q;H[l+204>>2]=x;w=(n&(-1<<w^-1)|(m>>>8&1)<<w|1)+2<<ea|n<<31;break la}w=0;if(!(S&1)){break ka}}H[f>>2]=w}ma:{if(m&32){n=Na(l+192|0);w=(m<<18>>31)+c|0;H[l+208>>2]=H[l+208>>2]-w;i=H[l+204>>2];q=H[l+200>>2];z=w&31;if((w&63)>>>0>=32){x=0;q=i>>>z|0}else{x=i>>>z|0;q=((1<<z)-1&i)<<32-z|q>>>z}H[l+200>>2]=q;H[l+204>>2]=x;q=n&(-1<<w^-1)|(m>>>9&1)<<w|1;H[(C<<2)+f>>2]=q+2<<ea|n<<31;n=32-Q(q)|0;q=I[b|0]&127;F[b|0]=(n>>>0>q>>>0?n:q)|128;break ma}if(!(S&2)){break ma}H[(C<<2)+f>>2]=0}w=f+4|0;na:{oa:{if(T){n=Na(l+192|0);z=(m<<17>>31)+c|0;H[l+208>>2]=H[l+208>>2]-z;i=H[l+204>>2];q=H[l+200>>2];T=z&31;if((z&63)>>>0>=32){x=0;q=i>>>T|0}else{x=i>>>T|0;q=((1<<T)-1&i)<<32-T|q>>>T}H[l+200>>2]=q;H[l+204>>2]=x;Y=(n&(-1<<z^-1)|(m>>>10&1)<<z|1)+2<<ea|n<<31;break oa}Y=0;if(!(S&4)){break na}}H[w>>2]=Y}pa:{if(W){q=Na(l+192|0);i=(m<<16>>31)+c|0;H[l+208>>2]=H[l+208>>2]-i;n=H[l+204>>2];c=H[l+200>>2];z=i&31;if((i&63)>>>0>=32){x=0;c=n>>>z|0}else{x=n>>>z|0;c=((1<<z)-1&n)<<32-z|c>>>z}H[l+200>>2]=c;H[l+204>>2]=x;c=q&(-1<<i^-1)|(m>>>11&1)<<i|1;H[w+(C<<2)>>2]=c+2<<ea|q<<31;F[b+1|0]=-96-Q(c);break pa}if(!(S&8)){break pa}H[w+(C<<2)>>2]=0}i=f+8|0;qa:{ra:{if(j&16){q=Na(l+192|0);m=(j<<19>>31)+k|0;H[l+208>>2]=H[l+208>>2]-m;n=H[l+204>>2];c=H[l+200>>2];w=m&31;if((m&63)>>>0>=32){x=0;c=n>>>w|0}else{x=n>>>w|0;c=((1<<w)-1&n)<<32-w|c>>>w}H[l+200>>2]=c;H[l+204>>2]=x;c=(q&(-1<<m^-1)|(j>>>8&1)<<m|1)+2<<ea|q<<31;break ra}c=0;if(!(S&16)){break qa}}H[i>>2]=c}sa:{if(j&32){q=Na(l+192|0);m=(j<<18>>31)+k|0;H[l+208>>2]=H[l+208>>2]-m;n=H[l+204>>2];c=H[l+200>>2];w=m&31;if((m&63)>>>0>=32){x=0;c=n>>>w|0}else{x=n>>>w|0;c=((1<<w)-1&n)<<32-w|c>>>w}H[l+200>>2]=c;H[l+204>>2]=x;c=q&(-1<<m^-1)|(j>>>9&1)<<m|1;H[i+(C<<2)>>2]=c+2<<ea|q<<31;q=32-Q(c)|0;c=I[b+1|0]&127;F[b+1|0]=(c>>>0<q>>>0?q:c)|128;break sa}if(!(S&32)){break sa}H[i+(C<<2)>>2]=0}i=f+12|0;ta:{ua:{if(j&64){q=Na(l+192|0);m=(j<<17>>31)+k|0;H[l+208>>2]=H[l+208>>2]-m;n=H[l+204>>2];c=H[l+200>>2];w=m&31;if((m&63)>>>0>=32){x=0;c=n>>>w|0}else{x=n>>>w|0;c=((1<<w)-1&n)<<32-w|c>>>w}H[l+200>>2]=c;H[l+204>>2]=x;c=(q&(-1<<m^-1)|(j>>>10&1)<<m|1)+2<<ea|q<<31;break ua}c=0;if(!(S&64)){break ta}}H[i>>2]=c}b=b+2|0;va:{if(j&128){q=Na(l+192|0);k=(j<<16>>31)+k|0;H[l+208>>2]=H[l+208>>2]-k;n=H[l+204>>2];c=H[l+200>>2];m=k&31;if((k&63)>>>0>=32){x=0;c=n>>>m|0}else{x=n>>>m|0;c=((1<<m)-1&n)<<32-m|c>>>m}H[l+200>>2]=c;H[l+204>>2]=x;c=q&(-1<<k^-1)|(j>>>11&1)<<k|1;H[i+(C<<2)>>2]=c+2<<ea|q<<31;F[b|0]=-96-Q(c);break va}if(S>>>0<128){break va}H[i+(C<<2)>>2]=0}_=_^16;p=(h&4)+p|0;f=f+16|0;if((r|0)<(C|0)){continue}break}}wa:{if(!(Z&2)|P>>>0<2){break wa}p=u&4;xa:{ya:{za:{Aa:{Ba:{if(V){w=p?$:fa;z=0;if((C|0)<=0){break Ba}q=ia+(N(C,Z-2|0)<<2)|0;while(1){j=ob(l+128|0);m=0;f=H[w>>2];if(f){m=q+(z<<2)|0;k=0;b=15;while(1){Ca:{if(!(b&f)){break Ca}n=b&286331153;if(n&f){H[m>>2]=D|H[m>>2]^((j^-1)&1)<<ea;j=j>>>1|0}if(f&n<<1){c=(C<<2)+m|0;H[c>>2]=D|H[c>>2]^((j^-1)&1)<<ea;j=j>>>1|0}if(f&n<<2){c=m+s|0;H[c>>2]=D|H[c>>2]^((j^-1)&1)<<ea;j=j>>>1|0}if(!(f&n<<3)){break Ca}c=m+na|0;H[c>>2]=D|H[c>>2]^((j^-1)&1)<<ea;j=j>>>1|0}m=m+4|0;b=b<<4;k=k+1|0;if((k|0)!=8){continue}break}m=Me(f)}w=w+4|0;H[l+144>>2]=H[l+144>>2]-m;c=H[l+140>>2];b=H[l+136>>2];j=m&31;if((m&63)>>>0>=32){x=0;b=c>>>j|0}else{x=c>>>j|0;b=((1<<j)-1&c)<<32-j|b>>>j}H[l+136>>2]=b;H[l+140>>2]=x;z=z+8|0;if((C|0)>(z|0)){continue}break}}c=0;j=0;Y=p?la:ka;m=Y;w=p?$:fa;b=w;if((C|0)>0){break za}b=!p;break Aa}Y=p?la:ka;b=!p}if(Z>>>0<=5){break wa}h=b?$:fa;if((C|0)<=0){break xa}b=b?la:ka;break ya}while(1){q=j>>>28|0;j=H[b>>2];q=j|(q|j<<4|j>>>4);H[m>>2]=q;q=q|H[b+4>>2]<<28;H[m>>2]=(q>>>1&2004318071|q<<1&-286331154|q)&(j^-1);m=m+4|0;b=b+4|0;c=c+8|0;if((C|0)>(c|0)){continue}break}if(Z>>>0<6){break wa}h=p?fa:$;b=p?ka:la}k=0;p=0;m=w;v=b;j=b;b=h;while(1){q=m+4|0;c=H[j>>2];n=H[m>>2];if(!ma){c=c|(n|(n<<4|p>>>28|n>>>4|H[q>>2]<<28))<<3&-2004318072}H[j>>2]=(H[b>>2]^-1)&c;b=b+4|0;j=j+4|0;p=n;m=q;k=k+8|0;if((C|0)>(k|0)){continue}break}if((C|0)<=0){break xa}T=ia+(N(C,Z-6|0)<<2)|0;pa=0;p=h;while(1){f=0;b=H[v>>2];if(b){W=pa|4;Z=C-pa|0;i=(pa<<2)+T|0;j=0;t=0;while(1){q=j;j=Na(l+160|0);S=(C|0)>(t+W|0)?t+4|0:Z;Da:{if((S|0)<=(t|0)){m=0;break Da}da=H[p>>2]^-1;x=t<<2;z=i+x|0;m=0;k=t;r=15<<x;c=r;while(1){Ea:{if(!(b&c)){break Ea}U=c&286331153;if(U&b){if(j&1){f=f|U;b=da&50<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=U<<1;if(n&b){if(j&1){f=f|n;b=da&116<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=U<<2;if(n&b){if(j&1){f=f|n;b=da&232<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=U<<3;if(!(n&b)){break Ea}if(j&1){f=f|n;b=da&192<<(k<<2)|b}m=m+1|0;j=j>>>1|0}c=c<<4;k=k+1|0;if((S|0)>(k|0)){continue}break}if(!(f>>>x&65535)){break Da}while(1){Fa:{if(!(f&r)){break Fa}n=r&286331153;if(n&f){H[z>>2]=A|(H[z>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<1&f){c=(C<<2)+z|0;H[c>>2]=A|(H[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<2&f){c=s+z|0;H[c>>2]=A|(H[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(!(n<<3&f)){break Fa}c=z+na|0;H[c>>2]=A|(H[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}r=r<<4;z=z+4|0;t=t+1|0;if((S|0)>(t|0)){continue}break}}H[l+176>>2]=H[l+176>>2]-m;j=H[l+172>>2];c=H[l+168>>2];n=m&31;if((m&63)>>>0>=32){x=0;c=j>>>n|0}else{x=j>>>n|0;c=((1<<n)-1&j)<<32-n|c>>>n}H[l+168>>2]=c;H[l+172>>2]=x;j=1;t=4;if(!(q&1)){continue}break}H[v+4>>2]=H[v+4>>2]|(f>>>27&14|f>>>29|f>>>28)&(H[p+4>>2]^-1)}j=H[p>>2]|f;q=j>>>3&286331153;c=q>>>4|q<<4|q;if(pa){b=Y-4|0;H[b>>2]=H[b>>2]|(H[w-4>>2]^-1)&q<<28}H[Y>>2]=H[Y>>2]|c&(H[w>>2]^-1);H[Y+4>>2]=H[Y+4>>2]|(H[w+4>>2]^-1)&j>>>31;v=v+4|0;p=p+4|0;Y=Y+4|0;w=w+4|0;pa=pa+8|0;if((C|0)>(pa|0)){continue}break}}if(!ha){break wa}y(h,0,ha)}if((u|0)<(ca|0)){continue}break}}Ga:{if(P>>>0<2){break Ga}f=(ca&3)-1|0;Ha:{if(V&f>>>0<2){if((C|0)<=0){break Ha}r=1<<ua-2;n=ia+(N(C,ca&16777212)<<2)|0;w=ca&4?fa:$;q=N(C,12);g=C<<3;i=ua-1|0;t=0;while(1){j=ob(l+128|0);m=0;o=H[w>>2];if(o){m=n+(t<<2)|0;b=15;k=0;while(1){Ia:{if(!(b&o)){break Ia}p=b&286331153;if(p&o){H[m>>2]=r|H[m>>2]^((j^-1)&1)<<i;j=j>>>1|0}if(o&p<<1){c=(C<<2)+m|0;H[c>>2]=r|H[c>>2]^((j^-1)&1)<<i;j=j>>>1|0}if(o&p<<2){c=g+m|0;H[c>>2]=r|H[c>>2]^((j^-1)&1)<<i;j=j>>>1|0}if(!(o&p<<3)){break Ia}c=m+q|0;H[c>>2]=r|H[c>>2]^((j^-1)&1)<<i;j=j>>>1|0}m=m+4|0;b=b<<4;k=k+1|0;if((k|0)!=8){continue}break}m=Me(o)}w=w+4|0;H[l+144>>2]=H[l+144>>2]-m;c=H[l+140>>2];b=H[l+136>>2];j=m&31;if((m&63)>>>0>=32){x=0;b=c>>>j|0}else{x=c>>>j|0;b=((1<<j)-1&c)<<32-j|b>>>j}H[l+136>>2]=b;H[l+140>>2]=x;t=t+8|0;if((C|0)>(t|0)){continue}break}}if((C|0)<=0|f>>>0>1){break Ha}b=ca&4;m=b?fa:$;b=b?ka:la;c=0;j=0;while(1){g=j>>>28|0;j=H[m>>2];g=j|(g|j<<4|j>>>4);H[b>>2]=g;g=g|H[m+4>>2]<<28;H[b>>2]=(g>>>1&2004318071|g<<1&-286331154|g)&(j^-1);b=b+4|0;m=m+4|0;c=c+8|0;if((C|0)>(c|0)){continue}break}}P=(ca|0)>6?(ca-(ca+1&3)|0)-3|0:0;if((ca|0)<=(P|0)){break Ga}W=N(C,12);t=C<<3;s=3<<ua-2;S=(C|0)<=0;while(1){c=ca-P|0;b=c-1|0;Ja:{Ka:{La:{if(b>>>0>=3){_=-1;if((c|0)<5){break La}if(S){break Ja}c=P&4;m=c?fa:$;j=c?ka:la;b=0;if(!ma){b=c?$:fa;c=0;f=0;while(1){g=f>>>28|0;_=-1;f=H[b>>2];H[j>>2]=(H[j>>2]|(f|(g|f<<4|f>>>4|H[b+4>>2]<<28))<<3&-2004318072)&(H[m>>2]^-1);m=m+4|0;j=j+4|0;b=b+4|0;c=c+8|0;if((C|0)>(c|0)){continue}break}break Ka}while(1){_=-1;H[j>>2]=H[j>>2]&(H[m>>2]^-1);m=m+4|0;j=j+4|0;b=b+8|0;if((C|0)>(b|0)){continue}break}break Ka}_=H[(b<<2)+20812>>2]}if(S){break Ja}}b=P&4;r=b?fa:$;p=b?ka:la;g=b?$:fa;v=b?la:ka;u=ia+(N(C,P)<<2)|0;h=0;while(1){f=0;b=H[p>>2]&_;if(b){V=h|4;Z=C-h|0;o=u+(h<<2)|0;j=0;i=0;while(1){q=j;j=Na(l+160|0);T=(C|0)>(i+V|0)?i+4|0:Z;Ma:{if((T|0)<=(i|0)){m=0;break Ma}x=i<<2;z=x+o|0;ha=(H[r>>2]^-1)&_;m=0;k=i;w=15<<x;c=w;while(1){Na:{if(!(b&c)){break Na}U=c&286331153;if(U&b){if(j&1){f=f|U;b=ha&50<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=U<<1;if(n&b){if(j&1){f=f|n;b=ha&116<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=U<<2;if(n&b){if(j&1){f=f|n;b=ha&232<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=U<<3;if(!(n&b)){break Na}if(j&1){f=f|n;b=ha&192<<(k<<2)|b}m=m+1|0;j=j>>>1|0}c=c<<4;k=k+1|0;if((T|0)>(k|0)){continue}break}if(!(f>>>x&65535)){break Ma}while(1){Oa:{if(!(f&w)){break Oa}n=w&286331153;if(n&f){H[z>>2]=s|(H[z>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<1&f){c=(C<<2)+z|0;H[c>>2]=s|(H[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<2&f){c=t+z|0;H[c>>2]=s|(H[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(!(n<<3&f)){break Oa}c=z+W|0;H[c>>2]=s|(H[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}w=w<<4;z=z+4|0;i=i+1|0;if((T|0)>(i|0)){continue}break}}H[l+176>>2]=H[l+176>>2]-m;j=H[l+172>>2];c=H[l+168>>2];n=m&31;if((m&63)>>>0>=32){x=0;c=j>>>n|0}else{x=j>>>n|0;c=((1<<n)-1&j)<<32-n|c>>>n}H[l+168>>2]=c;H[l+172>>2]=x;j=1;i=4;if(!(q&1)){continue}break}H[p+4>>2]=H[p+4>>2]|(f>>>27&14|f>>>29|f>>>28)&(H[r+4>>2]^-1)}j=H[r>>2]|f;q=j>>>3&286331153;c=q>>>4|q<<4|q;if(h){b=v-4|0;H[b>>2]=H[b>>2]|(H[g-4>>2]^-1)&q<<28}H[v>>2]=H[v>>2]|c&(H[g>>2]^-1);H[v+4>>2]=H[v+4>>2]|(H[g+4>>2]^-1)&j>>>31;p=p+4|0;r=r+4|0;v=v+4|0;g=g+4|0;h=h+8|0;if((C|0)>(h|0)){continue}break}}P=P+4|0;if((ca|0)>(P|0)){continue}break}}j=1;if((ca|0)<=0|(C|0)<=0){break j}q=C&2147483644;n=C&3;g=M-ja>>>0>4294967292;p=0;while(1){j=ia+(N(p,C)<<2)|0;m=0;if(!g){while(1){c=H[j>>2];b=c&2147483647;H[j>>2]=(c|0)<0?0-b|0:b;c=H[j+4>>2];b=c&2147483647;H[j+4>>2]=(c|0)<0?0-b|0:b;c=H[j+8>>2];b=c&2147483647;H[j+8>>2]=(c|0)<0?0-b|0:b;c=H[j+12>>2];b=c&2147483647;H[j+12>>2]=(c|0)<0?0-b|0:b;j=j+16|0;m=m+4|0;if((q|0)!=(m|0)){continue}break}}m=0;if(n){while(1){c=H[j>>2];b=c&2147483647;H[j>>2]=(c|0)<0?0-b|0:b;j=j+4|0;m=m+1|0;if((n|0)!=(m|0)){continue}break}}j=1;p=p+1|0;if((ca|0)!=(p|0)){continue}break}break j}if(!ba){break v}H[l+52>>2]=H[E+24>>2];H[l+48>>2]=S;Ca(R,1,9686,l+48|0);break u}H[l+20>>2]=m;H[l+16>>2]=S;Ca(R,1,9686,l+16|0);j=0;break j}j=0}oa=l+304|0;if(j){break i}break b}H[e+108>>2]=(b<<9)+22336;c=0;b=H[e+116>>2];Pa:{Qa:{p=H[E+16>>2]-H[E+8>>2]|0;k=H[E+20>>2]-H[E+12>>2]|0;g=N(p,k);Ra:{Sa:{Ta:{if(g>>>0>K[e+132>>2]){Da(b);b=Ja(g<<2);H[e+116>>2]=b;if(!b){break Ra}H[e+132>>2]=g;break Ta}if(!b){break Sa}}g=g<<2;if(!g){break Sa}y(b,0,g)}b=H[e+120>>2];r=p+2|0;n=k+3>>>2|0;g=N(r,n+2|0);if(g>>>0<=K[e+136>>2]){z=g<<2;break Qa}Da(b);z=g<<2;b=Ja(z);H[e+120>>2]=b;if(b){break Qa}}b=0;break Pa}H[e+136>>2]=g;if(z){y(b,0,z)}Ua:{if(!r){break Ua}q=H[e+120>>2];b=q;l=p+1|0;if(l>>>0>=7){g=r&-8;while(1){H[b+24>>2]=1226833920;H[b+28>>2]=1226833920;H[b+16>>2]=1226833920;H[b+20>>2]=1226833920;H[b+8>>2]=1226833920;H[b+12>>2]=1226833920;H[b>>2]=1226833920;H[b+4>>2]=1226833920;b=b+32|0;c=c+8|0;if((g|0)!=(c|0)){continue}break}}g=r&7;if(g){c=0;while(1){H[b>>2]=1226833920;b=b+4|0;c=c+1|0;if((g|0)!=(c|0)){continue}break}}b=q+(N(r,n+1|0)<<2)|0;if(l>>>0>=7){g=r&-8;c=0;while(1){H[b+24>>2]=1226833920;H[b+28>>2]=1226833920;H[b+16>>2]=1226833920;H[b+20>>2]=1226833920;H[b+8>>2]=1226833920;H[b+12>>2]=1226833920;H[b>>2]=1226833920;H[b+4>>2]=1226833920;b=b+32|0;c=c+8|0;if((g|0)!=(c|0)){continue}break}}g=r&7;if(g){c=0;while(1){H[b>>2]=1226833920;b=b+4|0;c=c+1|0;if((g|0)!=(c|0)){continue}break}}b=k&3;if(!b){break Ua}g=(b|0)==1?1224736768:(b|0)==2?1207959552:1073741824;b=q+(N(n,r)<<2)|0;if(l>>>0>=7){c=r&-8;z=0;while(1){H[b+28>>2]=g;H[b+24>>2]=g;H[b+20>>2]=g;H[b+16>>2]=g;H[b+12>>2]=g;H[b+8>>2]=g;H[b+4>>2]=g;H[b>>2]=g;b=b+32|0;z=z+8|0;if((c|0)!=(z|0)){continue}break}}c=r&7;if(!c){break Ua}z=0;while(1){H[b>>2]=g;b=b+4|0;z=z+1|0;if((c|0)!=(z|0)){continue}break}}H[e+128>>2]=k;H[e+124>>2]=p;b=1}if(!b){break b}z=P+H[E+28>>2]|0;if((z|0)>=31){if(!ba){break h}H[aa+16>>2]=z;Ca(R,2,8716,aa+16|0);break b}ic(e);_a(e,18,46);_a(e,17,3);_a(e,0,4);if(H[E+64>>2]){break i}q=H[E+52>>2];Va:{if(!(q>>>0<=1&(!H[e+144>>2]|(q|0)!=1))){b=H[E+4>>2];g=0;if(q-1>>>0>=3){c=q&-4;while(1){l=(i<<3)+b|0;g=H[l+28>>2]+(H[l+20>>2]+(H[l+12>>2]+(H[l+4>>2]+g|0)|0)|0)|0;i=i+4|0;f=f+4|0;if((c|0)!=(f|0)){continue}break}}c=q&3;if(c){while(1){g=H[((i<<3)+b|0)+4>>2]+g|0;i=i+1|0;j=j+1|0;if((c|0)!=(j|0)){continue}break}}ja=H[e+148>>2];c=g+2|0;if(c>>>0>K[e+152>>2]){b=Ia(ja,c);if(!b){break b}H[e+148>>2]=b;b=b+g|0;F[b|0]=0;F[b+1|0]=0;H[e+152>>2]=c;ja=H[e+148>>2];if(!H[E+52>>2]){break Va}b=H[E+4>>2]}g=0;i=0;while(1){l=i<<3;c=l+b|0;b=H[c+4>>2];if(b){B(g+ja|0,H[c>>2],b)}b=H[E+4>>2];g=H[(l+b|0)+4>>2]+g|0;i=i+1|0;if(i>>>0<K[E+52>>2]){continue}break}break Va}if((q|0)!=1){break i}ja=H[H[E+4>>2]>>2]}b=H[E+60>>2];if(b){$=H[e+116>>2];H[e+116>>2]=b}if(H[E+44>>2]){ia=Z&2;ha=Z&8;da=e+28|0;V=!(Z&1);_=2;while(1){l=U+ja|0;ma=H[E>>2]+N(S,24)|0;c=H[ma>>2];na=V|((H[E+28>>2]-4|0)<(z|0)|_>>>0>1);Wa:{if(!na){H[e+20>>2]=l;b=c+l|0;H[e+24>>2]=b;G[e+112>>1]=I[b|0]|I[b+1|0]<<8;F[b|0]=255;F[H[e+24>>2]+1|0]=255;H[e+8>>2]=0;H[e>>2]=0;H[e+16>>2]=l;break Wa}H[e+20>>2]=l;b=c+l|0;H[e+24>>2]=b;G[e+112>>1]=I[b|0]|I[b+1|0]<<8;F[b|0]=255;F[H[e+24>>2]+1|0]=255;H[e+104>>2]=e+28;H[e+16>>2]=l;H[e+12>>2]=0;b=c?I[l|0]<<16:16711680;H[e>>2]=b;j=1;c=l+1|0;g=I[l+1|0];Xa:{if(I[l|0]==255){if(g>>>0>=144){H[e+12>>2]=1;b=b|65280;break Xa}H[e+16>>2]=c;j=0;b=b+(g<<9)|0;break Xa}H[e+16>>2]=c;b=b|g<<8}H[e+8>>2]=j;H[e+4>>2]=32768;H[e>>2]=b<<7}x=H[ma>>2];Ya:{if(!H[ma+8>>2]|(z|0)<=0){break Ya}T=0;P=na&(ia|0)!=0;while(1){Za:{_a:{$a:{switch(_-1|0){default:if(!na){b=1<<z;o=b>>>1|b;r=H[e+124>>2];n=r<<2;b=(n+H[e+120>>2]|0)+12|0;g=H[e+116>>2];m=0;c=H[e+128>>2];if(c>>>0>=4){if(!r){break Za}d=N(r,12);q=r<<3;f=0-o|0;while(1){c=0;while(1){l=b;b=H[b>>2];ab:{if(!b){break ab}if(!(!(b&495)|b&2097168)){b=H[e>>2];j=H[e+8>>2];bb:{if(j){break bb}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];cb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break cb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break bb}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;db:{if(!(b>>>j&1)){break db}eb:{if(j){break eb}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];fb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break fb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break eb}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;k=b>>>j&1;H[g>>2]=k?f:o;j=H[e+124>>2];b=l-4|0;H[b>>2]=H[b>>2]|32;H[l+4>>2]=H[l+4>>2]|8;H[l>>2]=H[l>>2]|k<<19|16;if(ha){break db}b=l+(-2-j<<2)|0;H[b+4>>2]=H[b+4>>2]|32768;H[b>>2]=H[b>>2]|k<<31|65536;b=b-4|0;H[b>>2]=H[b>>2]|131072}b=H[l>>2]|2097152;H[l>>2]=b}if(!(!(b&3960)|b&16777344)){b=H[e>>2];j=H[e+8>>2];gb:{if(j){break gb}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];hb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break hb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break gb}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;if(b>>>j&1){ib:{if(j){break ib}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];jb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break jb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break ib}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;j=b>>>j&1;H[g+n>>2]=j?f:o;b=l-4|0;H[b>>2]=H[b>>2]|256;H[l+4>>2]=H[l+4>>2]|64;b=H[l>>2]|j<<22|128}else{b=H[l>>2]}b=b|16777216;H[l>>2]=b}if(!(!(b&31680)|b&134218752)){b=H[e>>2];j=H[e+8>>2];kb:{if(j){break kb}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];lb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break lb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break kb}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;if(b>>>j&1){mb:{if(j){break mb}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];nb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break nb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break mb}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;j=b>>>j&1;H[g+q>>2]=j?f:o;b=l-4|0;H[b>>2]=H[b>>2]|2048;H[l+4>>2]=H[l+4>>2]|512;b=H[l>>2]|j<<25|1024}else{b=H[l>>2]}b=b|134217728;H[l>>2]=b}if(!(b&253440)|b&1073750016){break ab}b=H[e>>2];j=H[e+8>>2];ob:{if(j){break ob}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];pb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break pb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break ob}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;if(b>>>j&1){qb:{if(j){break qb}j=(b|0)==255;k=H[e+16>>2];b=I[k|0];rb:{if(!j){H[e>>2]=b;H[e+16>>2]=k+1;break rb}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=k+1;j=7;break qb}b=255;H[e>>2]=255}j=8}j=j-1|0;H[e+8>>2]=j;k=b>>>j&1;H[d+g>>2]=k?f:o;j=H[e+124>>2];b=l-4|0;H[b>>2]=H[b>>2]|16384;H[l+4>>2]=H[l+4>>2]|4096;H[l>>2]=H[l>>2]|k<<28|8192;b=l+(j<<2)|0;H[b+4>>2]=H[b+4>>2]|4;H[b+12>>2]=H[b+12>>2]|1;H[b+8>>2]=H[b+8>>2]|k<<18|2}H[l>>2]=H[l>>2]|1073741824}g=g+4|0;b=l+4|0;c=c+1|0;if((r|0)!=(c|0)){continue}break}g=d+g|0;b=l+12|0;m=m+4|0;c=H[e+128>>2];if(m>>>0<(c&-4)>>>0){continue}break}}if(!r|c>>>0<=m>>>0){break _a}v=0;q=0-o|0;j=c;while(1){sb:{if((j|0)==(m|0)){j=m;break sb}d=b-4|0;k=H[b>>2];i=0;while(1){p=N(i,3);l=k>>>p|0;if(!(l&2097168|!(l&495))){c=H[e>>2];f=H[e+8>>2];tb:{if(f){break tb}l=(c|0)!=255;j=H[e+16>>2];c=I[j|0];ub:{if(!l){if(c>>>0>=144){c=255;H[e>>2]=255;break ub}H[e>>2]=c;H[e+16>>2]=j+1;f=7;break tb}H[e>>2]=c;H[e+16>>2]=j+1}f=8}f=f-1|0;H[e+8>>2]=f;vb:{if(!(c>>>f&1)){break vb}j=(N(i,r)<<2)+g|0;wb:{if(f){break wb}l=(c|0)!=255;n=H[e+16>>2];c=I[n|0];xb:{if(!l){if(c>>>0>=144){c=255;H[e>>2]=255;break xb}H[e>>2]=c;H[e+16>>2]=n+1;f=7;break wb}H[e>>2]=c;H[e+16>>2]=n+1}f=8}l=f-1|0;H[e+8>>2]=l;k=j;j=c>>>l&1;H[k>>2]=j?q:o;l=H[e+124>>2];H[d>>2]=H[d>>2]|32<<p;H[b>>2]=H[b>>2]|(j<<19|16)<<p;H[b+4>>2]=H[b+4>>2]|8<<p;if(!(i|ha)){c=(-2-l<<2)+b|0;H[c+4>>2]=H[c+4>>2]|32768;H[c>>2]=H[c>>2]|j<<31|65536;c=c-4|0;H[c>>2]=H[c>>2]|131072}if((i|0)!=3){break vb}c=(l<<2)+b|0;H[c+4>>2]=H[c+4>>2]|4;H[c+12>>2]=H[c+12>>2]|1;H[c+8>>2]=H[c+8>>2]|j<<18|2}k=H[b>>2]|2097152<<p;H[b>>2]=k;c=H[e+128>>2]}j=c;i=i+1|0;if(i>>>0<c-m>>>0){continue}break}}g=g+4|0;b=b+4|0;v=v+1|0;if((r|0)!=(v|0)){continue}break}break _a}j=0;r=0;v=0;yb:{zb:{Ab:{D=H[e+124>>2];if(!((D|0)!=64|H[e+128>>2]!=64)){b=1<<z;j=b>>>1|b;l=0-j|0;u=e+28|0;g=H[e+120>>2]+268|0;f=H[e+8>>2];c=H[e+4>>2];k=H[e>>2];m=H[e+104>>2];b=H[e+116>>2];if(Z&8){break Ab}while(1){v=0;while(1){q=b;n=g;g=H[g>>2];if(g){Bb:{if(g&2097168){break Bb}b=g&495;if(!b){break Bb}m=u+(I[b+H[e+108>>2]|0]<<2)|0;i=H[m>>2];b=H[i>>2];c=c-b|0;Cb:{if(k>>>16>>>0<b>>>0){p=H[i+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[i+(d?8:12)>>2];while(1){Db:{if(f){break Db}f=H[e+16>>2];c=f+1|0;i=I[f+1|0];if(I[f|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Db}H[e+16>>2]=c;k=(i<<9)+k|0;f=7;break Db}H[e+16>>2]=c;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?p:!p;break Cb}k=k-(b<<16)|0;if(!(c&32768)){p=H[i+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[i+(b?12:8)>>2];while(1){Eb:{if(f){break Eb}f=H[e+16>>2];d=f+1|0;i=I[f+1|0];if(I[f|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Eb}H[e+16>>2]=d;k=(i<<9)+k|0;f=7;break Eb}H[e+16>>2]=d;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!p:p;break Cb}b=H[i+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>17&4|(H[o>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];Fb:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Gb:{if(f){break Gb}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Gb}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Gb}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;d=d?i:!i;break Fb}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Hb:{if(f){break Hb}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Hb}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Hb}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}d=b?!i:i;break Fb}d=H[h+4>>2]}H[q>>2]=(p|0)==(d|0)?j:l;H[o>>2]=H[o>>2]|32;H[n+4>>2]=H[n+4>>2]|8;b=n-268|0;H[b>>2]=H[b>>2]|131072;b=n-260|0;H[b>>2]=H[b>>2]|32768;b=n-264|0;i=b;w=H[b>>2];b=d^p;H[i>>2]=w|b<<31|65536;g=b<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){p=g>>>3|0;m=u+(I[H[e+108>>2]+(p&495)|0]<<2)|0;o=H[m>>2];b=H[o>>2];c=c-b|0;Ib:{if(k>>>16>>>0<b>>>0){i=H[o+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[o+(d?8:12)>>2];while(1){Jb:{if(f){break Jb}f=H[e+16>>2];c=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Jb}H[e+16>>2]=c;k=(o<<9)+k|0;f=7;break Jb}H[e+16>>2]=c;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Ib}k=k-(b<<16)|0;if(!(c&32768)){i=H[o+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[o+(b?12:8)>>2];while(1){Kb:{if(f){break Kb}f=H[e+16>>2];d=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Kb}H[e+16>>2]=d;k=(o<<9)+k|0;f=7;break Kb}H[e+16>>2]=d;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Ib}b=H[o+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>20&4|(H[o>>2]>>>22&1|(g>>>15&16|(g>>>19&64|p&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];Lb:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Mb:{if(f){break Mb}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Mb}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Mb}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Lb}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Nb:{if(f){break Nb}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Nb}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Nb}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Lb}b=H[h+4>>2]}H[q+256>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|256;H[n+4>>2]=H[n+4>>2]|64;g=(b^p)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){p=g>>>6|0;m=u+(I[H[e+108>>2]+(p&495)|0]<<2)|0;o=H[m>>2];b=H[o>>2];c=c-b|0;Ob:{if(k>>>16>>>0<b>>>0){i=H[o+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[o+(d?8:12)>>2];while(1){Pb:{if(f){break Pb}f=H[e+16>>2];c=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Pb}H[e+16>>2]=c;k=(o<<9)+k|0;f=7;break Pb}H[e+16>>2]=c;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Ob}k=k-(b<<16)|0;if(!(c&32768)){i=H[o+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[o+(b?12:8)>>2];while(1){Qb:{if(f){break Qb}f=H[e+16>>2];d=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Qb}H[e+16>>2]=d;k=(o<<9)+k|0;f=7;break Qb}H[e+16>>2]=d;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Ob}b=H[o+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>23&4|(H[o>>2]>>>25&1|(g>>>18&16|(g>>>22&64|p&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];Rb:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Sb:{if(f){break Sb}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Sb}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Sb}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Rb}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Tb:{if(f){break Tb}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Tb}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Tb}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Rb}b=H[h+4>>2]}H[q+512>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|2048;H[n+4>>2]=H[n+4>>2]|512;g=(b^p)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){p=g>>>9|0;m=u+(I[H[e+108>>2]+(p&495)|0]<<2)|0;o=H[m>>2];b=H[o>>2];c=c-b|0;Ub:{if(k>>>16>>>0<b>>>0){i=H[o+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[o+(d?8:12)>>2];while(1){Vb:{if(f){break Vb}f=H[e+16>>2];c=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Vb}H[e+16>>2]=c;k=(o<<9)+k|0;f=7;break Vb}H[e+16>>2]=c;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Ub}k=k-(b<<16)|0;if(!(c&32768)){i=H[o+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[o+(b?12:8)>>2];while(1){Wb:{if(f){break Wb}f=H[e+16>>2];d=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Wb}H[e+16>>2]=d;k=(o<<9)+k|0;f=7;break Wb}H[e+16>>2]=d;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Ub}b=H[o+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>26&4|(H[o>>2]>>>28&1|(g>>>21&16|(g>>>25&64|p&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];Xb:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Yb:{if(f){break Yb}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Yb}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Yb}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Xb}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Zb:{if(f){break Zb}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break Zb}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Zb}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Xb}b=H[h+4>>2]}H[q+768>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|16384;H[n+4>>2]=H[n+4>>2]|4096;H[n+260>>2]=H[n+260>>2]|4;H[n+268>>2]=H[n+268>>2]|1;b=b^p;H[n+264>>2]=H[n+264>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}H[n>>2]=g}g=n+4|0;b=q+4|0;v=v+1|0;if((v|0)!=64){continue}break}g=n+12|0;b=q+772|0;q=r>>>0<60;r=r+4|0;if(q){continue}break}break zb}b=1<<z;l=b>>>1|b;q=H[e+120>>2];g=(q+(D<<2)|0)+12|0;b=H[e+128>>2];f=H[e+8>>2];c=H[e+4>>2];k=H[e>>2];m=H[e+104>>2];p=H[e+116>>2];_b:{if(Z&8){$b:{if(b>>>0<4){break $b}if(D){t=N(D,12);o=D<<3;q=0-l|0;M=e+28|0;while(1){w=0;while(1){n=g;g=H[g>>2];if(g){ac:{if(g&2097168){break ac}b=g&495;if(!b){break ac}m=M+(I[b+H[e+108>>2]|0]<<2)|0;r=H[m>>2];b=H[r>>2];c=c-b|0;bc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;i=H[r+4>>2];if(c&32768){break bc}i=H[r+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[r+(b?12:8)>>2];while(1){cc:{if(f){break cc}f=H[e+16>>2];d=f+1|0;r=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(r<<8)+k|0;break cc}if(r>>>0<=143){H[e+16>>2]=d;k=(r<<9)+k|0;f=7;break cc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}i=b?!i:i;break bc}i=H[r+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[r+(d?8:12)>>2];while(1){dc:{if(f){break dc}f=H[e+16>>2];c=f+1|0;r=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(r<<8)+k|0;break dc}if(r>>>0<=143){H[e+16>>2]=c;k=(r<<9)+k|0;f=7;break dc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;i=d?i:!i}if(i){h=n-4|0;d=H[n+4>>2]>>>17&4|(H[h>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=M+(I[d+24384|0]<<2)|0;u=H[m>>2];b=H[u>>2];c=c-b|0;i=I[d+24640|0];ec:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[u+4>>2];if(c&32768){break ec}r=H[u+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[u+(b?12:8)>>2];while(1){fc:{if(f){break fc}f=H[e+16>>2];d=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(u<<8)+k|0;break fc}if(u>>>0<=143){H[e+16>>2]=d;k=(u<<9)+k|0;f=7;break fc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break ec}r=H[u+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[u+(d?8:12)>>2];while(1){gc:{if(f){break gc}f=H[e+16>>2];c=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(u<<8)+k|0;break gc}if(u>>>0<=143){H[e+16>>2]=c;k=(u<<9)+k|0;f=7;break gc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}b=s;H[p>>2]=(i|0)==(b|0)?l:q;H[h>>2]=H[h>>2]|32;H[n+4>>2]=H[n+4>>2]|8;g=(b^i)<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){i=g>>>3|0;m=M+(I[H[e+108>>2]+(i&495)|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;hc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[h+4>>2];if(c&32768){break hc}r=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){ic:{if(f){break ic}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(h<<8)+k|0;break ic}if(h>>>0<=143){H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break ic}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break hc}r=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){jc:{if(f){break jc}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(h<<8)+k|0;break jc}if(h>>>0<=143){H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break jc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}if(s){u=n-4|0;d=H[n+4>>2]>>>20&4|(H[u>>2]>>>22&1|(g>>>15&16|(g>>>19&64|i&170)));m=M+(I[d+24384|0]<<2)|0;s=H[m>>2];b=H[s>>2];c=c-b|0;r=(D<<2)+p|0;i=I[d+24640|0];kc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;A=H[s+4>>2];if(c&32768){break kc}h=H[s+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[s+(b?12:8)>>2];while(1){lc:{if(f){break lc}f=H[e+16>>2];d=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(s<<8)+k|0;break lc}if(s>>>0<=143){H[e+16>>2]=d;k=(s<<9)+k|0;f=7;break lc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}A=b?!h:h;break kc}h=H[s+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[s+(d?8:12)>>2];while(1){mc:{if(f){break mc}f=H[e+16>>2];c=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(s<<8)+k|0;break mc}if(s>>>0<=143){H[e+16>>2]=c;k=(s<<9)+k|0;f=7;break mc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;A=d?h:!h}b=A;H[r>>2]=(i|0)==(b|0)?l:q;H[u>>2]=H[u>>2]|256;H[n+4>>2]=H[n+4>>2]|64;g=(b^i)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){i=g>>>6|0;m=M+(I[H[e+108>>2]+(i&495)|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;nc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[h+4>>2];if(c&32768){break nc}r=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){oc:{if(f){break oc}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(h<<8)+k|0;break oc}if(h>>>0<=143){H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break oc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break nc}r=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){pc:{if(f){break pc}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(h<<8)+k|0;break pc}if(h>>>0<=143){H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break pc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}if(s){u=n-4|0;d=H[n+4>>2]>>>23&4|(H[u>>2]>>>25&1|(g>>>18&16|(g>>>22&64|i&170)));m=M+(I[d+24384|0]<<2)|0;s=H[m>>2];b=H[s>>2];c=c-b|0;r=o+p|0;i=I[d+24640|0];qc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;A=H[s+4>>2];if(c&32768){break qc}h=H[s+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[s+(b?12:8)>>2];while(1){rc:{if(f){break rc}f=H[e+16>>2];d=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(s<<8)+k|0;break rc}if(s>>>0<=143){H[e+16>>2]=d;k=(s<<9)+k|0;f=7;break rc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}A=b?!h:h;break qc}h=H[s+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[s+(d?8:12)>>2];while(1){sc:{if(f){break sc}f=H[e+16>>2];c=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(s<<8)+k|0;break sc}if(s>>>0<=143){H[e+16>>2]=c;k=(s<<9)+k|0;f=7;break sc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;A=d?h:!h}b=A;H[r>>2]=(i|0)==(b|0)?l:q;H[u>>2]=H[u>>2]|2048;H[n+4>>2]=H[n+4>>2]|512;g=(b^i)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){i=g>>>9|0;m=M+(I[H[e+108>>2]+(i&495)|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;tc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[h+4>>2];if(c&32768){break tc}r=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){uc:{if(f){break uc}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(h<<8)+k|0;break uc}if(h>>>0<=143){H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break uc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break tc}r=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){vc:{if(f){break vc}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(h<<8)+k|0;break vc}if(h>>>0<=143){H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break vc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}if(s){u=n-4|0;d=H[n+4>>2]>>>26&4|(H[u>>2]>>>28&1|(g>>>21&16|(g>>>25&64|i&170)));m=M+(I[d+24384|0]<<2)|0;s=H[m>>2];b=H[s>>2];c=c-b|0;r=p+t|0;i=I[d+24640|0];wc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;A=H[s+4>>2];if(c&32768){break wc}h=H[s+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[s+(b?12:8)>>2];while(1){xc:{if(f){break xc}f=H[e+16>>2];d=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(s<<8)+k|0;break xc}if(s>>>0<=143){H[e+16>>2]=d;k=(s<<9)+k|0;f=7;break xc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}A=b?!h:h;break wc}h=H[s+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[s+(d?8:12)>>2];while(1){yc:{if(f){break yc}f=H[e+16>>2];c=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(s<<8)+k|0;break yc}if(s>>>0<=143){H[e+16>>2]=c;k=(s<<9)+k|0;f=7;break yc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;A=d?h:!h}b=A;H[r>>2]=(i|0)==(b|0)?l:q;H[u>>2]=H[u>>2]|16384;H[n+4>>2]=H[n+4>>2]|4096;d=n+(H[e+124>>2]<<2)|0;H[d+4>>2]=H[d+4>>2]|4;H[d+12>>2]=H[d+12>>2]|1;b=b^i;H[d+8>>2]=H[d+8>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}H[n>>2]=g}g=n+4|0;p=p+4|0;w=w+1|0;if((D|0)!=(w|0)){continue}break}g=n+12|0;p=p+t|0;j=j+4|0;b=H[e+128>>2];if(j>>>0<(b&-4)>>>0){continue}break}break $b}g=(b&-4)-1|0;j=(g&-4)+4|0;g=(q+(g<<1&-8)|0)+20|0}H[e+8>>2]=f;H[e+4>>2]=c;H[e>>2]=k;H[e+104>>2]=m;if(!D|b>>>0<=j>>>0){break _b}while(1){c=(b|0)==(j|0);f=0;b=j;if(!c){while(1){hc(e,g,(N(f,D)<<2)+p|0,l,f,H[e+124>>2]+2|0,1);f=f+1|0;b=H[e+128>>2];if(f>>>0<b-j>>>0){continue}break}}g=g+4|0;p=p+4|0;v=v+1|0;if((D|0)!=(v|0)){continue}break}break _b}zc:{if(b>>>0<4){break zc}if(D){t=N(D,12);o=D<<3;q=0-l|0;M=e+28|0;while(1){w=0;while(1){n=g;g=H[g>>2];if(g){Ac:{if(g&2097168){break Ac}b=g&495;if(!b){break Ac}m=M+(I[b+H[e+108>>2]|0]<<2)|0;r=H[m>>2];b=H[r>>2];c=c-b|0;Bc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;i=H[r+4>>2];if(c&32768){break Bc}i=H[r+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[r+(b?12:8)>>2];while(1){Cc:{if(f){break Cc}f=H[e+16>>2];d=f+1|0;r=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(r<<8)+k|0;break Cc}if(r>>>0<=143){H[e+16>>2]=d;k=(r<<9)+k|0;f=7;break Cc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}i=b?!i:i;break Bc}i=H[r+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[r+(d?8:12)>>2];while(1){Dc:{if(f){break Dc}f=H[e+16>>2];c=f+1|0;r=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(r<<8)+k|0;break Dc}if(r>>>0<=143){H[e+16>>2]=c;k=(r<<9)+k|0;f=7;break Dc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;i=d?i:!i}if(i){h=n-4|0;d=H[n+4>>2]>>>17&4|(H[h>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=M+(I[d+24384|0]<<2)|0;u=H[m>>2];b=H[u>>2];c=c-b|0;i=I[d+24640|0];Ec:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;d=H[u+4>>2];if(c&32768){break Ec}r=H[u+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[u+(b?12:8)>>2];while(1){Fc:{if(f){break Fc}f=H[e+16>>2];d=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(u<<8)+k|0;break Fc}if(u>>>0<=143){H[e+16>>2]=d;k=(u<<9)+k|0;f=7;break Fc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}d=b?!r:r;break Ec}r=H[u+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[u+(d?8:12)>>2];while(1){Gc:{if(f){break Gc}f=H[e+16>>2];c=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(u<<8)+k|0;break Gc}if(u>>>0<=143){H[e+16>>2]=c;k=(u<<9)+k|0;f=7;break Gc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;d=d?r:!r}H[p>>2]=(i|0)==(d|0)?l:q;H[h>>2]=H[h>>2]|32;H[n+4>>2]=H[n+4>>2]|8;b=n+(-2-H[e+124>>2]<<2)|0;H[b+4>>2]=H[b+4>>2]|32768;d=d^i;H[b>>2]=H[b>>2]|d<<31|65536;b=b-4|0;H[b>>2]=H[b>>2]|131072;g=d<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){i=g>>>3|0;m=M+(I[H[e+108>>2]+(i&495)|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;Hc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[h+4>>2];if(c&32768){break Hc}r=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Ic:{if(f){break Ic}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(h<<8)+k|0;break Ic}if(h>>>0<=143){H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Ic}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break Hc}r=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Jc:{if(f){break Jc}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(h<<8)+k|0;break Jc}if(h>>>0<=143){H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Jc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}if(s){u=n-4|0;d=H[n+4>>2]>>>20&4|(H[u>>2]>>>22&1|(g>>>15&16|(g>>>19&64|i&170)));m=M+(I[d+24384|0]<<2)|0;s=H[m>>2];b=H[s>>2];c=c-b|0;r=(D<<2)+p|0;i=I[d+24640|0];Kc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;A=H[s+4>>2];if(c&32768){break Kc}h=H[s+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[s+(b?12:8)>>2];while(1){Lc:{if(f){break Lc}f=H[e+16>>2];d=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(s<<8)+k|0;break Lc}if(s>>>0<=143){H[e+16>>2]=d;k=(s<<9)+k|0;f=7;break Lc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}A=b?!h:h;break Kc}h=H[s+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[s+(d?8:12)>>2];while(1){Mc:{if(f){break Mc}f=H[e+16>>2];c=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(s<<8)+k|0;break Mc}if(s>>>0<=143){H[e+16>>2]=c;k=(s<<9)+k|0;f=7;break Mc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;A=d?h:!h}b=A;H[r>>2]=(i|0)==(b|0)?l:q;H[u>>2]=H[u>>2]|256;H[n+4>>2]=H[n+4>>2]|64;g=(b^i)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){i=g>>>6|0;m=M+(I[H[e+108>>2]+(i&495)|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;Nc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[h+4>>2];if(c&32768){break Nc}r=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Oc:{if(f){break Oc}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(h<<8)+k|0;break Oc}if(h>>>0<=143){H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Oc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break Nc}r=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Pc:{if(f){break Pc}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(h<<8)+k|0;break Pc}if(h>>>0<=143){H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Pc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}if(s){u=n-4|0;d=H[n+4>>2]>>>23&4|(H[u>>2]>>>25&1|(g>>>18&16|(g>>>22&64|i&170)));m=M+(I[d+24384|0]<<2)|0;s=H[m>>2];b=H[s>>2];c=c-b|0;r=o+p|0;i=I[d+24640|0];Qc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;A=H[s+4>>2];if(c&32768){break Qc}h=H[s+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[s+(b?12:8)>>2];while(1){Rc:{if(f){break Rc}f=H[e+16>>2];d=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(s<<8)+k|0;break Rc}if(s>>>0<=143){H[e+16>>2]=d;k=(s<<9)+k|0;f=7;break Rc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}A=b?!h:h;break Qc}h=H[s+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[s+(d?8:12)>>2];while(1){Sc:{if(f){break Sc}f=H[e+16>>2];c=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(s<<8)+k|0;break Sc}if(s>>>0<=143){H[e+16>>2]=c;k=(s<<9)+k|0;f=7;break Sc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;A=d?h:!h}b=A;H[r>>2]=(i|0)==(b|0)?l:q;H[u>>2]=H[u>>2]|2048;H[n+4>>2]=H[n+4>>2]|512;g=(b^i)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){i=g>>>9|0;m=M+(I[H[e+108>>2]+(i&495)|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;Tc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;s=H[h+4>>2];if(c&32768){break Tc}r=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){Uc:{if(f){break Uc}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(h<<8)+k|0;break Uc}if(h>>>0<=143){H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Uc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}s=b?!r:r;break Tc}r=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){Vc:{if(f){break Vc}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(h<<8)+k|0;break Vc}if(h>>>0<=143){H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Vc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;s=d?r:!r}if(s){u=n-4|0;d=H[n+4>>2]>>>26&4|(H[u>>2]>>>28&1|(g>>>21&16|(g>>>25&64|i&170)));m=M+(I[d+24384|0]<<2)|0;s=H[m>>2];b=H[s>>2];c=c-b|0;r=p+t|0;i=I[d+24640|0];Wc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;A=H[s+4>>2];if(c&32768){break Wc}h=H[s+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[s+(b?12:8)>>2];while(1){Xc:{if(f){break Xc}f=H[e+16>>2];d=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=d;f=8;k=(s<<8)+k|0;break Xc}if(s>>>0<=143){H[e+16>>2]=d;k=(s<<9)+k|0;f=7;break Xc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}A=b?!h:h;break Wc}h=H[s+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[s+(d?8:12)>>2];while(1){Yc:{if(f){break Yc}f=H[e+16>>2];c=f+1|0;s=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=c;f=8;k=(s<<8)+k|0;break Yc}if(s>>>0<=143){H[e+16>>2]=c;k=(s<<9)+k|0;f=7;break Yc}H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;A=d?h:!h}b=A;H[r>>2]=(i|0)==(b|0)?l:q;H[u>>2]=H[u>>2]|16384;H[n+4>>2]=H[n+4>>2]|4096;d=n+(H[e+124>>2]<<2)|0;H[d+4>>2]=H[d+4>>2]|4;H[d+12>>2]=H[d+12>>2]|1;b=b^i;H[d+8>>2]=H[d+8>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}H[n>>2]=g}g=n+4|0;p=p+4|0;w=w+1|0;if((D|0)!=(w|0)){continue}break}g=n+12|0;p=p+t|0;j=j+4|0;b=H[e+128>>2];if(j>>>0<(b&-4)>>>0){continue}break}break zc}g=(b&-4)-1|0;j=(g&-4)+4|0;g=(q+(g<<1&-8)|0)+20|0}H[e+8>>2]=f;H[e+4>>2]=c;H[e>>2]=k;H[e+104>>2]=m;if(!D|b>>>0<=j>>>0){break _b}while(1){c=(b|0)==(j|0);f=0;b=j;if(!c){while(1){hc(e,g,(N(f,D)<<2)+p|0,l,f,H[e+124>>2]+2|0,0);f=f+1|0;b=H[e+128>>2];if(f>>>0<b-j>>>0){continue}break}}g=g+4|0;p=p+4|0;v=v+1|0;if((D|0)!=(v|0)){continue}break}}break yb}while(1){v=0;while(1){q=b;n=g;g=H[g>>2];if(g){Zc:{if(g&2097168){break Zc}b=g&495;if(!b){break Zc}m=u+(I[b+H[e+108>>2]|0]<<2)|0;i=H[m>>2];b=H[i>>2];c=c-b|0;_c:{if(k>>>16>>>0<b>>>0){p=H[i+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[i+(d?8:12)>>2];while(1){$c:{if(f){break $c}f=H[e+16>>2];c=f+1|0;i=I[f+1|0];if(I[f|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break $c}H[e+16>>2]=c;k=(i<<9)+k|0;f=7;break $c}H[e+16>>2]=c;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?p:!p;break _c}k=k-(b<<16)|0;if(!(c&32768)){p=H[i+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[i+(b?12:8)>>2];while(1){ad:{if(f){break ad}f=H[e+16>>2];d=f+1|0;i=I[f+1|0];if(I[f|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break ad}H[e+16>>2]=d;k=(i<<9)+k|0;f=7;break ad}H[e+16>>2]=d;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!p:p;break _c}b=H[i+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>17&4|(H[o>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];bd:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){cd:{if(f){break cd}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break cd}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break cd}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break bd}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){dd:{if(f){break dd}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break dd}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break dd}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break bd}b=H[h+4>>2]}H[q>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|32;H[n+4>>2]=H[n+4>>2]|8;g=(b^p)<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){p=g>>>3|0;m=u+(I[H[e+108>>2]+(p&495)|0]<<2)|0;o=H[m>>2];b=H[o>>2];c=c-b|0;ed:{if(k>>>16>>>0<b>>>0){i=H[o+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[o+(d?8:12)>>2];while(1){fd:{if(f){break fd}f=H[e+16>>2];c=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break fd}H[e+16>>2]=c;k=(o<<9)+k|0;f=7;break fd}H[e+16>>2]=c;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break ed}k=k-(b<<16)|0;if(!(c&32768)){i=H[o+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[o+(b?12:8)>>2];while(1){gd:{if(f){break gd}f=H[e+16>>2];d=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break gd}H[e+16>>2]=d;k=(o<<9)+k|0;f=7;break gd}H[e+16>>2]=d;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break ed}b=H[o+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>20&4|(H[o>>2]>>>22&1|(g>>>15&16|(g>>>19&64|p&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];hd:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){id:{if(f){break id}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break id}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break id}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break hd}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){jd:{if(f){break jd}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break jd}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break jd}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break hd}b=H[h+4>>2]}H[q+256>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|256;H[n+4>>2]=H[n+4>>2]|64;g=(b^p)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){p=g>>>6|0;m=u+(I[H[e+108>>2]+(p&495)|0]<<2)|0;o=H[m>>2];b=H[o>>2];c=c-b|0;kd:{if(k>>>16>>>0<b>>>0){i=H[o+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[o+(d?8:12)>>2];while(1){ld:{if(f){break ld}f=H[e+16>>2];c=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break ld}H[e+16>>2]=c;k=(o<<9)+k|0;f=7;break ld}H[e+16>>2]=c;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break kd}k=k-(b<<16)|0;if(!(c&32768)){i=H[o+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[o+(b?12:8)>>2];while(1){md:{if(f){break md}f=H[e+16>>2];d=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break md}H[e+16>>2]=d;k=(o<<9)+k|0;f=7;break md}H[e+16>>2]=d;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break kd}b=H[o+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>23&4|(H[o>>2]>>>25&1|(g>>>18&16|(g>>>22&64|p&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];nd:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){od:{if(f){break od}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break od}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break od}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break nd}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){pd:{if(f){break pd}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break pd}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break pd}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break nd}b=H[h+4>>2]}H[q+512>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|2048;H[n+4>>2]=H[n+4>>2]|512;g=(b^p)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){p=g>>>9|0;m=u+(I[H[e+108>>2]+(p&495)|0]<<2)|0;o=H[m>>2];b=H[o>>2];c=c-b|0;qd:{if(k>>>16>>>0<b>>>0){i=H[o+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[o+(d?8:12)>>2];while(1){rd:{if(f){break rd}f=H[e+16>>2];c=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break rd}H[e+16>>2]=c;k=(o<<9)+k|0;f=7;break rd}H[e+16>>2]=c;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break qd}k=k-(b<<16)|0;if(!(c&32768)){i=H[o+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[o+(b?12:8)>>2];while(1){sd:{if(f){break sd}f=H[e+16>>2];d=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break sd}H[e+16>>2]=d;k=(o<<9)+k|0;f=7;break sd}H[e+16>>2]=d;f=8;k=(o<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break qd}b=H[o+4>>2]}if(b){o=n-4|0;d=H[n+4>>2]>>>26&4|(H[o>>2]>>>28&1|(g>>>21&16|(g>>>25&64|p&170)));m=u+(I[d+24384|0]<<2)|0;h=H[m>>2];b=H[h>>2];c=c-b|0;p=I[d+24640|0];td:{if(k>>>16>>>0<b>>>0){i=H[h+4>>2];d=b>>>0>c>>>0;H[m>>2]=H[h+(d?8:12)>>2];while(1){ud:{if(f){break ud}f=H[e+16>>2];c=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break ud}H[e+16>>2]=c;k=(h<<9)+k|0;f=7;break ud}H[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break td}k=k-(b<<16)|0;if(!(c&32768)){i=H[h+4>>2];b=b>>>0>c>>>0;H[m>>2]=H[h+(b?12:8)>>2];while(1){vd:{if(f){break vd}f=H[e+16>>2];d=f+1|0;h=I[f+1|0];if(I[f|0]==255){if(h>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;k=k+65280|0;f=8;break vd}H[e+16>>2]=d;k=(h<<9)+k|0;f=7;break vd}H[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break td}b=H[h+4>>2]}H[q+768>>2]=(p|0)==(b|0)?j:l;H[o>>2]=H[o>>2]|16384;H[n+4>>2]=H[n+4>>2]|4096;H[n+260>>2]=H[n+260>>2]|4;H[n+268>>2]=H[n+268>>2]|1;b=b^p;H[n+264>>2]=H[n+264>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}H[n>>2]=g}g=n+4|0;b=q+4|0;v=v+1|0;if((v|0)!=64){continue}break}g=n+12|0;b=q+772|0;q=r>>>0<60;r=r+4|0;if(q){continue}break}}H[e+8>>2]=f;H[e+4>>2]=c;H[e>>2]=k;H[e+104>>2]=m}break _a;case 0:if(!na){o=1<<z>>>1|0;r=H[e+124>>2];d=r<<2;b=(d+H[e+120>>2]|0)+12|0;g=H[e+116>>2];k=0;c=H[e+128>>2];if(c>>>0>=4){if(!r){break Za}p=N(r,12);n=r<<3;i=0-o|0;while(1){c=0;while(1){l=b;b=H[b>>2];wd:{if(!b){break wd}if((b&2097168)==16){b=H[e>>2];h=H[e+8>>2];xd:{if(h){break xd}j=(b|0)==255;q=H[e+16>>2];b=I[q|0];yd:{if(!j){H[e>>2]=b;H[e+16>>2]=q+1;break yd}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=q+1;h=7;break xd}b=255;H[e>>2]=255}h=8}j=h-1|0;H[e+8>>2]=j;j=b>>>j&1;b=H[g>>2];H[g>>2]=((j|0)==(b>>>31|0)?i:o)+b;b=H[l>>2]|1048576;H[l>>2]=b}if((b&16777344)==128){b=H[e>>2];h=H[e+8>>2];zd:{if(h){break zd}j=(b|0)==255;q=H[e+16>>2];b=I[q|0];Ad:{if(!j){H[e>>2]=b;H[e+16>>2]=q+1;break Ad}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=q+1;h=7;break zd}b=255;H[e>>2]=255}h=8}q=h-1|0;H[e+8>>2]=q;j=d+g|0;f=H[j>>2];H[j>>2]=f+((b>>>q&1)==(f>>>31|0)?i:o);b=H[l>>2]|8388608;H[l>>2]=b}if((b&134218752)==1024){b=H[e>>2];h=H[e+8>>2];Bd:{if(h){break Bd}j=(b|0)==255;q=H[e+16>>2];b=I[q|0];Cd:{if(!j){H[e>>2]=b;H[e+16>>2]=q+1;break Cd}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=q+1;h=7;break Bd}b=255;H[e>>2]=255}h=8}q=h-1|0;H[e+8>>2]=q;j=g+n|0;f=H[j>>2];H[j>>2]=f+((b>>>q&1)==(f>>>31|0)?i:o);b=H[l>>2]|67108864;H[l>>2]=b}if((b&1073750016)!=8192){break wd}b=H[e>>2];h=H[e+8>>2];Dd:{if(h){break Dd}j=(b|0)==255;q=H[e+16>>2];b=I[q|0];Ed:{if(!j){H[e>>2]=b;H[e+16>>2]=q+1;break Ed}if(b>>>0<=143){H[e>>2]=b;H[e+16>>2]=q+1;h=7;break Dd}b=255;H[e>>2]=255}h=8}q=h-1|0;H[e+8>>2]=q;j=g+p|0;f=H[j>>2];H[j>>2]=f+((b>>>q&1)==(f>>>31|0)?i:o);H[l>>2]=H[l>>2]|536870912}g=g+4|0;b=l+4|0;c=c+1|0;if((r|0)!=(c|0)){continue}break}g=g+p|0;b=l+12|0;k=k+4|0;c=H[e+128>>2];if(k>>>0<(c&-4)>>>0){continue}break}}if(!r|c>>>0<=k>>>0){break _a}v=0;j=0-o|0;d=c;while(1){Fd:{if((d|0)==(k|0)){d=k;break Fd}h=H[b>>2];i=0;while(1){d=N(i,3);if((2097168<<d&h)==16<<d){n=(N(i,r)<<2)+g|0;c=H[e>>2];m=H[e+8>>2];Gd:{if(m){break Gd}l=(c|0)!=255;q=H[e+16>>2];c=I[q|0];Hd:{if(!l){if(c>>>0>=144){c=255;H[e>>2]=255;break Hd}H[e>>2]=c;H[e+16>>2]=q+1;m=7;break Gd}H[e>>2]=c;H[e+16>>2]=q+1}m=8}l=m-1|0;H[e+8>>2]=l;l=c>>>l&1;c=H[n>>2];H[n>>2]=((l|0)==(c>>>31|0)?j:o)+c;h=H[b>>2]|1048576<<d;H[b>>2]=h;c=H[e+128>>2]}i=i+1|0;d=c;if(i>>>0<c-k>>>0){continue}break}}g=g+4|0;b=b+4|0;v=v+1|0;if((r|0)!=(v|0)){continue}break}break _a}j=H[e+120>>2];d=H[e+116>>2];s=H[e+124>>2];c=H[e+128>>2];if(!((s|0)!=64|(c|0)!=64)){c=j+268|0;r=0;u=1<<z>>>1|0;h=0-u|0;i=H[e+8>>2];g=H[e+4>>2];b=H[e>>2];k=H[e+104>>2];while(1){m=0;while(1){q=d;j=c;d=H[c>>2];if(d){l=c;if((d&2097168)==16){k=da+((d&1048576?16:d&495?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;Id:{if(b>>>16>>>0<c>>>0){p=H[f+4>>2];n=c>>>0>g>>>0;H[k>>2]=H[f+(n?8:12)>>2];while(1){Jd:{if(i){break Jd}f=H[e+16>>2];g=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Jd}H[e+16>>2]=g;b=(o<<9)+b|0;i=7;break Jd}H[e+16>>2]=g;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?p:!p;break Id}b=b-(c<<16)|0;if(!(g&32768)){p=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){Kd:{if(i){break Kd}f=H[e+16>>2];n=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Kd}H[e+16>>2]=n;b=(o<<9)+b|0;i=7;break Kd}H[e+16>>2]=n;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!p:p;break Id}n=H[f+4>>2]}c=H[q>>2];H[q>>2]=((n|0)==(c>>>31|0)?h:u)+c;d=d|1048576}if((d&16777344)==128){k=da+((d&8388608?16:d&3960?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;Ld:{if(b>>>16>>>0<c>>>0){p=H[f+4>>2];n=c>>>0>g>>>0;H[k>>2]=H[f+(n?8:12)>>2];while(1){Md:{if(i){break Md}f=H[e+16>>2];g=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Md}H[e+16>>2]=g;b=(o<<9)+b|0;i=7;break Md}H[e+16>>2]=g;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?p:!p;break Ld}b=b-(c<<16)|0;if(!(g&32768)){p=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){Nd:{if(i){break Nd}f=H[e+16>>2];n=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Nd}H[e+16>>2]=n;b=(o<<9)+b|0;i=7;break Nd}H[e+16>>2]=n;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!p:p;break Ld}n=H[f+4>>2]}c=H[q+256>>2];H[q+256>>2]=((n|0)==(c>>>31|0)?h:u)+c;d=d|8388608}if((d&134218752)==1024){k=da+((d&67108864?16:d&31680?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;Od:{if(b>>>16>>>0<c>>>0){p=H[f+4>>2];n=c>>>0>g>>>0;H[k>>2]=H[f+(n?8:12)>>2];while(1){Pd:{if(i){break Pd}f=H[e+16>>2];g=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Pd}H[e+16>>2]=g;b=(o<<9)+b|0;i=7;break Pd}H[e+16>>2]=g;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?p:!p;break Od}b=b-(c<<16)|0;if(!(g&32768)){p=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){Qd:{if(i){break Qd}f=H[e+16>>2];n=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Qd}H[e+16>>2]=n;b=(o<<9)+b|0;i=7;break Qd}H[e+16>>2]=n;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!p:p;break Od}n=H[f+4>>2]}c=H[q+512>>2];H[q+512>>2]=((n|0)==(c>>>31|0)?h:u)+c;d=d|67108864}if((d&1073750016)==8192){k=da+((d&536870912?16:d&253440?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;Rd:{if(b>>>16>>>0<c>>>0){p=H[f+4>>2];n=c>>>0>g>>>0;H[k>>2]=H[f+(n?8:12)>>2];while(1){Sd:{if(i){break Sd}f=H[e+16>>2];g=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Sd}H[e+16>>2]=g;b=(o<<9)+b|0;i=7;break Sd}H[e+16>>2]=g;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?p:!p;break Rd}b=b-(c<<16)|0;if(!(g&32768)){p=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){Td:{if(i){break Td}f=H[e+16>>2];n=f+1|0;o=I[f+1|0];if(I[f|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8;break Td}H[e+16>>2]=n;b=(o<<9)+b|0;i=7;break Td}H[e+16>>2]=n;i=8;b=(o<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!p:p;break Rd}n=H[f+4>>2]}c=H[q+768>>2];H[q+768>>2]=((n|0)==(c>>>31|0)?h:u)+c;d=d|536870912}H[l>>2]=d}c=j+4|0;d=q+4|0;m=m+1|0;if((m|0)!=64){continue}break}c=j+12|0;d=q+772|0;l=r>>>0<60;r=r+4|0;if(l){continue}break}H[e+8>>2]=i;H[e+4>>2]=g;H[e>>2]=b;H[e+104>>2]=k;break _a}v=1<<z>>>1|0;r=s<<2;f=(r+j|0)+12|0;i=H[e+8>>2];g=H[e+4>>2];b=H[e>>2];k=H[e+104>>2];p=0;Ud:{if(c>>>0<4){break Ud}if(s){h=N(s,12);n=s<<3;t=0-v|0;while(1){m=0;while(1){l=f;j=H[f>>2];if(j){if((j&2097168)==16){k=da+((j&1048576?16:j&495?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;Vd:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=H[f+4>>2];if(g&32768){break Vd}o=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){Wd:{if(i){break Wd}f=H[e+16>>2];q=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=q;i=8;b=(u<<8)+b|0;break Wd}if(u>>>0<=143){H[e+16>>2]=q;b=(u<<9)+b|0;i=7;break Wd}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!o:o;break Vd}o=H[f+4>>2];q=c>>>0>g>>>0;H[k>>2]=H[f+(q?8:12)>>2];while(1){Xd:{if(i){break Xd}f=H[e+16>>2];g=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=g;i=8;b=(u<<8)+b|0;break Xd}if(u>>>0<=143){H[e+16>>2]=g;b=(u<<9)+b|0;i=7;break Xd}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?o:!o}c=H[d>>2];H[d>>2]=((q|0)==(c>>>31|0)?t:v)+c;j=j|1048576}if((j&16777344)==128){k=da+((j&8388608?16:j&3960?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;Yd:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=H[f+4>>2];if(g&32768){break Yd}o=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){Zd:{if(i){break Zd}f=H[e+16>>2];q=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=q;i=8;b=(u<<8)+b|0;break Zd}if(u>>>0<=143){H[e+16>>2]=q;b=(u<<9)+b|0;i=7;break Zd}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!o:o;break Yd}o=H[f+4>>2];q=c>>>0>g>>>0;H[k>>2]=H[f+(q?8:12)>>2];while(1){_d:{if(i){break _d}f=H[e+16>>2];g=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=g;i=8;b=(u<<8)+b|0;break _d}if(u>>>0<=143){H[e+16>>2]=g;b=(u<<9)+b|0;i=7;break _d}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?o:!o}f=q;c=d+r|0;q=H[c>>2];H[c>>2]=q+((f|0)==(q>>>31|0)?t:v);j=j|8388608}if((j&134218752)==1024){k=da+((j&67108864?16:j&31680?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;$d:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=H[f+4>>2];if(g&32768){break $d}o=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){ae:{if(i){break ae}f=H[e+16>>2];q=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=q;i=8;b=(u<<8)+b|0;break ae}if(u>>>0<=143){H[e+16>>2]=q;b=(u<<9)+b|0;i=7;break ae}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!o:o;break $d}o=H[f+4>>2];q=c>>>0>g>>>0;H[k>>2]=H[f+(q?8:12)>>2];while(1){be:{if(i){break be}f=H[e+16>>2];g=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=g;i=8;b=(u<<8)+b|0;break be}if(u>>>0<=143){H[e+16>>2]=g;b=(u<<9)+b|0;i=7;break be}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?o:!o}f=q;c=d+n|0;q=H[c>>2];H[c>>2]=q+((f|0)==(q>>>31|0)?t:v);j=j|67108864}if((j&1073750016)==8192){k=da+((j&536870912?16:j&253440?15:14)<<2)|0;f=H[k>>2];c=H[f>>2];g=g-c|0;ce:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=H[f+4>>2];if(g&32768){break ce}o=H[f+4>>2];c=c>>>0>g>>>0;H[k>>2]=H[f+(c?12:8)>>2];while(1){de:{if(i){break de}f=H[e+16>>2];q=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=q;i=8;b=(u<<8)+b|0;break de}if(u>>>0<=143){H[e+16>>2]=q;b=(u<<9)+b|0;i=7;break de}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!o:o;break ce}o=H[f+4>>2];q=c>>>0>g>>>0;H[k>>2]=H[f+(q?8:12)>>2];while(1){ee:{if(i){break ee}f=H[e+16>>2];g=f+1|0;u=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=g;i=8;b=(u<<8)+b|0;break ee}if(u>>>0<=143){H[e+16>>2]=g;b=(u<<9)+b|0;i=7;break ee}H[e+12>>2]=H[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?o:!o}f=q;c=d+h|0;q=H[c>>2];H[c>>2]=q+((f|0)==(q>>>31|0)?t:v);j=j|536870912}H[l>>2]=j}f=l+4|0;d=d+4|0;m=m+1|0;if((s|0)!=(m|0)){continue}break}f=l+12|0;d=d+h|0;p=p+4|0;c=H[e+128>>2];if(p>>>0<(c&-4)>>>0){continue}break}break Ud}l=(c&-4)-1|0;p=(l&-4)+4|0;f=(j+(l<<1&-8)|0)+20|0}H[e+8>>2]=i;H[e+4>>2]=g;H[e>>2]=b;H[e+104>>2]=k;if(!s|c>>>0<=p>>>0){break _a}w=0;l=0-v|0;b=c;while(1){fe:{if((b|0)==(p|0)){b=p;break fe}i=H[f>>2];h=0;while(1){m=N(h,3);if((2097168<<m&i)==16<<m){k=(N(h,s)<<2)+d|0;b=i>>>m|0;j=da+((b&1048576?16:b&495?15:14)<<2)|0;H[e+104>>2]=j;q=H[j>>2];b=H[q>>2];c=H[e+4>>2]-b|0;H[e+4>>2]=c;g=H[e>>2];ge:{if(g>>>16>>>0<b>>>0){n=H[q+4>>2];H[e+4>>2]=b;c=b>>>0>c>>>0;H[j>>2]=H[q+(c?8:12)>>2];i=H[e+8>>2];while(1){he:{if(i){break he}q=H[e+16>>2];j=q+1|0;r=I[q+1|0];if(I[q|0]==255){if(r>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;g=g+65280|0;i=8;break he}H[e+16>>2]=j;g=(r<<9)+g|0;i=7;break he}H[e+16>>2]=j;i=8;g=(r<<8)+g|0}i=i-1|0;H[e+8>>2]=i;g=g<<1;H[e>>2]=g;b=b<<1;H[e+4>>2]=b;if(b>>>0<32768){continue}break}c=c?n:!n;break ge}g=g-(b<<16)|0;H[e>>2]=g;if(!(c&32768)){n=H[q+4>>2];b=b>>>0>c>>>0;H[j>>2]=H[q+(b?12:8)>>2];i=H[e+8>>2];while(1){ie:{if(i){break ie}q=H[e+16>>2];j=q+1|0;r=I[q+1|0];if(I[q|0]==255){if(r>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;g=g+65280|0;i=8;break ie}H[e+16>>2]=j;g=(r<<9)+g|0;i=7;break ie}H[e+16>>2]=j;i=8;g=(r<<8)+g|0}i=i-1|0;H[e+8>>2]=i;g=g<<1;H[e>>2]=g;c=c<<1;H[e+4>>2]=c;if(c>>>0<32768){continue}break}c=b?!n:n;break ge}c=H[q+4>>2]}b=H[k>>2];H[k>>2]=((c|0)==(b>>>31|0)?l:v)+b;i=H[f>>2]|1048576<<m;H[f>>2]=i;c=H[e+128>>2]}h=h+1|0;b=c;if(h>>>0<b-p>>>0){continue}break}}f=f+4|0;d=d+4|0;w=w+1|0;if((s|0)!=(w|0)){continue}break};break _a;case 1:break $a}}M=0;r=0;je:{ke:{le:{X=H[e+124>>2];if(!((X|0)!=64|H[e+128>>2]!=64)){b=1<<z;v=b>>>1|b;w=0-v|0;q=e+100|0;l=e+96|0;s=e+28|0;g=H[e+120>>2]+268|0;h=H[e+8>>2];b=H[e+4>>2];d=H[e>>2];j=H[e+104>>2];c=H[e+116>>2];if(Z&8){break le}while(1){u=0;while(1){k=c;f=g;g=H[g>>2];me:{ne:{oe:{if(!g){j=H[l>>2];g=H[j>>2];b=b-g|0;pe:{if(d>>>16>>>0<g>>>0){n=H[j+4>>2];c=b>>>0<g>>>0;H[l>>2]=H[j+(c?8:12)>>2];while(1){qe:{if(h){break qe}j=H[e+16>>2];b=j+1|0;p=I[j+1|0];if(I[j|0]==255){if(p>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break qe}H[e+16>>2]=b;d=(p<<9)+d|0;h=7;break qe}H[e+16>>2]=b;h=8;d=(p<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?n:!n;break pe}d=d-(g<<16)|0;if(!(b&32768)){n=H[j+4>>2];c=b>>>0<g>>>0;H[l>>2]=H[j+(c?12:8)>>2];while(1){re:{if(h){break re}j=H[e+16>>2];g=j+1|0;p=I[j+1|0];if(I[j|0]==255){if(p>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break re}H[e+16>>2]=g;d=(p<<9)+d|0;h=7;break re}H[e+16>>2]=g;h=8;d=(p<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!n:n;break pe}c=H[j+4>>2]}if(!c){j=l;break me}c=H[q>>2];g=H[c>>2];b=b-g|0;se:{if(d>>>16>>>0<g>>>0){p=H[c+4>>2];j=b>>>0<g>>>0;c=H[(j?8:12)+c>>2];H[q>>2]=c;while(1){te:{if(h){break te}n=H[e+16>>2];b=n+1|0;i=I[n+1|0];if(I[n|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break te}H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break te}H[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;n=j?p:!p;break se}d=d-(g<<16)|0;if(!(b&32768)){p=H[c+4>>2];g=b>>>0<g>>>0;c=H[(g?12:8)+c>>2];H[q>>2]=c;while(1){ue:{if(h){break ue}n=H[e+16>>2];j=n+1|0;i=I[n+1|0];if(I[n|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ue}H[e+16>>2]=j;d=(i<<9)+d|0;h=7;break ue}H[e+16>>2]=j;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=g?!p:p;break se}n=H[c+4>>2]}g=H[c>>2];b=b-g|0;ve:{if(d>>>16>>>0<g>>>0){p=H[c+4>>2];j=c;c=b>>>0<g>>>0;H[q>>2]=H[j+(c?8:12)>>2];while(1){we:{if(h){break we}j=H[e+16>>2];b=j+1|0;i=I[j+1|0];if(I[j|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break we}H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break we}H[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?p:!p;break ve}d=d-(g<<16)|0;if(!(b&32768)){p=H[c+4>>2];j=c;c=b>>>0<g>>>0;H[q>>2]=H[j+(c?12:8)>>2];while(1){xe:{if(h){break xe}j=H[e+16>>2];g=j+1|0;i=I[j+1|0];if(I[j|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break xe}H[e+16>>2]=g;d=(i<<9)+d|0;h=7;break xe}H[e+16>>2]=g;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!p:p;break ve}c=H[c+4>>2]}g=0;j=q;ye:{ze:{Ae:{Be:{Ce:{switch(c|n<<1){case 0:i=f-4|0;j=H[f+4>>2]>>>17&4|H[i>>2]>>>19&1;c=s+(I[j+24384|0]<<2)|0;n=H[c>>2];g=H[n>>2];b=b-g|0;De:{if(d>>>16>>>0<g>>>0){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?8:12)>>2];while(1){Ee:{if(h){break Ee}n=H[e+16>>2];b=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ee}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Ee}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;n=c?p:!p;break De}d=d-(g<<16)|0;if(!(b&32768)){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?12:8)>>2];while(1){Fe:{if(h){break Fe}n=H[e+16>>2];g=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Fe}H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break Fe}H[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!p:p;break De}n=H[n+4>>2]}g=I[j+24640|0];H[k>>2]=(n|0)==(g|0)?v:w;H[i>>2]=H[i>>2]|32;H[f+4>>2]=H[f+4>>2]|8;c=f-268|0;H[c>>2]=H[c>>2]|131072;c=f-260|0;H[c>>2]=H[c>>2]|32768;c=f-264|0;j=c;i=H[c>>2];c=g^n;H[j>>2]=i|c<<31|65536;j=c<<19;i=H[e+108>>2];c=s+(I[i+2|0]<<2)|0;n=H[c>>2];g=H[n>>2];b=b-g|0;Ge:{if(d>>>16>>>0<g>>>0){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?8:12)>>2];while(1){He:{if(h){break He}n=H[e+16>>2];b=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break He}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break He}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?p:!p;break Ge}d=d-(g<<16)|0;if(!(b&32768)){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?12:8)>>2];while(1){Ie:{if(h){break Ie}n=H[e+16>>2];g=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ie}H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break Ie}H[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!p:p;break Ge}c=H[n+4>>2]}g=j|16;if(!c){break Be}break;case 1:break Ce;case 2:break Ae;case 3:break ye;default:break ne}}m=f-4|0;n=H[f+4>>2]>>>20&4|(H[m>>2]>>>22&1|(g>>>15&16|(g>>>19&64|g>>>3&170)));j=s+(I[n+24384|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;Je:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];A=j;j=b>>>0<c>>>0;H[A>>2]=H[p+(j?8:12)>>2];while(1){Ke:{if(h){break Ke}p=H[e+16>>2];b=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ke}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Ke}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break Je}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){Le:{if(h){break Le}p=H[e+16>>2];j=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Le}H[e+16>>2]=j;d=(o<<9)+d|0;h=7;break Le}H[e+16>>2]=j;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break Je}j=H[p+4>>2]}c=I[n+24640|0];H[k+256>>2]=(j|0)==(c|0)?v:w;H[m>>2]=H[m>>2]|256;H[f+4>>2]=H[f+4>>2]|64;i=H[e+108>>2];g=(c^j)<<22|g|128}j=s+(I[(g>>>6&495)+i|0]<<2)|0;n=H[j>>2];c=H[n>>2];b=b-c|0;Me:{if(d>>>16>>>0<c>>>0){p=H[n+4>>2];A=j;j=b>>>0<c>>>0;H[A>>2]=H[n+(j?8:12)>>2];while(1){Ne:{if(h){break Ne}n=H[e+16>>2];b=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ne}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Ne}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=j?p:!p;break Me}d=d-(c<<16)|0;if(!(b&32768)){p=H[n+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[n+(c?12:8)>>2];while(1){Oe:{if(h){break Oe}n=H[e+16>>2];j=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Oe}H[e+16>>2]=j;d=(m<<9)+d|0;h=7;break Oe}H[e+16>>2]=j;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!p:p;break Me}c=H[n+4>>2]}if(!c){break ze}}m=f-4|0;n=H[f+4>>2]>>>23&4|(H[m>>2]>>>25&1|(g>>>18&16|(g>>>22&64|g>>>6&170)));j=s+(I[n+24384|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;Pe:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];A=j;j=b>>>0<c>>>0;H[A>>2]=H[p+(j?8:12)>>2];while(1){Qe:{if(h){break Qe}p=H[e+16>>2];b=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Qe}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Qe}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break Pe}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){Re:{if(h){break Re}p=H[e+16>>2];j=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Re}H[e+16>>2]=j;d=(o<<9)+d|0;h=7;break Re}H[e+16>>2]=j;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break Pe}j=H[p+4>>2]}c=I[n+24640|0];H[k+512>>2]=(j|0)==(c|0)?v:w;H[m>>2]=H[m>>2]|2048;H[f+4>>2]=H[f+4>>2]|512;g=(c^j)<<25|g|1024;i=H[e+108>>2]}j=s+(I[(g>>>9&495)+i|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;Se:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[p+(n?8:12)>>2];while(1){Te:{if(h){break Te}p=H[e+16>>2];b=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Te}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Te}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break Se}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){Ue:{if(h){break Ue}p=H[e+16>>2];n=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ue}H[e+16>>2]=n;d=(m<<9)+d|0;h=7;break Ue}H[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break Se}c=H[p+4>>2]}if(!c){break ne}}M=f-4|0;t=H[f+4>>2]>>>26&4|(H[M>>2]>>>28&1|(g>>>21&16|(g>>>25&64|g>>>9&170)));j=s+(I[t+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;break oe}Ve:{if(g&2097168){break Ve}j=s+(I[H[e+108>>2]+(g&495)|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;We:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[p+(n?8:12)>>2];while(1){Xe:{if(h){break Xe}p=H[e+16>>2];b=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Xe}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Xe}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break We}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){Ye:{if(h){break Ye}p=H[e+16>>2];n=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ye}H[e+16>>2]=n;d=(m<<9)+d|0;h=7;break Ye}H[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break We}c=H[p+4>>2]}if(!c){break Ve}o=f-4|0;p=H[f+4>>2]>>>17&4|(H[o>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));j=s+(I[p+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;Ze:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){_e:{if(h){break _e}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break _e}H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break _e}H[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;i=n?m:!m;break Ze}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){$e:{if(h){break $e}i=H[e+16>>2];n=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break $e}H[e+16>>2]=n;d=(t<<9)+d|0;h=7;break $e}H[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=c?!m:m;break Ze}i=H[i+4>>2]}n=I[p+24640|0];H[k>>2]=(i|0)==(n|0)?v:w;H[o>>2]=H[o>>2]|32;H[f+4>>2]=H[f+4>>2]|8;c=f-268|0;H[c>>2]=H[c>>2]|131072;c=f-260|0;H[c>>2]=H[c>>2]|32768;c=f-264|0;p=c;A=H[c>>2];c=i^n;H[p>>2]=A|c<<31|65536;g=c<<19|g|16}af:{if(g&16777344){break af}p=g>>>3|0;j=s+(I[H[e+108>>2]+(p&495)|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;bf:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){cf:{if(h){break cf}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break cf}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break cf}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break bf}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){df:{if(h){break df}i=H[e+16>>2];n=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break df}H[e+16>>2]=n;d=(o<<9)+d|0;h=7;break df}H[e+16>>2]=n;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break bf}c=H[i+4>>2]}if(!c){break af}o=f-4|0;p=H[f+4>>2]>>>20&4|(H[o>>2]>>>22&1|(g>>>15&16|(g>>>19&64|p&170)));j=s+(I[p+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;ef:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){ff:{if(h){break ff}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ff}H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break ff}H[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break ef}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){gf:{if(h){break gf}i=H[e+16>>2];n=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break gf}H[e+16>>2]=n;d=(t<<9)+d|0;h=7;break gf}H[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break ef}n=H[i+4>>2]}c=I[p+24640|0];H[k+256>>2]=(n|0)==(c|0)?v:w;H[o>>2]=H[o>>2]|256;H[f+4>>2]=H[f+4>>2]|64;g=(c^n)<<22|g|128}hf:{if(g&134218752){break hf}p=g>>>6|0;j=s+(I[H[e+108>>2]+(p&495)|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;jf:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){kf:{if(h){break kf}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break kf}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break kf}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break jf}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){lf:{if(h){break lf}i=H[e+16>>2];n=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break lf}H[e+16>>2]=n;d=(o<<9)+d|0;h=7;break lf}H[e+16>>2]=n;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break jf}c=H[i+4>>2]}if(!c){break hf}o=f-4|0;p=H[f+4>>2]>>>23&4|(H[o>>2]>>>25&1|(g>>>18&16|(g>>>22&64|p&170)));j=s+(I[p+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;mf:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){nf:{if(h){break nf}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break nf}H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break nf}H[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break mf}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){of:{if(h){break of}i=H[e+16>>2];n=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break of}H[e+16>>2]=n;d=(t<<9)+d|0;h=7;break of}H[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break mf}n=H[i+4>>2]}c=I[p+24640|0];H[k+512>>2]=(n|0)==(c|0)?v:w;H[o>>2]=H[o>>2]|2048;H[f+4>>2]=H[f+4>>2]|512;g=(c^n)<<25|g|1024}if(g&1073750016){break ne}p=g>>>9|0;j=s+(I[H[e+108>>2]+(p&495)|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;pf:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){qf:{if(h){break qf}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break qf}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break qf}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break pf}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){rf:{if(h){break rf}i=H[e+16>>2];n=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break rf}H[e+16>>2]=n;d=(o<<9)+d|0;h=7;break rf}H[e+16>>2]=n;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break pf}c=H[i+4>>2]}if(!c){break ne}M=f-4|0;t=H[f+4>>2]>>>26&4|(H[M>>2]>>>28&1|(g>>>21&16|(g>>>25&64|p&170)));j=s+(I[t+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0}sf:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[(n?8:12)+i>>2];while(1){tf:{if(h){break tf}p=H[e+16>>2];b=p+1|0;i=I[p+1|0];if(I[p|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break tf}H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break tf}H[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break sf}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[(c?12:8)+i>>2];while(1){uf:{if(h){break uf}p=H[e+16>>2];n=p+1|0;i=I[p+1|0];if(I[p|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break uf}H[e+16>>2]=n;d=(i<<9)+d|0;h=7;break uf}H[e+16>>2]=n;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break sf}n=H[i+4>>2]}c=I[t+24640|0];H[k+768>>2]=(n|0)==(c|0)?v:w;H[M>>2]=H[M>>2]|16384;H[f+4>>2]=H[f+4>>2]|4096;H[f+260>>2]=H[f+260>>2]|4;H[f+268>>2]=H[f+268>>2]|1;c=c^n;H[f+264>>2]=H[f+264>>2]|c<<18|2;g=c<<28|g|8192}H[f>>2]=g&-1226833921}g=f+4|0;c=k+4|0;u=u+1|0;if((u|0)!=64){continue}break}g=f+12|0;c=k+772|0;n=r>>>0<60;r=r+4|0;if(n){continue}break}break ke}b=1<<z;A=b>>>1|b;l=H[e+120>>2];c=(l+(X<<2)|0)+12|0;g=H[e+128>>2];h=H[e+8>>2];b=H[e+4>>2];d=H[e>>2];j=H[e+104>>2];p=H[e+116>>2];if(Z&8){vf:{if(g>>>0<4){break vf}if(X){n=e+100|0;q=e+96|0;w=N(X,12);u=X<<3;s=0-A|0;D=e+28|0;while(1){v=0;while(1){k=c;c=H[c>>2];wf:{xf:{yf:{if(c){zf:{if(c&2097168){break zf}j=D+(I[H[e+108>>2]+(c&495)|0]<<2)|0;f=H[j>>2];g=H[f>>2];b=b-g|0;Af:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[f+4>>2];if(b&32768){break Af}i=H[f+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[f+(g?12:8)>>2];while(1){Bf:{if(h){break Bf}f=H[e+16>>2];l=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Bf}if(m>>>0<=143){H[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Bf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break Af}i=H[f+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[f+(l?8:12)>>2];while(1){Cf:{if(h){break Cf}f=H[e+16>>2];b=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Cf}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Cf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break zf}o=k-4|0;f=H[k+4>>2]>>>17&4|(H[o>>2]>>>19&1|(c>>>14&16|(c>>>16&64|c&170)));j=D+(I[f+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Df:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Df}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Ef:{if(h){break Ef}i=H[e+16>>2];l=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Ef}if(t>>>0<=143){H[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Ef}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Df}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Ff:{if(h){break Ff}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Ff}if(t>>>0<=143){H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Ff}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[f+24640|0];H[p>>2]=(l|0)==(g|0)?A:s;H[o>>2]=H[o>>2]|32;H[k+4>>2]=H[k+4>>2]|8;c=(g^l)<<19|c|16}Gf:{if(c&16777344){break Gf}f=c>>>3|0;j=D+(I[H[e+108>>2]+(f&495)|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Hf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Hf}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){If:{if(h){break If}i=H[e+16>>2];l=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break If}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break If}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Hf}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Jf:{if(h){break Jf}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Jf}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Jf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Gf}o=k-4|0;f=H[k+4>>2]>>>20&4|(H[o>>2]>>>22&1|(c>>>15&16|(c>>>19&64|f&170)));j=D+(I[f+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Y=(X<<2)+p|0;Kf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Kf}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Lf:{if(h){break Lf}i=H[e+16>>2];l=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Lf}if(t>>>0<=143){H[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Lf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Kf}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Mf:{if(h){break Mf}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Mf}if(t>>>0<=143){H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Mf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[f+24640|0];H[Y>>2]=(l|0)==(g|0)?A:s;H[o>>2]=H[o>>2]|256;H[k+4>>2]=H[k+4>>2]|64;c=(g^l)<<22|c|128}Nf:{if(c&134218752){break Nf}f=c>>>6|0;j=D+(I[H[e+108>>2]+(f&495)|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Of:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Of}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Pf:{if(h){break Pf}i=H[e+16>>2];l=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break Pf}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break Pf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Of}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Qf:{if(h){break Qf}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Qf}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Qf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Nf}o=k-4|0;f=H[k+4>>2]>>>23&4|(H[o>>2]>>>25&1|(c>>>18&16|(c>>>22&64|f&170)));j=D+(I[f+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Y=p+u|0;Rf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Rf}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Sf:{if(h){break Sf}i=H[e+16>>2];l=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Sf}if(t>>>0<=143){H[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Sf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Rf}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Tf:{if(h){break Tf}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Tf}if(t>>>0<=143){H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Tf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[f+24640|0];H[Y>>2]=(l|0)==(g|0)?A:s;H[o>>2]=H[o>>2]|2048;H[k+4>>2]=H[k+4>>2]|512;c=(g^l)<<25|c|1024}if(c&1073750016){break xf}f=c>>>9|0;j=D+(I[H[e+108>>2]+(f&495)|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Uf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Uf}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Vf:{if(h){break Vf}i=H[e+16>>2];l=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break Vf}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break Vf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Uf}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Wf:{if(h){break Wf}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Wf}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Wf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break xf}t=k-4|0;o=H[k+4>>2]>>>26&4|(H[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|f&170)));j=D+(I[o+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;break yf}l=H[q>>2];c=H[l>>2];b=b-c|0;Xf:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;g=H[l+4>>2];if(b&32768){break Xf}j=H[l+4>>2];c=b>>>0<c>>>0;H[q>>2]=H[l+(c?12:8)>>2];while(1){Yf:{if(h){break Yf}l=H[e+16>>2];g=l+1|0;f=I[l+1|0];if(I[l|0]!=255){H[e+16>>2]=g;h=8;d=(f<<8)+d|0;break Yf}if(f>>>0<=143){H[e+16>>2]=g;d=(f<<9)+d|0;h=7;break Yf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}g=c?!j:j;break Xf}j=H[l+4>>2];g=b>>>0<c>>>0;H[q>>2]=H[l+(g?8:12)>>2];while(1){Zf:{if(h){break Zf}l=H[e+16>>2];b=l+1|0;f=I[l+1|0];if(I[l|0]!=255){H[e+16>>2]=b;h=8;d=(f<<8)+d|0;break Zf}if(f>>>0<=143){H[e+16>>2]=b;d=(f<<9)+d|0;h=7;break Zf}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;g=g?j:!j}if(!g){j=q;break wf}g=H[n>>2];c=H[g>>2];b=b-c|0;_f:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;l=H[g+4>>2];if(b&32768){break _f}f=H[g+4>>2];c=b>>>0<c>>>0;g=H[(c?12:8)+g>>2];H[n>>2]=g;while(1){$f:{if(h){break $f}j=H[e+16>>2];l=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=l;h=8;d=(i<<8)+d|0;break $f}if(i>>>0<=143){H[e+16>>2]=l;d=(i<<9)+d|0;h=7;break $f}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=c?!f:f;break _f}f=H[g+4>>2];l=b>>>0<c>>>0;g=H[(l?8:12)+g>>2];H[n>>2]=g;while(1){ag:{if(h){break ag}j=H[e+16>>2];b=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(i<<8)+d|0;break ag}if(i>>>0<=143){H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break ag}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;l=l?f:!f}c=H[g>>2];b=b-c|0;bg:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;j=H[g+4>>2];if(b&32768){break bg}f=H[g+4>>2];c=b>>>0<c>>>0;H[n>>2]=H[(c?12:8)+g>>2];while(1){cg:{if(h){break cg}j=H[e+16>>2];g=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=g;h=8;d=(i<<8)+d|0;break cg}if(i>>>0<=143){H[e+16>>2]=g;d=(i<<9)+d|0;h=7;break cg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!f:f;break bg}f=H[g+4>>2];j=g;g=b>>>0<c>>>0;H[n>>2]=H[j+(g?8:12)>>2];while(1){dg:{if(h){break dg}j=H[e+16>>2];b=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(i<<8)+d|0;break dg}if(i>>>0<=143){H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break dg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=g?f:!f}g=j;c=0;j=n;eg:{fg:{gg:{hg:{ig:{switch(g|l<<1){case 0:i=k-4|0;l=H[k+4>>2]>>>17&4|H[i>>2]>>>19&1;g=D+(I[l+24384|0]<<2)|0;j=H[g>>2];c=H[j>>2];b=b-c|0;jg:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=H[j+4>>2];if(b&32768){break jg}f=H[j+4>>2];c=b>>>0<c>>>0;H[g>>2]=H[j+(c?12:8)>>2];while(1){kg:{if(h){break kg}j=H[e+16>>2];g=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=g;h=8;d=(m<<8)+d|0;break kg}if(m>>>0<=143){H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break kg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break jg}f=H[j+4>>2];m=g;g=b>>>0<c>>>0;H[m>>2]=H[j+(g?8:12)>>2];while(1){lg:{if(h){break lg}j=H[e+16>>2];b=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break lg}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break lg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=I[l+24640|0];H[p>>2]=(g|0)==(c|0)?A:s;H[i>>2]=H[i>>2]|32;H[k+4>>2]=H[k+4>>2]|8;l=(c^g)<<19;i=H[e+108>>2];g=D+(I[i+2|0]<<2)|0;j=H[g>>2];c=H[j>>2];b=b-c|0;mg:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=H[j+4>>2];if(b&32768){break mg}f=H[j+4>>2];c=b>>>0<c>>>0;H[g>>2]=H[j+(c?12:8)>>2];while(1){ng:{if(h){break ng}j=H[e+16>>2];g=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=g;h=8;d=(m<<8)+d|0;break ng}if(m>>>0<=143){H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break ng}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break mg}f=H[j+4>>2];m=g;g=b>>>0<c>>>0;H[m>>2]=H[j+(g?8:12)>>2];while(1){og:{if(h){break og}j=H[e+16>>2];b=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break og}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break og}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=l|16;if(!g){break hg}break;case 1:break ig;case 2:break gg;case 3:break eg;default:break xf}}m=k-4|0;j=H[k+4>>2]>>>20&4|(H[m>>2]>>>22&1|(c>>>15&16|(c>>>19&64|c>>>3&170)));l=D+(I[j+24384|0]<<2)|0;f=H[l>>2];g=H[f>>2];b=b-g|0;t=(X<<2)+p|0;pg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=H[f+4>>2];if(b&32768){break pg}i=H[f+4>>2];g=b>>>0<g>>>0;H[l>>2]=H[f+(g?12:8)>>2];while(1){qg:{if(h){break qg}f=H[e+16>>2];l=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break qg}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break qg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break pg}i=H[f+4>>2];o=l;l=b>>>0<g>>>0;H[o>>2]=H[f+(l?8:12)>>2];while(1){rg:{if(h){break rg}f=H[e+16>>2];b=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break rg}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break rg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=I[j+24640|0];H[t>>2]=(l|0)==(g|0)?A:s;H[m>>2]=H[m>>2]|256;H[k+4>>2]=H[k+4>>2]|64;i=H[e+108>>2];c=(g^l)<<22|c|128}l=D+(I[(c>>>6&495)+i|0]<<2)|0;j=H[l>>2];g=H[j>>2];b=b-g|0;sg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;f=H[j+4>>2];if(b&32768){break sg}f=H[j+4>>2];g=b>>>0<g>>>0;H[l>>2]=H[j+(g?12:8)>>2];while(1){tg:{if(h){break tg}j=H[e+16>>2];l=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=l;h=8;d=(m<<8)+d|0;break tg}if(m>>>0<=143){H[e+16>>2]=l;d=(m<<9)+d|0;h=7;break tg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=g?!f:f;break sg}f=H[j+4>>2];m=l;l=b>>>0<g>>>0;H[m>>2]=H[j+(l?8:12)>>2];while(1){ug:{if(h){break ug}j=H[e+16>>2];b=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break ug}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break ug}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;f=l?f:!f}if(!f){break fg}}m=k-4|0;j=H[k+4>>2]>>>23&4|(H[m>>2]>>>25&1|(c>>>18&16|(c>>>22&64|c>>>6&170)));l=D+(I[j+24384|0]<<2)|0;f=H[l>>2];g=H[f>>2];b=b-g|0;t=p+u|0;vg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=H[f+4>>2];if(b&32768){break vg}i=H[f+4>>2];g=b>>>0<g>>>0;H[l>>2]=H[f+(g?12:8)>>2];while(1){wg:{if(h){break wg}f=H[e+16>>2];l=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break wg}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break wg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break vg}i=H[f+4>>2];o=l;l=b>>>0<g>>>0;H[o>>2]=H[f+(l?8:12)>>2];while(1){xg:{if(h){break xg}f=H[e+16>>2];b=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break xg}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break xg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=I[j+24640|0];H[t>>2]=(l|0)==(g|0)?A:s;H[m>>2]=H[m>>2]|2048;H[k+4>>2]=H[k+4>>2]|512;c=(g^l)<<25|c|1024;i=H[e+108>>2]}j=D+(I[(c>>>9&495)+i|0]<<2)|0;f=H[j>>2];g=H[f>>2];b=b-g|0;yg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[f+4>>2];if(b&32768){break yg}i=H[f+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[f+(g?12:8)>>2];while(1){zg:{if(h){break zg}f=H[e+16>>2];l=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(m<<8)+d|0;break zg}if(m>>>0<=143){H[e+16>>2]=l;d=(m<<9)+d|0;h=7;break zg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break yg}i=H[f+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[f+(l?8:12)>>2];while(1){Ag:{if(h){break Ag}f=H[e+16>>2];b=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Ag}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Ag}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break xf}}t=k-4|0;o=H[k+4>>2]>>>26&4|(H[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|c>>>9&170)));j=D+(I[o+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0}Y=p+w|0;Bg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Bg}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[(g?12:8)+i>>2];while(1){Cg:{if(h){break Cg}f=H[e+16>>2];l=f+1|0;i=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(i<<8)+d|0;break Cg}if(i>>>0<=143){H[e+16>>2]=l;d=(i<<9)+d|0;h=7;break Cg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Bg}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[(l?8:12)+i>>2];while(1){Dg:{if(h){break Dg}f=H[e+16>>2];b=f+1|0;i=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(i<<8)+d|0;break Dg}if(i>>>0<=143){H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Dg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[o+24640|0];H[Y>>2]=(l|0)==(g|0)?A:s;H[t>>2]=H[t>>2]|16384;H[k+4>>2]=H[k+4>>2]|4096;f=k+(H[e+124>>2]<<2)|0;H[f+4>>2]=H[f+4>>2]|4;H[f+12>>2]=H[f+12>>2]|1;g=g^l;H[f+8>>2]=H[f+8>>2]|g<<18|2;c=g<<28|c|8192}H[k>>2]=c&-1226833921}c=k+4|0;p=p+4|0;v=v+1|0;if((X|0)!=(v|0)){continue}break}c=k+12|0;p=p+w|0;r=r+4|0;g=H[e+128>>2];if(r>>>0<(g&-4)>>>0){continue}break}break vf}c=(g&-4)-1|0;r=(c&-4)+4|0;c=(l+(c<<1&-8)|0)+20|0}H[e+8>>2]=h;H[e+4>>2]=b;H[e>>2]=d;H[e+104>>2]=j;if(!X|g>>>0<=r>>>0){break je}while(1){h=0;if(H[e+128>>2]!=(r|0)){while(1){gc(e,c,(N(h,X)<<2)+p|0,A,h,1);h=h+1|0;if(h>>>0<H[e+128>>2]-r>>>0){continue}break}}H[c>>2]=H[c>>2]&-1226833921;p=p+4|0;c=c+4|0;M=M+1|0;if((X|0)!=(M|0)){continue}break}break je}Eg:{if(g>>>0<4){break Eg}if(X){n=e+100|0;q=e+96|0;w=N(X,12);u=X<<3;s=0-A|0;D=e+28|0;while(1){v=0;while(1){k=c;c=H[c>>2];Fg:{Gg:{Hg:{if(c){Ig:{if(c&2097168){break Ig}j=D+(I[H[e+108>>2]+(c&495)|0]<<2)|0;f=H[j>>2];g=H[f>>2];b=b-g|0;Jg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[f+4>>2];if(b&32768){break Jg}i=H[f+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[f+(g?12:8)>>2];while(1){Kg:{if(h){break Kg}f=H[e+16>>2];l=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Kg}if(m>>>0<=143){H[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Kg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break Jg}i=H[f+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[f+(l?8:12)>>2];while(1){Lg:{if(h){break Lg}f=H[e+16>>2];b=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Lg}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Lg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break Ig}o=k-4|0;f=H[k+4>>2]>>>17&4|(H[o>>2]>>>19&1|(c>>>14&16|(c>>>16&64|c&170)));j=D+(I[f+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Mg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Mg}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Ng:{if(h){break Ng}i=H[e+16>>2];l=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Ng}if(t>>>0<=143){H[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Ng}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Mg}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Og:{if(h){break Og}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Og}if(t>>>0<=143){H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Og}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[f+24640|0];H[p>>2]=(l|0)==(g|0)?A:s;H[o>>2]=H[o>>2]|32;H[k+4>>2]=H[k+4>>2]|8;f=k+(-2-H[e+124>>2]<<2)|0;H[f+4>>2]=H[f+4>>2]|32768;l=g^l;H[f>>2]=H[f>>2]|l<<31|65536;g=f-4|0;H[g>>2]=H[g>>2]|131072;c=l<<19|c|16}Pg:{if(c&16777344){break Pg}f=c>>>3|0;j=D+(I[H[e+108>>2]+(f&495)|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Qg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Qg}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Rg:{if(h){break Rg}i=H[e+16>>2];l=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break Rg}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break Rg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Qg}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Sg:{if(h){break Sg}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Sg}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Sg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Pg}o=k-4|0;f=H[k+4>>2]>>>20&4|(H[o>>2]>>>22&1|(c>>>15&16|(c>>>19&64|f&170)));j=D+(I[f+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Y=(X<<2)+p|0;Tg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Tg}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Ug:{if(h){break Ug}i=H[e+16>>2];l=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Ug}if(t>>>0<=143){H[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Ug}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Tg}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Vg:{if(h){break Vg}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Vg}if(t>>>0<=143){H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Vg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[f+24640|0];H[Y>>2]=(l|0)==(g|0)?A:s;H[o>>2]=H[o>>2]|256;H[k+4>>2]=H[k+4>>2]|64;c=(g^l)<<22|c|128}Wg:{if(c&134218752){break Wg}f=c>>>6|0;j=D+(I[H[e+108>>2]+(f&495)|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Xg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Xg}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){Yg:{if(h){break Yg}i=H[e+16>>2];l=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break Yg}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break Yg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Xg}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){Zg:{if(h){break Zg}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Zg}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Zg}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Wg}o=k-4|0;f=H[k+4>>2]>>>23&4|(H[o>>2]>>>25&1|(c>>>18&16|(c>>>22&64|f&170)));j=D+(I[f+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;Y=p+u|0;_g:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break _g}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){$g:{if(h){break $g}i=H[e+16>>2];l=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(t<<8)+d|0;break $g}if(t>>>0<=143){H[e+16>>2]=l;d=(t<<9)+d|0;h=7;break $g}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break _g}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){ah:{if(h){break ah}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(t<<8)+d|0;break ah}if(t>>>0<=143){H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break ah}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[f+24640|0];H[Y>>2]=(l|0)==(g|0)?A:s;H[o>>2]=H[o>>2]|2048;H[k+4>>2]=H[k+4>>2]|512;c=(g^l)<<25|c|1024}if(c&1073750016){break Gg}f=c>>>9|0;j=D+(I[H[e+108>>2]+(f&495)|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;bh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break bh}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[i+(g?12:8)>>2];while(1){ch:{if(h){break ch}i=H[e+16>>2];l=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break ch}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break ch}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break bh}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[i+(l?8:12)>>2];while(1){dh:{if(h){break dh}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break dh}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break dh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Gg}t=k-4|0;o=H[k+4>>2]>>>26&4|(H[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|f&170)));j=D+(I[o+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0;break Hg}l=H[q>>2];c=H[l>>2];b=b-c|0;eh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;g=H[l+4>>2];if(b&32768){break eh}j=H[l+4>>2];c=b>>>0<c>>>0;H[q>>2]=H[l+(c?12:8)>>2];while(1){fh:{if(h){break fh}l=H[e+16>>2];g=l+1|0;f=I[l+1|0];if(I[l|0]!=255){H[e+16>>2]=g;h=8;d=(f<<8)+d|0;break fh}if(f>>>0<=143){H[e+16>>2]=g;d=(f<<9)+d|0;h=7;break fh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}g=c?!j:j;break eh}j=H[l+4>>2];g=b>>>0<c>>>0;H[q>>2]=H[l+(g?8:12)>>2];while(1){gh:{if(h){break gh}l=H[e+16>>2];b=l+1|0;f=I[l+1|0];if(I[l|0]!=255){H[e+16>>2]=b;h=8;d=(f<<8)+d|0;break gh}if(f>>>0<=143){H[e+16>>2]=b;d=(f<<9)+d|0;h=7;break gh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;g=g?j:!j}if(!g){j=q;break Fg}g=H[n>>2];c=H[g>>2];b=b-c|0;hh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;l=H[g+4>>2];if(b&32768){break hh}f=H[g+4>>2];c=b>>>0<c>>>0;g=H[(c?12:8)+g>>2];H[n>>2]=g;while(1){ih:{if(h){break ih}j=H[e+16>>2];l=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=l;h=8;d=(i<<8)+d|0;break ih}if(i>>>0<=143){H[e+16>>2]=l;d=(i<<9)+d|0;h=7;break ih}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=c?!f:f;break hh}f=H[g+4>>2];l=b>>>0<c>>>0;g=H[(l?8:12)+g>>2];H[n>>2]=g;while(1){jh:{if(h){break jh}j=H[e+16>>2];b=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(i<<8)+d|0;break jh}if(i>>>0<=143){H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break jh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;l=l?f:!f}c=H[g>>2];b=b-c|0;kh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;j=H[g+4>>2];if(b&32768){break kh}f=H[g+4>>2];c=b>>>0<c>>>0;H[n>>2]=H[(c?12:8)+g>>2];while(1){lh:{if(h){break lh}j=H[e+16>>2];g=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=g;h=8;d=(i<<8)+d|0;break lh}if(i>>>0<=143){H[e+16>>2]=g;d=(i<<9)+d|0;h=7;break lh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!f:f;break kh}f=H[g+4>>2];j=g;g=b>>>0<c>>>0;H[n>>2]=H[j+(g?8:12)>>2];while(1){mh:{if(h){break mh}j=H[e+16>>2];b=j+1|0;i=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(i<<8)+d|0;break mh}if(i>>>0<=143){H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break mh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=g?f:!f}g=j;c=0;j=n;nh:{oh:{ph:{qh:{rh:{switch(g|l<<1){case 0:i=k-4|0;l=H[k+4>>2]>>>17&4|H[i>>2]>>>19&1;g=D+(I[l+24384|0]<<2)|0;j=H[g>>2];c=H[j>>2];b=b-c|0;sh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=H[j+4>>2];if(b&32768){break sh}f=H[j+4>>2];c=b>>>0<c>>>0;H[g>>2]=H[j+(c?12:8)>>2];while(1){th:{if(h){break th}j=H[e+16>>2];g=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=g;h=8;d=(m<<8)+d|0;break th}if(m>>>0<=143){H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break th}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break sh}f=H[j+4>>2];m=g;g=b>>>0<c>>>0;H[m>>2]=H[j+(g?8:12)>>2];while(1){uh:{if(h){break uh}j=H[e+16>>2];b=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break uh}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break uh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=I[l+24640|0];H[p>>2]=(g|0)==(c|0)?A:s;H[i>>2]=H[i>>2]|32;H[k+4>>2]=H[k+4>>2]|8;l=k+(-2-H[e+124>>2]<<2)|0;H[l+4>>2]=H[l+4>>2]|32768;g=c^g;H[l>>2]=H[l>>2]|g<<31|65536;c=l-4|0;H[c>>2]=H[c>>2]|131072;l=g<<19;i=H[e+108>>2];g=D+(I[i+2|0]<<2)|0;j=H[g>>2];c=H[j>>2];b=b-c|0;vh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=H[j+4>>2];if(b&32768){break vh}f=H[j+4>>2];c=b>>>0<c>>>0;H[g>>2]=H[j+(c?12:8)>>2];while(1){wh:{if(h){break wh}j=H[e+16>>2];g=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=g;h=8;d=(m<<8)+d|0;break wh}if(m>>>0<=143){H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break wh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break vh}f=H[j+4>>2];m=g;g=b>>>0<c>>>0;H[m>>2]=H[j+(g?8:12)>>2];while(1){xh:{if(h){break xh}j=H[e+16>>2];b=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break xh}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break xh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=l|16;if(!g){break qh}break;case 1:break rh;case 2:break ph;case 3:break nh;default:break Gg}}m=k-4|0;j=H[k+4>>2]>>>20&4|(H[m>>2]>>>22&1|(c>>>15&16|(c>>>19&64|c>>>3&170)));l=D+(I[j+24384|0]<<2)|0;f=H[l>>2];g=H[f>>2];b=b-g|0;t=(X<<2)+p|0;yh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=H[f+4>>2];if(b&32768){break yh}i=H[f+4>>2];g=b>>>0<g>>>0;H[l>>2]=H[f+(g?12:8)>>2];while(1){zh:{if(h){break zh}f=H[e+16>>2];l=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break zh}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break zh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break yh}i=H[f+4>>2];o=l;l=b>>>0<g>>>0;H[o>>2]=H[f+(l?8:12)>>2];while(1){Ah:{if(h){break Ah}f=H[e+16>>2];b=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Ah}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Ah}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=I[j+24640|0];H[t>>2]=(l|0)==(g|0)?A:s;H[m>>2]=H[m>>2]|256;H[k+4>>2]=H[k+4>>2]|64;i=H[e+108>>2];c=(g^l)<<22|c|128}l=D+(I[(c>>>6&495)+i|0]<<2)|0;j=H[l>>2];g=H[j>>2];b=b-g|0;Bh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;f=H[j+4>>2];if(b&32768){break Bh}f=H[j+4>>2];g=b>>>0<g>>>0;H[l>>2]=H[j+(g?12:8)>>2];while(1){Ch:{if(h){break Ch}j=H[e+16>>2];l=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Ch}if(m>>>0<=143){H[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Ch}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=g?!f:f;break Bh}f=H[j+4>>2];m=l;l=b>>>0<g>>>0;H[m>>2]=H[j+(l?8:12)>>2];while(1){Dh:{if(h){break Dh}j=H[e+16>>2];b=j+1|0;m=I[j+1|0];if(I[j|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Dh}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Dh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;f=l?f:!f}if(!f){break oh}}m=k-4|0;j=H[k+4>>2]>>>23&4|(H[m>>2]>>>25&1|(c>>>18&16|(c>>>22&64|c>>>6&170)));l=D+(I[j+24384|0]<<2)|0;f=H[l>>2];g=H[f>>2];b=b-g|0;t=p+u|0;Eh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=H[f+4>>2];if(b&32768){break Eh}i=H[f+4>>2];g=b>>>0<g>>>0;H[l>>2]=H[f+(g?12:8)>>2];while(1){Fh:{if(h){break Fh}f=H[e+16>>2];l=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(o<<8)+d|0;break Fh}if(o>>>0<=143){H[e+16>>2]=l;d=(o<<9)+d|0;h=7;break Fh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break Eh}i=H[f+4>>2];o=l;l=b>>>0<g>>>0;H[o>>2]=H[f+(l?8:12)>>2];while(1){Gh:{if(h){break Gh}f=H[e+16>>2];b=f+1|0;o=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(o<<8)+d|0;break Gh}if(o>>>0<=143){H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Gh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=I[j+24640|0];H[t>>2]=(l|0)==(g|0)?A:s;H[m>>2]=H[m>>2]|2048;H[k+4>>2]=H[k+4>>2]|512;c=(g^l)<<25|c|1024;i=H[e+108>>2]}j=D+(I[(c>>>9&495)+i|0]<<2)|0;f=H[j>>2];g=H[f>>2];b=b-g|0;Hh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[f+4>>2];if(b&32768){break Hh}i=H[f+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[f+(g?12:8)>>2];while(1){Ih:{if(h){break Ih}f=H[e+16>>2];l=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Ih}if(m>>>0<=143){H[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Ih}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break Hh}i=H[f+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[f+(l?8:12)>>2];while(1){Jh:{if(h){break Jh}f=H[e+16>>2];b=f+1|0;m=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Jh}if(m>>>0<=143){H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Jh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break Gg}}t=k-4|0;o=H[k+4>>2]>>>26&4|(H[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|c>>>9&170)));j=D+(I[o+24384|0]<<2)|0;i=H[j>>2];g=H[i>>2];b=b-g|0}Y=p+w|0;Kh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=H[i+4>>2];if(b&32768){break Kh}m=H[i+4>>2];g=b>>>0<g>>>0;H[j>>2]=H[(g?12:8)+i>>2];while(1){Lh:{if(h){break Lh}f=H[e+16>>2];l=f+1|0;i=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=l;h=8;d=(i<<8)+d|0;break Lh}if(i>>>0<=143){H[e+16>>2]=l;d=(i<<9)+d|0;h=7;break Lh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Kh}m=H[i+4>>2];l=b>>>0<g>>>0;H[j>>2]=H[(l?8:12)+i>>2];while(1){Mh:{if(h){break Mh}f=H[e+16>>2];b=f+1|0;i=I[f+1|0];if(I[f|0]!=255){H[e+16>>2]=b;h=8;d=(i<<8)+d|0;break Mh}if(i>>>0<=143){H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Mh}H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=I[o+24640|0];H[Y>>2]=(l|0)==(g|0)?A:s;H[t>>2]=H[t>>2]|16384;H[k+4>>2]=H[k+4>>2]|4096;f=k+(H[e+124>>2]<<2)|0;H[f+4>>2]=H[f+4>>2]|4;H[f+12>>2]=H[f+12>>2]|1;g=g^l;H[f+8>>2]=H[f+8>>2]|g<<18|2;c=g<<28|c|8192}H[k>>2]=c&-1226833921}c=k+4|0;p=p+4|0;v=v+1|0;if((X|0)!=(v|0)){continue}break}c=k+12|0;p=p+w|0;r=r+4|0;g=H[e+128>>2];if(r>>>0<(g&-4)>>>0){continue}break}break Eg}c=(g&-4)-1|0;r=(c&-4)+4|0;c=(l+(c<<1&-8)|0)+20|0}H[e+8>>2]=h;H[e+4>>2]=b;H[e>>2]=d;H[e+104>>2]=j;if(!X|g>>>0<=r>>>0){break je}while(1){h=0;if(H[e+128>>2]!=(r|0)){while(1){gc(e,c,(N(h,X)<<2)+p|0,A,h,0);h=h+1|0;if(h>>>0<H[e+128>>2]-r>>>0){continue}break}}H[c>>2]=H[c>>2]&-1226833921;p=p+4|0;c=c+4|0;M=M+1|0;if((X|0)!=(M|0)){continue}break}break je}while(1){u=0;while(1){k=c;f=g;g=H[g>>2];Nh:{Oh:{Ph:{if(!g){j=H[l>>2];g=H[j>>2];b=b-g|0;Qh:{if(d>>>16>>>0<g>>>0){n=H[j+4>>2];c=b>>>0<g>>>0;H[l>>2]=H[j+(c?8:12)>>2];while(1){Rh:{if(h){break Rh}j=H[e+16>>2];b=j+1|0;p=I[j+1|0];if(I[j|0]==255){if(p>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Rh}H[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Rh}H[e+16>>2]=b;h=8;d=(p<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?n:!n;break Qh}d=d-(g<<16)|0;if(!(b&32768)){n=H[j+4>>2];c=b>>>0<g>>>0;H[l>>2]=H[j+(c?12:8)>>2];while(1){Sh:{if(h){break Sh}j=H[e+16>>2];g=j+1|0;p=I[j+1|0];if(I[j|0]==255){if(p>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Sh}H[e+16>>2]=g;d=(p<<9)+d|0;h=7;break Sh}H[e+16>>2]=g;h=8;d=(p<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!n:n;break Qh}c=H[j+4>>2]}if(!c){j=l;break Nh}c=H[q>>2];g=H[c>>2];b=b-g|0;Th:{if(d>>>16>>>0<g>>>0){p=H[c+4>>2];j=b>>>0<g>>>0;c=H[(j?8:12)+c>>2];H[q>>2]=c;while(1){Uh:{if(h){break Uh}n=H[e+16>>2];b=n+1|0;i=I[n+1|0];if(I[n|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Uh}H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Uh}H[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;n=j?p:!p;break Th}d=d-(g<<16)|0;if(!(b&32768)){p=H[c+4>>2];g=b>>>0<g>>>0;c=H[(g?12:8)+c>>2];H[q>>2]=c;while(1){Vh:{if(h){break Vh}n=H[e+16>>2];j=n+1|0;i=I[n+1|0];if(I[n|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Vh}H[e+16>>2]=j;d=(i<<9)+d|0;h=7;break Vh}H[e+16>>2]=j;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=g?!p:p;break Th}n=H[c+4>>2]}g=H[c>>2];b=b-g|0;Wh:{if(d>>>16>>>0<g>>>0){p=H[c+4>>2];j=c;c=b>>>0<g>>>0;H[q>>2]=H[j+(c?8:12)>>2];while(1){Xh:{if(h){break Xh}j=H[e+16>>2];b=j+1|0;i=I[j+1|0];if(I[j|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Xh}H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Xh}H[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?p:!p;break Wh}d=d-(g<<16)|0;if(!(b&32768)){p=H[c+4>>2];j=c;c=b>>>0<g>>>0;H[q>>2]=H[j+(c?12:8)>>2];while(1){Yh:{if(h){break Yh}j=H[e+16>>2];g=j+1|0;i=I[j+1|0];if(I[j|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Yh}H[e+16>>2]=g;d=(i<<9)+d|0;h=7;break Yh}H[e+16>>2]=g;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!p:p;break Wh}c=H[c+4>>2]}g=0;j=q;Zh:{_h:{$h:{ai:{bi:{switch(c|n<<1){case 0:i=f-4|0;j=H[f+4>>2]>>>17&4|H[i>>2]>>>19&1;c=s+(I[j+24384|0]<<2)|0;n=H[c>>2];g=H[n>>2];b=b-g|0;ci:{if(d>>>16>>>0<g>>>0){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?8:12)>>2];while(1){di:{if(h){break di}n=H[e+16>>2];b=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break di}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break di}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;g=c?p:!p;break ci}d=d-(g<<16)|0;if(!(b&32768)){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?12:8)>>2];while(1){ei:{if(h){break ei}n=H[e+16>>2];g=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ei}H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break ei}H[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}g=c?!p:p;break ci}g=H[n+4>>2]}c=I[j+24640|0];H[k>>2]=(g|0)==(c|0)?v:w;H[i>>2]=H[i>>2]|32;H[f+4>>2]=H[f+4>>2]|8;j=(c^g)<<19;i=H[e+108>>2];c=s+(I[i+2|0]<<2)|0;n=H[c>>2];g=H[n>>2];b=b-g|0;fi:{if(d>>>16>>>0<g>>>0){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?8:12)>>2];while(1){gi:{if(h){break gi}n=H[e+16>>2];b=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break gi}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break gi}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?p:!p;break fi}d=d-(g<<16)|0;if(!(b&32768)){p=H[n+4>>2];A=c;c=b>>>0<g>>>0;H[A>>2]=H[n+(c?12:8)>>2];while(1){hi:{if(h){break hi}n=H[e+16>>2];g=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break hi}H[e+16>>2]=g;d=(m<<9)+d|0;h=7;break hi}H[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!p:p;break fi}c=H[n+4>>2]}g=j|16;if(!c){break ai}break;case 1:break bi;case 2:break $h;case 3:break Zh;default:break Oh}}m=f-4|0;n=H[f+4>>2]>>>20&4|(H[m>>2]>>>22&1|(g>>>15&16|(g>>>19&64|g>>>3&170)));j=s+(I[n+24384|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;ii:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];A=j;j=b>>>0<c>>>0;H[A>>2]=H[p+(j?8:12)>>2];while(1){ji:{if(h){break ji}p=H[e+16>>2];b=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ji}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break ji}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break ii}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){ki:{if(h){break ki}p=H[e+16>>2];j=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ki}H[e+16>>2]=j;d=(o<<9)+d|0;h=7;break ki}H[e+16>>2]=j;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break ii}j=H[p+4>>2]}c=I[n+24640|0];H[k+256>>2]=(j|0)==(c|0)?v:w;H[m>>2]=H[m>>2]|256;H[f+4>>2]=H[f+4>>2]|64;i=H[e+108>>2];g=(c^j)<<22|g|128}j=s+(I[(g>>>6&495)+i|0]<<2)|0;n=H[j>>2];c=H[n>>2];b=b-c|0;li:{if(d>>>16>>>0<c>>>0){p=H[n+4>>2];A=j;j=b>>>0<c>>>0;H[A>>2]=H[n+(j?8:12)>>2];while(1){mi:{if(h){break mi}n=H[e+16>>2];b=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break mi}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break mi}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=j?p:!p;break li}d=d-(c<<16)|0;if(!(b&32768)){p=H[n+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[n+(c?12:8)>>2];while(1){ni:{if(h){break ni}n=H[e+16>>2];j=n+1|0;m=I[n+1|0];if(I[n|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ni}H[e+16>>2]=j;d=(m<<9)+d|0;h=7;break ni}H[e+16>>2]=j;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!p:p;break li}c=H[n+4>>2]}if(!c){break _h}}m=f-4|0;n=H[f+4>>2]>>>23&4|(H[m>>2]>>>25&1|(g>>>18&16|(g>>>22&64|g>>>6&170)));j=s+(I[n+24384|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;oi:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];A=j;j=b>>>0<c>>>0;H[A>>2]=H[p+(j?8:12)>>2];while(1){pi:{if(h){break pi}p=H[e+16>>2];b=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break pi}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break pi}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break oi}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){qi:{if(h){break qi}p=H[e+16>>2];j=p+1|0;o=I[p+1|0];if(I[p|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break qi}H[e+16>>2]=j;d=(o<<9)+d|0;h=7;break qi}H[e+16>>2]=j;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break oi}j=H[p+4>>2]}c=I[n+24640|0];H[k+512>>2]=(j|0)==(c|0)?v:w;H[m>>2]=H[m>>2]|2048;H[f+4>>2]=H[f+4>>2]|512;g=(c^j)<<25|g|1024;i=H[e+108>>2]}j=s+(I[(g>>>9&495)+i|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;ri:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[p+(n?8:12)>>2];while(1){si:{if(h){break si}p=H[e+16>>2];b=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break si}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break si}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break ri}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){ti:{if(h){break ti}p=H[e+16>>2];n=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break ti}H[e+16>>2]=n;d=(m<<9)+d|0;h=7;break ti}H[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break ri}c=H[p+4>>2]}if(!c){break Oh}}M=f-4|0;t=H[f+4>>2]>>>26&4|(H[M>>2]>>>28&1|(g>>>21&16|(g>>>25&64|g>>>9&170)));j=s+(I[t+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;break Ph}ui:{if(g&2097168){break ui}j=s+(I[H[e+108>>2]+(g&495)|0]<<2)|0;p=H[j>>2];c=H[p>>2];b=b-c|0;vi:{if(d>>>16>>>0<c>>>0){i=H[p+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[p+(n?8:12)>>2];while(1){wi:{if(h){break wi}p=H[e+16>>2];b=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break wi}H[e+16>>2]=b;d=(m<<9)+d|0;h=7;break wi}H[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break vi}d=d-(c<<16)|0;if(!(b&32768)){i=H[p+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[p+(c?12:8)>>2];while(1){xi:{if(h){break xi}p=H[e+16>>2];n=p+1|0;m=I[p+1|0];if(I[p|0]==255){if(m>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break xi}H[e+16>>2]=n;d=(m<<9)+d|0;h=7;break xi}H[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break vi}c=H[p+4>>2]}if(!c){break ui}o=f-4|0;p=H[f+4>>2]>>>17&4|(H[o>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));j=s+(I[p+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;yi:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){zi:{if(h){break zi}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break zi}H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break zi}H[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break yi}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){Ai:{if(h){break Ai}i=H[e+16>>2];n=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ai}H[e+16>>2]=n;d=(t<<9)+d|0;h=7;break Ai}H[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break yi}n=H[i+4>>2]}c=I[p+24640|0];H[k>>2]=(n|0)==(c|0)?v:w;H[o>>2]=H[o>>2]|32;H[f+4>>2]=H[f+4>>2]|8;g=(c^n)<<19|g|16}Bi:{if(g&16777344){break Bi}p=g>>>3|0;j=s+(I[H[e+108>>2]+(p&495)|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;Ci:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){Di:{if(h){break Di}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Di}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Di}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break Ci}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){Ei:{if(h){break Ei}i=H[e+16>>2];n=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ei}H[e+16>>2]=n;d=(o<<9)+d|0;h=7;break Ei}H[e+16>>2]=n;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break Ci}c=H[i+4>>2]}if(!c){break Bi}o=f-4|0;p=H[f+4>>2]>>>20&4|(H[o>>2]>>>22&1|(g>>>15&16|(g>>>19&64|p&170)));j=s+(I[p+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;Fi:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){Gi:{if(h){break Gi}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Gi}H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Gi}H[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break Fi}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){Hi:{if(h){break Hi}i=H[e+16>>2];n=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Hi}H[e+16>>2]=n;d=(t<<9)+d|0;h=7;break Hi}H[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break Fi}n=H[i+4>>2]}c=I[p+24640|0];H[k+256>>2]=(n|0)==(c|0)?v:w;H[o>>2]=H[o>>2]|256;H[f+4>>2]=H[f+4>>2]|64;g=(c^n)<<22|g|128}Ii:{if(g&134218752){break Ii}p=g>>>6|0;j=s+(I[H[e+108>>2]+(p&495)|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;Ji:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){Ki:{if(h){break Ki}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ki}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Ki}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break Ji}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){Li:{if(h){break Li}i=H[e+16>>2];n=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Li}H[e+16>>2]=n;d=(o<<9)+d|0;h=7;break Li}H[e+16>>2]=n;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break Ji}c=H[i+4>>2]}if(!c){break Ii}o=f-4|0;p=H[f+4>>2]>>>23&4|(H[o>>2]>>>25&1|(g>>>18&16|(g>>>22&64|p&170)));j=s+(I[p+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;Mi:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){Ni:{if(h){break Ni}i=H[e+16>>2];b=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ni}H[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Ni}H[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break Mi}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){Oi:{if(h){break Oi}i=H[e+16>>2];n=i+1|0;t=I[i+1|0];if(I[i|0]==255){if(t>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Oi}H[e+16>>2]=n;d=(t<<9)+d|0;h=7;break Oi}H[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break Mi}n=H[i+4>>2]}c=I[p+24640|0];H[k+512>>2]=(n|0)==(c|0)?v:w;H[o>>2]=H[o>>2]|2048;H[f+4>>2]=H[f+4>>2]|512;g=(c^n)<<25|g|1024}if(g&1073750016){break Oh}p=g>>>9|0;j=s+(I[H[e+108>>2]+(p&495)|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0;Pi:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[i+(n?8:12)>>2];while(1){Qi:{if(h){break Qi}i=H[e+16>>2];b=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Qi}H[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Qi}H[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break Pi}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[i+(c?12:8)>>2];while(1){Ri:{if(h){break Ri}i=H[e+16>>2];n=i+1|0;o=I[i+1|0];if(I[i|0]==255){if(o>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ri}H[e+16>>2]=n;d=(o<<9)+d|0;h=7;break Ri}H[e+16>>2]=n;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break Pi}c=H[i+4>>2]}if(!c){break Oh}M=f-4|0;t=H[f+4>>2]>>>26&4|(H[M>>2]>>>28&1|(g>>>21&16|(g>>>25&64|p&170)));j=s+(I[t+24384|0]<<2)|0;i=H[j>>2];c=H[i>>2];b=b-c|0}Si:{if(d>>>16>>>0<c>>>0){m=H[i+4>>2];n=b>>>0<c>>>0;H[j>>2]=H[(n?8:12)+i>>2];while(1){Ti:{if(h){break Ti}p=H[e+16>>2];b=p+1|0;i=I[p+1|0];if(I[p|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ti}H[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Ti}H[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break Si}d=d-(c<<16)|0;if(!(b&32768)){m=H[i+4>>2];c=b>>>0<c>>>0;H[j>>2]=H[(c?12:8)+i>>2];while(1){Ui:{if(h){break Ui}p=H[e+16>>2];n=p+1|0;i=I[p+1|0];if(I[p|0]==255){if(i>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;d=d+65280|0;h=8;break Ui}H[e+16>>2]=n;d=(i<<9)+d|0;h=7;break Ui}H[e+16>>2]=n;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break Si}n=H[i+4>>2]}c=I[t+24640|0];H[k+768>>2]=(n|0)==(c|0)?v:w;H[M>>2]=H[M>>2]|16384;H[f+4>>2]=H[f+4>>2]|4096;H[f+260>>2]=H[f+260>>2]|4;H[f+268>>2]=H[f+268>>2]|1;c=c^n;H[f+264>>2]=H[f+264>>2]|c<<18|2;g=c<<28|g|8192}H[f>>2]=g&-1226833921}g=f+4|0;c=k+4|0;u=u+1|0;if((u|0)!=64){continue}break}g=f+12|0;c=k+772|0;n=r>>>0<60;r=r+4|0;if(n){continue}break}}H[e+8>>2]=h;H[e+4>>2]=b;H[e>>2]=d;H[e+104>>2]=j}Vi:{if(!(Z&32)){break Vi}H[e+104>>2]=e+100;g=H[e+100>>2];b=H[g>>2];d=H[e+4>>2]-b|0;H[e+4>>2]=d;h=H[e>>2];Wi:{if(h>>>16>>>0<b>>>0){H[e+4>>2]=b;g=H[(b>>>0>d>>>0?8:12)+g>>2];H[e+100>>2]=g;d=H[e+8>>2];while(1){Xi:{if(d){break Xi}l=H[e+16>>2];c=l+1|0;j=I[l+1|0];if(I[l|0]==255){if(j>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;d=8;break Xi}H[e+16>>2]=c;h=(j<<9)+h|0;d=7;break Xi}H[e+16>>2]=c;d=8;h=(j<<8)+h|0}d=d-1|0;H[e+8>>2]=d;h=h<<1;H[e>>2]=h;b=b<<1;H[e+4>>2]=b;if(b>>>0<32768){continue}break}d=b;break Wi}h=h-(b<<16)|0;H[e>>2]=h;if(d&32768){break Wi}g=H[(b>>>0>d>>>0?12:8)+g>>2];H[e+100>>2]=g;b=H[e+8>>2];while(1){Yi:{if(b){break Yi}c=H[e+16>>2];b=c+1|0;l=I[c+1|0];if(I[c|0]==255){if(l>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;b=8;break Yi}H[e+16>>2]=b;h=(l<<9)+h|0;b=7;break Yi}H[e+16>>2]=b;b=8;h=(l<<8)+h|0}b=b-1|0;H[e+8>>2]=b;h=h<<1;H[e>>2]=h;d=d<<1;H[e+4>>2]=d;if(d>>>0<32768){continue}break}}b=H[g>>2];d=d-b|0;H[e+4>>2]=d;Zi:{if(h>>>16>>>0<b>>>0){H[e+4>>2]=b;g=H[(b>>>0>d>>>0?8:12)+g>>2];H[e+100>>2]=g;d=H[e+8>>2];while(1){_i:{if(d){break _i}l=H[e+16>>2];c=l+1|0;j=I[l+1|0];if(I[l|0]==255){if(j>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;d=8;break _i}H[e+16>>2]=c;h=(j<<9)+h|0;d=7;break _i}H[e+16>>2]=c;d=8;h=(j<<8)+h|0}d=d-1|0;H[e+8>>2]=d;h=h<<1;H[e>>2]=h;b=b<<1;H[e+4>>2]=b;if(b>>>0<32768){continue}break}d=b;break Zi}h=h-(b<<16)|0;H[e>>2]=h;if(d&32768){break Zi}g=H[(b>>>0>d>>>0?12:8)+g>>2];H[e+100>>2]=g;b=H[e+8>>2];while(1){$i:{if(b){break $i}c=H[e+16>>2];b=c+1|0;l=I[c+1|0];if(I[c|0]==255){if(l>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;b=8;break $i}H[e+16>>2]=b;h=(l<<9)+h|0;b=7;break $i}H[e+16>>2]=b;b=8;h=(l<<8)+h|0}b=b-1|0;H[e+8>>2]=b;h=h<<1;H[e>>2]=h;d=d<<1;H[e+4>>2]=d;if(d>>>0<32768){continue}break}}b=H[g>>2];d=d-b|0;H[e+4>>2]=d;aj:{if(h>>>16>>>0<b>>>0){H[e+4>>2]=b;g=H[(b>>>0>d>>>0?8:12)+g>>2];H[e+100>>2]=g;d=H[e+8>>2];while(1){bj:{if(d){break bj}l=H[e+16>>2];c=l+1|0;j=I[l+1|0];if(I[l|0]==255){if(j>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;d=8;break bj}H[e+16>>2]=c;h=(j<<9)+h|0;d=7;break bj}H[e+16>>2]=c;d=8;h=(j<<8)+h|0}d=d-1|0;H[e+8>>2]=d;h=h<<1;H[e>>2]=h;b=b<<1;H[e+4>>2]=b;if(b>>>0<32768){continue}break}d=b;break aj}h=h-(b<<16)|0;H[e>>2]=h;if(d&32768){break aj}g=H[(b>>>0>d>>>0?12:8)+g>>2];H[e+100>>2]=g;b=H[e+8>>2];while(1){cj:{if(b){break cj}c=H[e+16>>2];b=c+1|0;l=I[c+1|0];if(I[c|0]==255){if(l>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;b=8;break cj}H[e+16>>2]=b;h=(l<<9)+h|0;b=7;break cj}H[e+16>>2]=b;b=8;h=(l<<8)+h|0}b=b-1|0;H[e+8>>2]=b;h=h<<1;H[e>>2]=h;d=d<<1;H[e+4>>2]=d;if(d>>>0<32768){continue}break}}b=H[g>>2];d=d-b|0;H[e+4>>2]=d;if(h>>>16>>>0<b>>>0){H[e+4>>2]=b;H[e+100>>2]=H[(b>>>0>d>>>0?8:12)+g>>2];d=H[e+8>>2];while(1){dj:{if(d){break dj}g=H[e+16>>2];c=g+1|0;l=I[g+1|0];if(I[g|0]==255){if(l>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;h=h+65280|0;d=8;break dj}H[e+16>>2]=c;h=(l<<9)+h|0;d=7;break dj}H[e+16>>2]=c;d=8;h=(l<<8)+h|0}d=d-1|0;H[e+8>>2]=d;h=h<<1;H[e>>2]=h;b=b<<1;H[e+4>>2]=b;if(b>>>0<32768){continue}break}break Vi}c=h-(b<<16)|0;H[e>>2]=c;if(d&32768){break Vi}H[e+100>>2]=H[(b>>>0>d>>>0?12:8)+g>>2];h=H[e+8>>2];while(1){ej:{if(h){break ej}g=H[e+16>>2];b=g+1|0;l=I[g+1|0];if(I[g|0]==255){if(l>>>0>=144){H[e+12>>2]=H[e+12>>2]+1;c=c+65280|0;h=8;break ej}H[e+16>>2]=b;c=(l<<9)+c|0;h=7;break ej}H[e+16>>2]=b;h=8;c=(l<<8)+c|0}h=h-1|0;H[e+8>>2]=h;c=c<<1;H[e>>2]=c;d=d<<1;H[e+4>>2]=d;if(d>>>0<32768){continue}break}}}if(!P){break Za}ic(e);_a(e,18,46);_a(e,17,3);_a(e,0,4)}b=_+1|0;c=(b|0)==3;_=c?0:b;z=z-c|0;T=T+1|0;if(T>>>0>=K[ma+8>>2]){break Ya}if((z|0)>0){continue}break}}U=x+U|0;c=H[e+24>>2];b=J[e+112>>1];F[c|0]=b;F[c+1|0]=b>>>8;S=S+1|0;if(S>>>0<K[E+44>>2]){continue}break}}fj:{if(!W){break fj}gj:{c=H[e+24>>2];g=H[e+16>>2];if(c>>>0>g+2>>>0){if(!ba){break gj}g=H[e+16>>2];c=H[e+24>>2];b=H[e+20>>2];H[aa+56>>2]=c-b;H[aa+52>>2]=g-b;H[aa+48>>2]=(c-g|0)-2;Ca(R,2,15235,aa+48|0);break fj}b=H[e+12>>2];if(b>>>0<3){break fj}if(ba){H[aa+80>>2]=H[e+12>>2];Ca(R,2,7107,aa+80|0);break fj}H[aa+64>>2]=b;Ca(R,2,7107,aa- -64|0);break fj}b=H[e+20>>2];H[aa+40>>2]=c-b;H[aa+36>>2]=g-b;H[aa+32>>2]=(c-g|0)-2;Ca(R,2,15235,aa+32|0)}if(!H[E+60>>2]){break i}H[e+116>>2]=$}l=H[ta+4>>2];g=H[E+12>>2];m=H[E+8>>2]-H[ta>>2]|0;c=H[ta+16>>2];if(c&1){b=H[qa+28>>2]+N(va,152)|0;m=(H[b-144>>2]+m|0)-H[b-152>>2]|0}j=g-l|0;if(c&2){b=H[qa+28>>2]+N(va,152)|0;j=(H[b-140>>2]+j|0)-H[b-148>>2]|0}k=H[E+60>>2];i=k;i=i?i:H[e+116>>2];o=H[e+128>>2];P=H[e+124>>2];n=H[ra+808>>2];hj:{if(!n){break hj}b=!o|!P;if((n|0)<=30){if(b){break hj}h=0;while(1){l=(N(h,P)<<2)+i|0;b=0;while(1){g=l+(b<<2)|0;q=H[g>>2];c=q>>31;c=(c^q)-c|0;if(c>>>n|0){c=c>>>H[ra+808>>2]|0;H[g>>2]=(q|0)<0?0-c|0:c}b=b+1|0;if((P|0)!=(b|0)){continue}break}h=h+1|0;if((o|0)!=(h|0)){continue}break}break hj}if(b){break hj}b=N(o,P)<<2;if(!b){break hj}y(i,0,b)}if(k){q=N(o,P);if(H[ra+20>>2]==1){if(!q){break a}b=0;if((q|0)!=1){j=i+4|0;l=q&-2;e=0;while(1){g=b<<2;c=g+i|0;H[c>>2]=H[c>>2]/2;c=g+j|0;H[c>>2]=H[c>>2]/2;b=b+2|0;e=e+2|0;if((l|0)!=(e|0)){continue}break}}if(!(q&1)){break a}b=(b<<2)+i|0;H[b>>2]=H[b>>2]/2;break a}if(!q){break a}ga=O(L[ta+32>>2]*O(.5));if(q>>>0>=4){c=q&-4;b=0;while(1){L[i>>2]=ga*O(H[i>>2]);L[i+4>>2]=ga*O(H[i+4>>2]);L[i+8>>2]=ga*O(H[i+8>>2]);L[i+12>>2]=ga*O(H[i+12>>2]);i=i+16|0;b=b+4|0;if((c|0)!=(b|0)){continue}break}}c=q&3;if(!c){break a}b=0;while(1){L[i>>2]=ga*O(H[i>>2]);i=i+4|0;b=b+1|0;if((c|0)!=(b|0)){continue}break}break a}r=xa-wa|0;if(H[ra+20>>2]==1){if(!o){break a}f=(H[qa+36>>2]+(N(j,r)<<2)|0)+(m<<2)|0;d=P&-4;j=0;while(1){b=0;if(d){k=f+(N(j,r)<<2)|0;n=(N(j,P)<<2)+i|0;while(1){q=b<<2;p=q+n|0;l=H[p+4>>2];g=H[p+8>>2];c=H[p+12>>2];q=k+q|0;H[q>>2]=H[p>>2]/2;H[q+12>>2]=(c|0)/2;H[q+8>>2]=(g|0)/2;H[q+4>>2]=(l|0)/2;b=b+4|0;if(d>>>0>b>>>0){continue}break}}ij:{if(b>>>0>=P>>>0){break ij}c=b+1|0;l=f+(N(j,r)<<2)|0;g=(N(j,P)<<2)+i|0;if(P-b&1){b=b<<2;H[b+l>>2]=H[b+g>>2]/2;b=c}if((c|0)==(P|0)){break ij}while(1){c=b<<2;H[c+l>>2]=H[c+g>>2]/2;c=c+4|0;H[c+l>>2]=H[c+g>>2]/2;b=b+2|0;if((P|0)!=(b|0)){continue}break}}j=j+1|0;if((o|0)!=(j|0)){continue}break}break a}if(!o|!P){break a}ga=O(L[ta+32>>2]*O(.5));j=(H[qa+36>>2]+(N(j,r)<<2)|0)+(m<<2)|0;g=P&-4;l=P&3;f=0;c=P-1>>>0<3;while(1){b=j;e=0;if(!c){while(1){L[b>>2]=ga*O(H[i>>2]);L[b+4>>2]=ga*O(H[i+4>>2]);L[b+8>>2]=ga*O(H[i+8>>2]);L[b+12>>2]=ga*O(H[i+12>>2]);b=b+16|0;i=i+16|0;e=e+4|0;if((g|0)!=(e|0)){continue}break}}e=0;if(l){while(1){L[b>>2]=ga*O(H[i>>2]);b=b+4|0;i=i+4|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}j=(r<<2)+j|0;f=f+1|0;if((o|0)!=(f|0)){continue}break}break a}H[aa>>2]=z;Ca(R,2,8716,aa)}H[H[d>>2]>>2]=0}Da(a);oa=aa+96|0}
function gb(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,z=0,A=0,C=0,D=0,E=0,J=0,M=0,Q=0,R=0,S=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=O(0),ia=0,ja=0,ka=0,la=0,ma=0,na=0,pa=0,qa=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ha=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ta=0,Va=0,Ya=0,Za=0,_a=0,$a=0,bb=0,cb=0,db=0,eb=O(0),fb=O(0),gb=0,jb=0,kb=0,lb=0,nb=0,ob=0,pb=0,sb=O(0),tb=0;Za=oa-16|0;oa=Za;a:{if(!(I[a+8|0]&128)|H[a+228>>2]!=(b|0)){break a}Ya=H[a+180>>2]+N(b,5644)|0;q=H[Ya+5596>>2];if(!q){mb(Ya);break a}j=H[a+100>>2];if(!j){j=H[a+96>>2]}k=H[j>>2];g=H[j+4>>2];r=H[j+8>>2];i=H[j+12>>2];m=H[a+60>>2];l=H[a+64>>2];j=H[Ya+5600>>2];Ha=oa-16|0;oa=Ha;R=H[a+232>>2];H[R+36>>2]=b;h=H[H[R+28>>2]+76>>2];H[R+64>>2]=1;H[R+60>>2]=i;H[R+56>>2]=r;H[R+52>>2]=g;H[R+48>>2]=k;H[R+32>>2]=h+N(b,5644);Da(H[R+68>>2]);r=0;H[R+68>>2]=0;b:{if(m){r=Fa(4,H[H[R+24>>2]+16>>2]);if(!r){break b}if(m>>>0>=4){k=l+12|0;g=l+8|0;i=l+4|0;h=m&-4;b=0;while(1){o=$<<2;H[(H[o+l>>2]<<2)+r>>2]=1;H[(H[i+o>>2]<<2)+r>>2]=1;H[(H[g+o>>2]<<2)+r>>2]=1;H[(H[k+o>>2]<<2)+r>>2]=1;$=$+4|0;b=b+4|0;if((h|0)!=(b|0)){continue}break}}b=m&3;if(b){while(1){H[(H[l+($<<2)>>2]<<2)+r>>2]=1;$=$+1|0;E=E+1|0;if((b|0)!=(E|0)){continue}break}}H[R+68>>2]=r}c:{t=H[R+24>>2];ia=H[t+16>>2];d:{if(!ia){break d}$=0;e:{while(1){f:{if(H[($<<2)+r>>2]?0:r){break f}g=H[t+24>>2]+N($,52)|0;p=H[g+4>>2];o=p-1|0;i=H[R+60>>2];h=o+i|0;l=0-!p|0;b=l;k=Ke(h,h>>>0<i>>>0?b+1|0:b,p,0);s=H[g>>2];g=s-1|0;i=H[R+56>>2];h=g+i|0;m=0-!s|0;b=m;i=Ke(h,h>>>0<i>>>0?b+1|0:b,s,0);h=H[R+52>>2];b=h+o|0;o=Ke(b,b>>>0<h>>>0?l+1|0:l,p,0);p=H[H[H[R+20>>2]>>2]+20>>2]+N($,76)|0;l=H[p+20>>2]-H[p+24>>2]|0;if(l>>>0>31){break f}h=H[R+48>>2];b=h+g|0;h=Ke(b,b>>>0<h>>>0?m+1|0:m,s,0);b=h-H[p>>2]|0;g:{if((b>>>0<=h>>>0?b:0)>>>l|0){break g}b=o-H[p+4>>2]|0;if((b>>>0<=o>>>0?b:0)>>>l|0){break g}h=H[p+8>>2];b=h-i|0;if((b>>>0<=h>>>0?b:0)>>>l|0){break g}h=H[p+12>>2];b=h-k|0;if(!((b>>>0<=h>>>0?b:0)>>>l|0)){break f}}H[R+64>>2]=0;break e}$=$+1|0;if((ia|0)!=($|0)){continue}break}if(!H[R+64>>2]){break e}E=0;while(1){o=H[H[H[R+20>>2]>>2]+20>>2]+N(E,76)|0;b=H[o+28>>2]+N(H[o+24>>2],152)|0;k=H[b-148>>2];g=H[b-140>>2];r=H[b-152>>2];i=H[b-144>>2];b=H[R+68>>2];h:{if(H[b+(E<<2)>>2]?0:b){break h}h=g-k|0;b=i-r|0;Ie(h,0,b);if(!(!ra|(g|0)==(k|0))){$=0;Ca(f,1,2982,0);break b}b=N(b,h);if(b>>>0>=1073741824){$=0;Ca(f,1,2982,0);break b}h=b<<2;H[o+44>>2]=h;i:{j:{k:{b=H[o+36>>2];if(b){if(h>>>0<=K[o+48>>2]){break h}if(H[o+40>>2]){break k}}b=Ja(h);H[o+36>>2]=b;h=b;b=H[o+44>>2];if(!(b?h:1)){break j}H[o+40>>2]=1;H[o+48>>2]=b;break h}Da(b);b=Ja(H[o+44>>2]);H[o+36>>2]=b;if(b){break i}H[o+48>>2]=0;H[o+40>>2]=0;H[o+44>>2]=0}$=0;Ca(f,1,2982,0);break b}H[o+40>>2]=1;H[o+48>>2]=H[o+44>>2]}E=E+1|0;t=H[R+24>>2];if(E>>>0<K[t+16>>2]){continue}break}break d}A=H[t+24>>2];Q=H[H[H[R+20>>2]>>2]+20>>2];b=0;while(1){l:{if(H[(b<<2)+r>>2]?0:r){break l}M=Q+N(b,76)|0;o=H[M>>2];k=A+N(b,52)|0;w=H[k>>2];m=w-1|0;g=H[R+48>>2];i=m+g|0;s=0-!w|0;h=s;h=Ke(i,g>>>0>i>>>0?h+1|0:h,w,0);W=h>>>0<o>>>0?o:h;H[M+56>>2]=W;o=H[M+4>>2];p=H[k+4>>2];k=p-1|0;g=H[R+52>>2];i=k+g|0;l=0-!p|0;h=l;h=Ke(i,g>>>0>i>>>0?h+1|0:h,p,0);z=h>>>0<o>>>0?o:h;H[M+60>>2]=z;g=H[M+8>>2];i=H[R+56>>2];h=i+m|0;h=Ke(h,h>>>0<i>>>0?s+1|0:s,w,0);o=g>>>0<h>>>0?g:h;H[M+64>>2]=o;g=H[M+12>>2];i=H[R+60>>2];h=k+i|0;h=Ke(h,h>>>0<i>>>0?l+1|0:l,p,0);h=g>>>0<h>>>0?g:h;H[M+68>>2]=h;if(o>>>0<W>>>0|h>>>0<z>>>0){break c}ca=H[M+20>>2];if(!ca){break l}X=h-1|0;C=0-!h|0;w=o-1|0;p=0-!o|0;Z=z-1|0;s=0-!z|0;z=W-1|0;m=0-!W|0;o=H[M+28>>2];k=0;i=0;while(1){fa=o+N(k,152)|0;D=ca+(k^-1)|0;g=D&31;if((D&63)>>>0>=32){l=1<<g;h=0}else{h=1<<g;l=h-1&1>>>32-g}M=h;h=X+M|0;g=l+C|0;g=h>>>0<X>>>0?g+1|0:g;W=D&31;if((D&63)>>>0>=32){h=g>>>W|0}else{h=((1<<W)-1&g)<<32-W|h>>>W}H[fa+148>>2]=h;h=l+p|0;v=h+1|0;g=h;h=w+M|0;g=h>>>0<M>>>0?v:g;W=D&31;if((D&63)>>>0>=32){h=g>>>W|0}else{h=((1<<W)-1&g)<<32-W|h>>>W}H[fa+144>>2]=h;h=l+s|0;v=h+1|0;g=h;h=M+Z|0;g=h>>>0<Z>>>0?v:g;W=D&31;if((D&63)>>>0>=32){h=g>>>W|0}else{h=((1<<W)-1&g)<<32-W|h>>>W}H[fa+140>>2]=h;h=l+m|0;l=h+1|0;g=h;h=z+M|0;g=h>>>0<z>>>0?l:g;l=D&31;if((D&63)>>>0>=32){h=g>>>l|0}else{h=((1<<l)-1&g)<<32-l|h>>>l}H[fa+136>>2]=h;k=k+1|0;i=k?i:i+1|0;if(i|(k|0)!=(ca|0)){continue}break}}b=b+1|0;if((ia|0)!=(b|0)){continue}break}}$=0;H[Ha+8>>2]=0;b=H[R+28>>2];da=Fa(1,8);if(da){H[da+4>>2]=b;H[da>>2]=t}if(!da){break b}m=H[H[R+20>>2]>>2];S=oa-144|0;oa=S;Q=H[R+36>>2];b=N(Q,5644);z=H[da+4>>2];va=b+H[z+76>>2]|0;ia=H[va+420>>2];t=0;g=0;Y=oa-32|0;oa=Y;sa=b+H[z+76>>2]|0;ya=H[sa+420>>2];ja=H[da>>2];ka=H[ja+16>>2];o=Ga(N(ka,528));m:{if(!o){break m}r=Ga(ka<<2);n:{if(!r){r=o;break n}s=H[z+76>>2]+N(Q,5644)|0;p=H[s+420>>2];b=p+1|0;h=Fa(b,240);o:{if(h){p:{if(b){ba=H[ja+16>>2];b=h;while(1){H[b+236>>2]=f;i=Fa(ba,16);H[b+200>>2]=i;if(!i){break p}i=H[ja+16>>2];H[b+196>>2]=i;v=0;ba=0;if(i){while(1){l=H[b+200>>2]+(v<<4)|0;k=H[s+5584>>2]+N(v,1080)|0;i=Fa(H[k+4>>2],16);H[l+12>>2]=i;if(!i){break p}H[l+8>>2]=H[k+4>>2];v=v+1|0;ba=H[ja+16>>2];if(v>>>0<ba>>>0){continue}break}}b=b+240|0;i=(n|0)==(p|0);n=n+1|0;if(!i){continue}break}}break o}b=H[h+4>>2];if(b){Da(b);H[h+4>>2]=0}b=h;n=0;while(1){v=H[b+200>>2];if(v){ba=0;k=H[b+196>>2];if(k){while(1){i=H[v+12>>2];if(i){Da(i);H[v+12>>2]=0;k=H[b+196>>2]}v=v+16|0;ba=ba+1|0;if(ba>>>0<k>>>0){continue}break}v=H[b+200>>2]}Da(v);H[b+200>>2]=0}b=b+240|0;i=(n|0)==(p|0);n=n+1|0;if(!i){continue}break}Da(h)}h=0}if(h){q:{if(!ka){break q}b=o;if(ka>>>0>=8){C=r+28|0;w=r+24|0;p=r+20|0;s=r+16|0;l=r+12|0;n=r+8|0;k=r+4|0;i=ka&-8;while(1){A=u<<2;H[A+r>>2]=b;H[k+A>>2]=b+528;H[n+A>>2]=b+1056;H[l+A>>2]=b+1584;H[s+A>>2]=b+2112;H[p+A>>2]=b+2640;H[w+A>>2]=b+3168;H[A+C>>2]=b+3696;u=u+8|0;b=b+4224|0;t=t+8|0;if((i|0)!=(t|0)){continue}break}}i=ka&7;if(!i){break q}while(1){H[(u<<2)+r>>2]=b;u=u+1|0;b=b+528|0;V=V+1|0;if((i|0)!=(V|0)){continue}break}}k=r;n=0;s=H[(H[z+76>>2]+N(Q,5644)|0)+5584>>2];v=H[ja+24>>2];b=H[z+24>>2];r=(Q>>>0)/(b>>>0)|0;i=H[z+4>>2]+N(H[z+12>>2],Q-N(b,r)|0)|0;b=H[ja>>2];H[Y+20>>2]=b>>>0<i>>>0?i:b;b=i+H[z+12>>2]|0;i=b>>>0<i>>>0?-1:b;b=H[ja+8>>2];H[Y+16>>2]=b>>>0>i>>>0?i:b;i=H[z+8>>2]+N(r,H[z+16>>2])|0;b=H[ja+4>>2];H[Y+12>>2]=b>>>0<i>>>0?i:b;b=i+H[z+16>>2]|0;i=b>>>0<i>>>0?-1:b;b=H[ja+12>>2];H[Y+8>>2]=b>>>0>i>>>0?i:b;H[Y+24>>2]=0;H[Y+28>>2]=0;H[Y+4>>2]=2147483647;H[Y>>2]=2147483647;if(H[ja+16>>2]){while(1){r=k?H[k+(n<<2)>>2]:0;Z=H[v+4>>2];t=Z-1|0;l=H[Y+8>>2];i=t+l|0;A=0-!Z|0;b=A;Q=Ke(i,i>>>0<l>>>0?b+1|0:b,Z,0);z=H[v>>2];p=z-1|0;l=H[Y+16>>2];i=p+l|0;C=0-!z|0;b=C;w=Ke(i,i>>>0<l>>>0?b+1|0:b,z,0);i=H[Y+12>>2];b=i+t|0;l=Ke(b,b>>>0<i>>>0?A+1|0:A,Z,0);i=H[Y+20>>2];b=i+p|0;b=Ke(b,b>>>0<i>>>0?C+1|0:C,z,0);V=H[s+4>>2];if(V>>>0>K[Y+28>>2]){H[Y+28>>2]=V;V=H[s+4>>2]}if(V){ca=s+944|0;X=s+812|0;na=Q-1|0;Z=0-!Q|0;D=w-1|0;z=0-!w|0;fa=l-1|0;A=0-!l|0;M=b-1|0;Q=0-!b|0;t=0;while(1){b=t<<2;u=H[b+ca>>2];pa=H[b+X>>2];ba=0;if(r){H[r+4>>2]=u;H[r>>2]=pa;ba=r+8|0}V=V-1|0;r=pa+V|0;r:{if(r>>>0>31){break r}b=H[v>>2];if(b>>>0>-1>>>r>>>0){break r}i=H[Y+4>>2];b=b<<r;H[Y+4>>2]=b>>>0>i>>>0?i:b}r=u+V|0;s:{if(r>>>0>31){break s}b=H[v+4>>2];if(b>>>0>-1>>>r>>>0){break s}i=H[Y>>2];b=b<<r;H[Y>>2]=b>>>0>i>>>0?i:b}r=0;i=V&31;if((V&63)>>>0>=32){l=1<<i;b=0}else{b=1<<i;l=b-1&1>>>32-i}_=b;b=na+_|0;i=l;l=Z+i|0;w=b>>>0<na>>>0?l+1|0:l;W=V&31;l=u&31;if((u&63)>>>0>=32){l=1<<l;p=0}else{p=1<<l;l=p-1&1>>>32-l}C=p;if((V&63)>>>0>=32){w=w>>>W|0}else{w=((1<<W)-1&w)<<32-W|b>>>W}b=C+w|0;p=b-1|0;C=(b>>>0<C>>>0?l+1|0:l)-!b|0;W=u&31;b=i+A|0;wa=b+1|0;l=b;b=_+fa|0;l=b>>>0<fa>>>0?wa:l;if((u&63)>>>0>=32){p=C>>>W|0}else{p=((1<<W)-1&C)<<32-W|p>>>W}C=p;p=V&31;if((V&63)>>>0>=32){b=l>>>p|0}else{b=((1<<p)-1&l)<<32-p|b>>>p}W=(b|0)!=(w|0)?C-(b>>>u|0)&-1>>>u:0;b=i+z|0;w=b+1|0;l=b;b=D+_|0;C=b>>>0<D>>>0?w:l;u=V&31;l=pa&31;if((pa&63)>>>0>=32){l=1<<l;w=0}else{p=1<<l;l=p-1&1>>>32-l;w=p}if((V&63)>>>0>=32){p=C>>>u|0}else{p=((1<<u)-1&C)<<32-u|b>>>u}b=w+p|0;w=(b>>>0<w>>>0?l+1|0:l)-!b|0;l=b-1|0;C=pa&31;b=i+Q|0;wa=b+1|0;i=b;b=M+_|0;i=b>>>0<M>>>0?wa:i;if((pa&63)>>>0>=32){l=w>>>C|0}else{l=((1<<C)-1&w)<<32-C|l>>>C}w=l;l=V&31;if((V&63)>>>0>=32){b=i>>>l|0}else{b=((1<<l)-1&i)<<32-l|b>>>l}b=(b|0)!=(p|0)?w-(b>>>pa|0)&-1>>>pa:0;if(ba){H[ba+4>>2]=W;H[ba>>2]=b;r=ba+8|0}b=N(b,W);if(b>>>0>K[Y+24>>2]){H[Y+24>>2]=b}t=t+1|0;if(t>>>0<K[s+4>>2]){continue}break}}v=v+52|0;s=s+1080|0;n=n+1|0;if(n>>>0<K[ja+16>>2]){continue}break}}l=ya+1|0;X=H[Y+28>>2];ca=H[Y+24>>2];H[h+4>>2]=0;b=H[sa+8>>2]+1|0;A=N(ca,ka);Z=N(A,X);Ie(b,0,Z);t:{if(!ra){b=N(b,Z);H[h+8>>2]=b;b=Fa(b,2);H[h+4>>2]=b;if(b){break t}}Da(o);Da(k);b=H[h+4>>2];if(b){Da(b);H[h+4>>2]=0}if(!l){r=h;break n}r=0;t=h;while(1){b=H[t+200>>2];if(b){n=0;u=H[t+196>>2];if(u){while(1){i=H[b+12>>2];if(i){Da(i);H[b+12>>2]=0;u=H[t+196>>2]}b=b+16|0;n=n+1|0;if(u>>>0>n>>>0){continue}break}b=H[t+200>>2]}Da(b);H[t+200>>2]=0}t=t+240|0;b=(r|0)==(ya|0);r=r+1|0;if(!b){continue}break}r=h;break n}g=H[ja+24>>2];Q=H[Y+20>>2];H[h+204>>2]=Q;C=H[Y+12>>2];H[h+208>>2]=C;w=H[Y+16>>2];H[h+212>>2]=w;p=H[Y+8>>2];H[h+216>>2]=p;H[h+12>>2]=Z;H[h+16>>2]=A;H[h+20>>2]=ca;v=1;H[h+24>>2]=1;if(ka){t=H[h+200>>2];s=0;r=g;while(1){b=H[k+(s<<2)>>2];H[t>>2]=H[r>>2];H[t+4>>2]=H[r+4>>2];z=H[t+8>>2];u:{if(!z){break u}n=H[t+12>>2];if((z|0)!=1){i=z&-2;u=0;while(1){H[n>>2]=H[b>>2];H[n+4>>2]=H[b+4>>2];H[n+8>>2]=H[b+8>>2];H[n+12>>2]=H[b+12>>2];H[n+16>>2]=H[b+16>>2];H[n+20>>2]=H[b+20>>2];H[n+24>>2]=H[b+24>>2];H[n+28>>2]=H[b+28>>2];n=n+32|0;b=b+32|0;u=u+2|0;if((i|0)!=(u|0)){continue}break}}if(!(z&1)){break u}H[n>>2]=H[b>>2];H[n+4>>2]=H[b+4>>2];H[n+8>>2]=H[b+8>>2];H[n+12>>2]=H[b+12>>2]}r=r+52|0;t=t+16|0;s=s+1|0;if((ka|0)!=(s|0)){continue}break}}if(l>>>0>1){i=h;while(1){H[i+456>>2]=p;H[i+452>>2]=w;H[i+448>>2]=C;H[i+444>>2]=Q;H[i+264>>2]=1;H[i+260>>2]=ca;H[i+256>>2]=A;H[i+252>>2]=Z;if(ka){t=H[i+440>>2];s=0;r=g;while(1){b=H[k+(s<<2)>>2];H[t>>2]=H[r>>2];H[t+4>>2]=H[r+4>>2];z=H[t+8>>2];v:{if(!z){break v}n=H[t+12>>2];if((z|0)!=1){l=z&-2;u=0;while(1){H[n>>2]=H[b>>2];H[n+4>>2]=H[b+4>>2];H[n+8>>2]=H[b+8>>2];H[n+12>>2]=H[b+12>>2];H[n+16>>2]=H[b+16>>2];H[n+20>>2]=H[b+20>>2];H[n+24>>2]=H[b+24>>2];H[n+28>>2]=H[b+28>>2];n=n+32|0;b=b+32|0;u=u+2|0;if((l|0)!=(u|0)){continue}break}}if(!(z&1)){break v}H[n>>2]=H[b>>2];H[n+4>>2]=H[b+4>>2];H[n+8>>2]=H[b+8>>2];H[n+12>>2]=H[b+12>>2]}r=r+52|0;t=t+16|0;s=s+1|0;if((ka|0)!=(s|0)){continue}break}}b=H[i+8>>2];H[i+244>>2]=H[i+4>>2];H[i+248>>2]=b;b=(v|0)!=(ya|0);i=i+240|0;v=v+1|0;if(b){continue}break}}Da(o);Da(k);k=H[sa+420>>2];w:{if(I[sa+5640|0]&4){if((k|0)==-1){break w}n=sa+424|0;r=H[sa+8>>2];u=0;b=h;while(1){i=H[n+36>>2];H[b+44>>2]=1;H[b+84>>2]=i;H[b+48>>2]=H[n>>2];i=H[n+4>>2];H[b+68>>2]=0;H[b+72>>2]=0;H[b+52>>2]=i;H[b+60>>2]=H[n+12>>2];H[b+64>>2]=H[n+16>>2];i=H[n+8>>2];H[b+76>>2]=ca;H[b+56>>2]=i>>>0<r>>>0?i:r;n=n+148|0;b=b+240|0;i=(k|0)==(u|0);u=u+1|0;if(!i){continue}break}break w}if((k|0)==-1){break w}g=H[sa+8>>2];r=H[sa+4>>2];b=h;if(k){i=k+1&-2;V=0;while(1){H[b+68>>2]=0;H[b+72>>2]=0;H[b+52>>2]=0;H[b+44>>2]=1;H[b+48>>2]=0;H[b+84>>2]=r;H[b+60>>2]=X;H[b+324>>2]=r;H[b+76>>2]=ca;H[b+56>>2]=g;H[b+308>>2]=0;H[b+312>>2]=0;H[b+292>>2]=0;H[b+284>>2]=1;H[b+288>>2]=0;H[b+300>>2]=X;H[b+296>>2]=g;H[b+316>>2]=ca;H[b+64>>2]=H[b+196>>2];H[b+304>>2]=H[b+436>>2];b=b+480|0;V=V+2|0;if((i|0)!=(V|0)){continue}break}}if(k&1){break w}H[b+68>>2]=0;H[b+72>>2]=0;H[b+52>>2]=0;H[b+44>>2]=1;H[b+48>>2]=0;H[b+84>>2]=r;H[b+60>>2]=X;H[b+76>>2]=ca;H[b+56>>2]=g;H[b+64>>2]=H[b+196>>2]}g=h;break m}Da(o)}Da(r)}oa=Y+32|0;h=g;x:{y:{if(!g){break y}A=ia+1|0;p=q;k=g;z:{A:{while(1){if(H[k+84>>2]==-1){break z}z=Ga(H[ja+16>>2]<<2);if(!z){break z}b=H[ja+16>>2]<<2;if(b){y(z,1,b)}if(fc(k)){while(1){E=H[m+20>>2];B:{C:{if(K[k+40>>2]>=K[va+12>>2]){break C}i=H[k+32>>2];b=N(H[k+28>>2],76)+E|0;if(i>>>0>=K[b+24>>2]){break C}r=H[b+28>>2]+N(i,152)|0;if(!H[r+24>>2]){break C}i=r+28|0;D=0;D:{while(1){b=i+N(D,36)|0;g=H[b+20>>2]+N(H[k+36>>2],40)|0;if(!Cb(R,H[k+28>>2],H[k+32>>2],H[b+16>>2],H[g>>2],H[g+4>>2],H[g+8>>2],H[g+12>>2])){D=D+1|0;if(D>>>0<K[r+24>>2]){continue}break D}break}H[z+(H[k+28>>2]<<2)>>2]=0;H[S+136>>2]=0;if(!ec(H[da+4>>2],H[m+20>>2],va,k,S+140|0,p,S+136|0,j,f)){break A}D=H[k+32>>2];o=H[k+28>>2];u=H[S+136>>2];if(H[S+140>>2]){H[S+136>>2]=0;C=H[(H[m+20>>2]+N(o,76)|0)+28>>2]+N(D,152)|0;E=H[C+24>>2];if(E){Q=j-u|0;w=j+p|0;o=C+28|0;X=0;g=0;l=p+u|0;v=l;while(1){E:{if(H[o+8>>2]==H[o>>2]|H[o+12>>2]==H[o+4>>2]){break E}b=H[o+20>>2]+N(H[k+36>>2],40)|0;r=N(H[b+20>>2],H[b+16>>2]);if(!r){break E}E=H[b+24>>2];V=0;while(1){t=H[E+36>>2];if(t){F:{if(g|H[E+64>>2]){H[E+52>>2]=0;D=1;b=64;break F}D=H[E>>2];b=H[E+40>>2];G:{if(b){D=N(b,24)+D|0;if(H[D-20>>2]!=H[D-12>>2]){D=D-24|0;break G}b=b+1|0}else{b=1}H[E+40>>2]=b}n=H[D+20>>2];H:{I:{if(n>>>0>(v^-1)>>>0){break I}i=D+20|0;while(1){if(w>>>0<n+v>>>0){break I}s=H[E+4>>2];g=H[E+52>>2];if((g|0)!=H[E+56>>2]){b=t}else{b=g<<1|1;s=Ia(s,b<<3);if(!s){Ca(f,1,1024,0);break A}H[E+56>>2]=b;H[E+4>>2]=s;g=H[E+52>>2];n=H[i>>2];b=H[E+36>>2]}i=(g<<3)+s|0;H[i+4>>2]=n;H[i>>2]=v;H[E+52>>2]=g+1;H[D>>2]=H[D>>2]+n;g=H[D+16>>2];i=g+H[D+4>>2]|0;H[D+4>>2]=i;t=b-g|0;H[E+36>>2]=t;H[D+8>>2]=i;v=n+v|0;i=0;if((b|0)==(g|0)){break H}H[E+40>>2]=H[E+40>>2]+1;i=D+44|0;n=H[D+44>>2];D=D+24|0;if((v^-1)>>>0>=n>>>0){continue}break}}g=H[k+28>>2];i=H[k+32>>2];b=H[k+36>>2];if(H[H[da+4>>2]+104>>2]){H[S+120>>2]=g;H[S+116>>2]=i;H[S+112>>2]=X;H[S+108>>2]=b;H[S+104>>2]=V;H[S+100>>2]=Q;H[S+96>>2]=n;Ca(f,1,14693,S+96|0);break A}H[S+88>>2]=g;H[S+84>>2]=i;H[S+80>>2]=X;H[S+76>>2]=b;H[S+72>>2]=V;H[S+68>>2]=Q;H[S+64>>2]=n;Ca(f,2,14693,S- -64|0);H[E+52>>2]=0;H[E+64>>2]=1;i=1}g=i;D=H[E+40>>2];b=44}H[b+E>>2]=D}E=E+68|0;V=V+1|0;if((r|0)!=(V|0)){continue}break}E=H[C+24>>2]}o=o+36|0;X=X+1|0;if(E>>>0>X>>>0){continue}break}D=H[k+32>>2];o=H[k+28>>2];b=g?Q:v-l|0}else{b=0}u=b+u|0}b=H[ja+24>>2]+N(o,52)|0;i=H[b+36>>2];H[b+36>>2]=i>>>0<D>>>0?D:i;break B}E=H[m+20>>2]}H[S+136>>2]=0;if(!ec(H[da+4>>2],E,va,k,S+140|0,p,S+136|0,j,f)){break A}o=H[k+28>>2];u=H[S+136>>2];if(!H[S+140>>2]){break B}w=H[k+32>>2];b=H[(H[m+20>>2]+N(o,76)|0)+28>>2]+N(w,152)|0;r=H[b+24>>2];if(!r){break B}Q=j-u|0;s=b+28|0;C=H[k+36>>2];D=0;g=0;J:{K:{while(1){L:{if(H[s+8>>2]==H[s>>2]|H[s+12>>2]==H[s+4>>2]){break L}b=H[s+20>>2]+N(C,40)|0;i=N(H[b+20>>2],H[b+16>>2]);if(!i){break L}t=H[b+24>>2];X=0;while(1){n=H[t+36>>2];if(n){E=H[t>>2];V=H[t+40>>2];M:{if(V){E=N(V,24)+E|0;if(H[E-20>>2]!=H[E-12>>2]){E=E-24|0;break M}V=V+1|0}else{V=1}H[t+40>>2]=V}v=H[E+20>>2];D=D+v|0;if(Q>>>0<D>>>0|v>>>0>D>>>0){break J}while(1){N:{l=H[E+16>>2];H[E+4>>2]=l+H[E+4>>2];b=n-l|0;if((l|0)==(n|0)){break N}V=V+1|0;H[t+40>>2]=V;v=H[E+44>>2];D=D+v|0;if(v>>>0>D>>>0){break K}E=E+24|0;n=b;if(D>>>0<=Q>>>0){continue}break K}break}H[t+36>>2]=b}t=t+68|0;X=X+1|0;if((i|0)!=(X|0)){continue}break}}s=s+36|0;g=g+1|0;if((r|0)!=(g|0)){continue}break}u=u+D|0;break B}H[t+36>>2]=b}if(!H[H[da+4>>2]+104>>2]){H[S+24>>2]=o;H[S+20>>2]=w;H[S+16>>2]=g;H[S+12>>2]=C;H[S+8>>2]=X;H[S+4>>2]=Q;H[S>>2]=v;Ca(f,2,14608,S);o=H[k+28>>2];u=u+Q|0;break B}H[S+56>>2]=o;H[S+52>>2]=w;H[S+48>>2]=g;H[S+44>>2]=C;H[S+40>>2]=X;H[S+36>>2]=Q;H[S+32>>2]=v;Ca(f,1,14608,S+32|0);break A}O:{if(!H[z+(o<<2)>>2]){break O}b=H[ja+24>>2]+N(o,52)|0;if(H[b+36>>2]){break O}H[b+36>>2]=H[(H[m+20>>2]+N(o,76)|0)+24>>2]-1}j=j-u|0;p=p+u|0;if(fc(k)){continue}break}}Da(z);k=k+240|0;ma=ma+1|0;if(ma>>>0<=K[va+420>>2]){continue}break}Db(h,A);H[Ha+8>>2]=p-q;b=1;break x}Db(h,A);Da(z);break y}Db(h,A)}b=0}oa=S+144|0;hb(da);if(!b){break b}$=H[H[R+32>>2]+5584>>2];s=H[H[R+20>>2]>>2];p=H[s+20>>2];H[Ha+12>>2]=1;E=0;b=H[R+32>>2];m=H[$+16>>2]>>>4&1&H[b+12>>2]==H[b+8>>2];t=H[s+16>>2];P:{if(!t){break P}while(1){b=H[R+68>>2];if(!(H[b+(E<<2)>>2]?0:b)){r=Ha+12|0;ma=0;qa=H[p+24>>2];Q:{if(!qa){break Q}l=H[R+44>>2];while(1){q=H[p+28>>2]+N(ma,152)|0;n=H[q+24>>2];if(n){j=q+28|0;qa=H[q+20>>2];g=H[q+16>>2];i=0;while(1){if(N(g,qa)){t=j+N(i,36)|0;k=0;while(1){w=H[t+20>>2]+N(k,40)|0;b=Cb(R,H[p+16>>2],ma,H[t+16>>2],H[w>>2],H[w+4>>2],H[w+8>>2],H[w+12>>2]);n=H[w+20>>2];o=H[w+16>>2];h=N(n,o);R:{if(b){if(!h){break R}o=0;while(1){h=H[w+24>>2]+N(o,68)|0;S:{if(!Cb(R,H[p+16>>2],ma,H[t+16>>2],H[h+8>>2],H[h+12>>2],H[h+16>>2],H[h+20>>2])){b=H[h+60>>2];if(!b){break S}Da(b);H[h+60>>2]=0;break S}if(!H[R+64>>2]){if(H[h+60>>2]|H[h+16>>2]==H[h+8>>2]|H[h+20>>2]==H[h+12>>2]){break S}}g=Fa(1,44);if(!g){H[Ha+12>>2]=0;break Q}b=H[R+64>>2];H[g+36>>2]=0;H[g+28>>2]=r;H[g+20>>2]=$;H[g+16>>2]=p;H[g+12>>2]=t;H[g+8>>2]=h;H[g+4>>2]=ma;H[g>>2]=b;H[g+40>>2]=m;H[g+32>>2]=f;H[g+24>>2]=H[l+4>>2]>1;ib(l,14,g);if(!H[Ha+12>>2]){break Q}}o=o+1|0;if(o>>>0<N(H[w+20>>2],H[w+16>>2])>>>0){continue}break}break R}if(!h){break R}g=0;while(1){h=H[w+24>>2]+N(g,68)|0;b=H[h+60>>2];if(b){Da(b);H[h+60>>2]=0;n=H[w+20>>2];o=H[w+16>>2]}g=g+1|0;if(g>>>0<N(n,o)>>>0){continue}break}}k=k+1|0;g=H[q+16>>2];qa=H[q+20>>2];if(k>>>0<N(g,qa)>>>0){continue}break}n=H[q+24>>2]}i=i+1|0;if(n>>>0>i>>>0){continue}break}qa=H[p+24>>2]}ma=ma+1|0;if(qa>>>0>ma>>>0){continue}break}}if(!H[Ha+12>>2]){break P}t=H[s+16>>2]}$=$+1080|0;p=p+76|0;E=E+1|0;if(E>>>0<t>>>0){continue}break}}$=0;Ua(H[R+44>>2]);if(!H[Ha+12>>2]){break b}T:{if(H[R+64>>2]){break T}E=H[R+24>>2];if(!H[E+16>>2]){break T}p=0;while(1){g=H[H[H[R+20>>2]>>2]+20>>2]+N(p,76)|0;b=H[g+28>>2]+N(H[(H[E+24>>2]+N(p,52)|0)+36>>2],152)|0;r=H[b+136>>2];i=H[b+144>>2];j=H[b+140>>2];h=H[b+148>>2];Da(H[g+52>>2]);H[g+52>>2]=0;U:{b=H[R+68>>2];if((i|0)==(r|0)|(h|0)==(j|0)|(H[b+(p<<2)>>2]?0:b)){break U}h=h-j|0;b=i-r|0;Ie(h,0,b);if(ra){Ca(f,1,2982,0);break b}b=N(b,h);if(b>>>0>=1073741824){Ca(f,1,2982,0);break b}b=Ja(b<<2);H[g+52>>2]=b;if(b){break U}Ca(f,1,2982,0);break b}p=p+1|0;E=H[R+24>>2];if(p>>>0<K[E+16>>2]){continue}break}}E=H[R+32>>2];qa=H[H[R+20>>2]>>2];if(H[qa+16>>2]){p=H[qa+20>>2];E=H[E+5584>>2];t=H[H[R+24>>2]+24>>2];r=0;while(1){V:{b=H[R+68>>2];if(H[b+(r<<2)>>2]?0:b){break V}m=H[t+36>>2]+1|0;if(H[E+20>>2]==1){fa=m;j=0;ta=oa-32|0;oa=ta;W:{X:{if(H[R+64>>2]){b=1;if((m|0)==1){break W}i=H[p+28>>2];h=i+N(H[p+24>>2],152)|0;s=H[h-144>>2];l=H[h-152>>2];if((s|0)==(l|0)){break W}g=m-1|0;n=g&1;z=H[R+44>>2];Q=H[z+4>>2];Y:{if((m|0)==2){h=i;break Y}o=g&-2;h=i;b=0;while(1){k=H[h+160>>2]-H[h+152>>2]|0;k=j>>>0>k>>>0?j:k;j=H[h+164>>2]-H[h+156>>2]|0;k=j>>>0<k>>>0?k:j;j=H[h+312>>2]-H[h+304>>2]|0;k=j>>>0<k>>>0?k:j;j=H[h+316>>2]-H[h+308>>2]|0;j=j>>>0<k>>>0?k:j;h=h+304|0;b=b+2|0;if((o|0)!=(b|0)){continue}break}}b=0;if(n){k=H[h+160>>2]-H[h+152>>2]|0;j=j>>>0>k>>>0?j:k;h=H[h+164>>2]-H[h+156>>2]|0;j=h>>>0<j>>>0?j:h}if(j>>>0>134217727){break W}n=H[i+4>>2];o=H[i+12>>2];k=H[i>>2];h=H[i+8>>2];q=j<<5;A=rb(q);H[ta+16>>2]=A;if(!A){break W}C=s-l|0;b=o-n|0;k=h-k|0;H[ta>>2]=A;while(1){w=H[p+36>>2];o=b;H[ta+8>>2]=b;b=k;H[ta+24>>2]=b;l=H[i+156>>2];n=H[i+164>>2];j=H[i+160>>2];h=H[i+152>>2];H[ta+28>>2]=(h|0)%2;k=j-h|0;H[ta+20>>2]=k-b;m=(Q|0)<2;b=n-l|0;Z:{if(!(!m&b>>>0>1)){h=0;if(!b){break Z}while(1){lc(ta+16|0,w+(N(h,C)<<2)|0);h=h+1|0;if((h|0)!=(b|0)){continue}break}break Z}s=b>>>0<Q>>>0?b:Q;n=s-1|0;l=(b>>>0)/(s>>>0)|0;j=0;while(1){Z=Ga(36);if(!Z){break X}h=H[ta+20>>2];H[Z>>2]=H[ta+16>>2];H[Z+4>>2]=h;h=H[ta+28>>2];H[Z+8>>2]=H[ta+24>>2];H[Z+12>>2]=h;H[Z+28>>2]=N(j,l);H[Z+24>>2]=w;H[Z+20>>2]=C;H[Z+16>>2]=k;h=(j|0)==(n|0);j=j+1|0;H[Z+32>>2]=h?b:N(l,j);h=rb(q);H[Z>>2]=h;if(!h){b=0;Ua(z);Da(Z);Da(A);break W}ib(z,10,Z);if((j|0)!=(s|0)){continue}break}Ua(z)}H[ta+4>>2]=b-o;H[ta+12>>2]=H[i+156>>2]%2;_:{if(!(!m&k>>>0>1)){j=8;h=0;if(k>>>0>=8){while(1){qb(ta,w+(h<<2)|0,C,8);h=j;j=h+8|0;if(k>>>0>=j>>>0){continue}break}}if(h>>>0>=k>>>0){break _}qb(ta,w+(h<<2)|0,C,k-h|0);break _}m=k>>>0<Q>>>0?k:Q;o=m-1|0;n=(k>>>0)/(m>>>0)|0;j=0;while(1){l=Ga(36);if(!l){break X}h=H[ta+4>>2];H[l>>2]=H[ta>>2];H[l+4>>2]=h;h=H[ta+12>>2];H[l+8>>2]=H[ta+8>>2];H[l+12>>2]=h;H[l+28>>2]=N(j,n);H[l+24>>2]=w;H[l+20>>2]=C;H[l+16>>2]=b;h=(j|0)==(o|0);j=j+1|0;H[l+32>>2]=h?k:N(n,j);h=rb(q);H[l>>2]=h;if(!h){b=0;Ua(z);Da(l);Da(A);break W}ib(z,11,l);if((j|0)!=(m|0)){continue}break}Ua(z)}i=i+152|0;g=g-1|0;if(g){continue}break}b=1;Da(A);break W}b=1;o=H[p+28>>2];Ta=o+N(fa,152)|0;jb=Ta-152|0;if(H[jb>>2]==H[Ta-144>>2]){break W}aa=Ta-148|0;if(H[aa>>2]==H[Ta-140>>2]){break W}s=H[o+4>>2];l=H[o+12>>2];m=H[o>>2];n=H[o+8>>2];M=H[p+68>>2];W=H[p+64>>2];ia=H[p+60>>2];ca=H[p+56>>2];Na=kc(p,fa);if(!Na){b=0;break W}$:{aa:{if((fa|0)!=1){b=fa-1|0;k=b&1;ba:{if((fa|0)==2){h=o;break ba}g=b&-2;h=o;b=0;while(1){i=H[h+160>>2]-H[h+152>>2]|0;i=i>>>0<j>>>0?j:i;j=H[h+164>>2]-H[h+156>>2]|0;i=i>>>0>j>>>0?i:j;j=H[h+312>>2]-H[h+304>>2]|0;i=i>>>0>j>>>0?i:j;j=H[h+316>>2]-H[h+308>>2]|0;j=i>>>0>j>>>0?i:j;h=h+304|0;b=b+2|0;if((g|0)!=(b|0)){continue}break}}if(k){b=H[h+160>>2]-H[h+152>>2]|0;j=b>>>0<j>>>0?j:b;b=H[h+164>>2]-H[h+156>>2]|0;j=b>>>0<j>>>0?j:b}if(j>>>0>=268435456){break $}J=rb(j<<4);if(!J){break $}ca:{if(!fa){break ca}u=l-s|0;ba=n-m|0;_a=J-4|0;kb=J+28|0;Q=J+24|0;Va=J+16|0;bb=J-16|0;lb=J-32|0;nb=J-8|0;wa=J+4|0;$a=1;da:while(1){h=H[o+156>>2];cb=(h|0)%2|0;b=H[o+152>>2];ga=(b|0)%2|0;X=H[o+164>>2]-h|0;Ba=X-u|0;Z=H[o+160>>2]-b|0;Aa=Z-ba|0;_=ca;b=ca;i=ia;ma=i;j=W;g=j;h=M;v=h;k=H[p+20>>2];ea:{if((k|0)==($a|0)){break ea}q=k-$a|0;ma=0;b=0;if(ca){h=q&31;if((q&63)>>>0>=32){l=-1<<h;b=0}else{b=-1<<h;l=b|(1<<h)-1&-1>>>32-h}b=ca+(b^-1)|0;h=l^-1;h=b>>>0<ca>>>0?h+1|0:h;j=q&31;if((q&63)>>>0>=32){b=h>>>j|0}else{b=((1<<j)-1&h)<<32-j|b>>>j}}if(ia){j=q&31;if((q&63)>>>0>=32){l=-1<<j;h=0}else{h=-1<<j;l=h|(1<<j)-1&-1>>>32-j}h=ia+(h^-1)|0;j=l^-1;j=h>>>0<ia>>>0?j+1|0:j;i=q&31;if((q&63)>>>0>=32){ma=j>>>i|0}else{ma=((1<<i)-1&j)<<32-i|h>>>i}}h=0;j=0;if(W){i=q&31;if((q&63)>>>0>=32){l=-1<<i;j=0}else{j=-1<<i;l=j|(1<<i)-1&-1>>>32-i}j=W+(j^-1)|0;i=l^-1;i=j>>>0<W>>>0?i+1|0:i;g=q&31;if((q&63)>>>0>=32){j=i>>>g|0}else{j=((1<<g)-1&i)<<32-g|j>>>g}}if(M){i=q&31;if((q&63)>>>0>=32){l=-1<<i;h=0}else{h=-1<<i;l=h|(1<<i)-1&-1>>>32-i}h=M+(h^-1)|0;i=l^-1;i=h>>>0<M>>>0?i+1|0:i;g=q&31;if((q&63)>>>0>=32){h=i>>>g|0}else{h=((1<<g)-1&i)<<32-g|h>>>g}}g=0;_=0;s=1<<q-1;if(s>>>0<ca>>>0){k=q&31;if((q&63)>>>0>=32){l=-1<<k;i=0}else{i=-1<<k;l=i|(1<<k)-1&-1>>>32-k}n=i^-1;i=n+(ca-s|0)|0;k=l^-1;k=i>>>0<n>>>0?k+1|0:k;n=q&31;if((q&63)>>>0>=32){_=k>>>n|0}else{_=((1<<n)-1&k)<<32-n|i>>>n}}if(s>>>0<W>>>0){g=q&31;if((q&63)>>>0>=32){l=-1<<g;i=0}else{i=-1<<g;l=i|(1<<g)-1&-1>>>32-g}k=i^-1;i=k+(W-s|0)|0;g=l^-1;g=i>>>0<k>>>0?g+1|0:g;k=q&31;if((q&63)>>>0>=32){g=g>>>k|0}else{g=((1<<k)-1&g)<<32-k|i>>>k}}v=0;i=0;if(s>>>0<ia>>>0){k=q&31;if((q&63)>>>0>=32){l=-1<<k;i=0}else{i=-1<<k;l=i|(1<<k)-1&-1>>>32-k}n=i^-1;i=n+(ia-s|0)|0;k=l^-1;k=i>>>0<n>>>0?k+1|0:k;n=q&31;if((q&63)>>>0>=32){i=k>>>n|0}else{i=((1<<n)-1&k)<<32-n|i>>>n}}if(s>>>0>=M>>>0){break ea}n=q&31;if((q&63)>>>0>=32){l=-1<<n;k=0}else{k=-1<<n;l=k|(1<<n)-1&-1>>>32-n}m=k^-1;k=m+(M-s|0)|0;n=l^-1;n=k>>>0<m>>>0?n+1|0:n;m=q&31;if((q&63)>>>0>=32){v=n>>>m|0}else{v=((1<<m)-1&n)<<32-m|k>>>m}}n=H[o+180>>2];k=g-n|0;k=g>>>0>=k>>>0?k:0;g=k+2|0;g=g>>>0<k>>>0?-1:g;Oa=g>>>0<Aa>>>0?g:Aa;k=H[o+216>>2];g=j-k|0;g=g>>>0<=j>>>0?g:0;j=g+2|0;j=g>>>0>j>>>0?-1:j;Pa=j>>>0<ba>>>0?j:ba;g=(ga?Oa:Pa)<<1;j=(ga?Pa:Oa)<<1|1;ua=g>>>0>j>>>0?g:j;D=ua>>>0<Z>>>0;j=_-n|0;g=j>>>0<=_>>>0?j:0;j=g-2|0;m=g>>>0>=j>>>0?j:0;j=b-k|0;j=b>>>0>=j>>>0?j:0;b=j-2|0;l=b>>>0<=j>>>0?b:0;z=(ga?m:l)<<1;A=(ga?l:m)<<1|1;q=z>>>0<A>>>0;s=H[o+184>>2];b=ma-s|0;j=b>>>0<=ma>>>0?b:0;b=j-2|0;n=b>>>0<=j>>>0?b:0;C=n;g=H[o+220>>2];b=i-g|0;j=b>>>0<=i>>>0?b:0;b=j-2|0;k=b>>>0<=j>>>0?b:0;w=k;b=h-s|0;h=b>>>0<=h>>>0?b:0;b=h+2|0;b=b>>>0<h>>>0?-1:b;Qa=b>>>0<u>>>0?b:u;s=Qa;b=v-g|0;h=b>>>0<=v>>>0?b:0;b=h+2|0;b=b>>>0<h>>>0?-1:b;na=b>>>0<Ba>>>0?b:Ba;ma=na;if(cb){w=n;C=k;ma=s;s=na}db=D?ua:Z;D=q?z:A;ob=u+na|0;pb=k+u|0;if(X){i=l<<3;j=Aa<<3;la=j+_a|0;h=(l|0)<(Aa|0);x=h?i+wa|0:la;z=(ba|0)>(Oa|0)?Oa:ba-1|0;v=0;ea=(ba|0)>1|(Aa|0)>0;b=ga<<2;S=(wa-b|0)+(m<<3)|0;xa=i+J|0;Y=xa+b|0;A=(Aa|0)>(Pa|0)?Pa:Aa;q=l+1|0;da=ba+Oa|0;ka=m+ba|0;pa=J+(D<<2)|0;b=ba<<3;za=b+nb|0;La=b+_a|0;Ma=j+nb|0;sa=!ba&(Aa|0)==1;b=db<<2;va=b+J|0;ja=b+_a|0;ya=((h?l:Aa)<<3)+_a|0;while(1){fa:{if(!(v>>>0<Qa>>>0&n>>>0<=v>>>0|v>>>0<ob>>>0&v>>>0>=pb>>>0)){V=v+1|0;break fa}if(Z>>>0>ua>>>0){H[ja>>2]=0;H[va>>2]=0}V=v+1|0;Ra(Na,l,v,Pa,V,Y,2,0);Ra(Na,ka,v,da,V,S,2,0);ga:{ha:{ia:{if(!ga){if(!ea){break ga}if((l|0)>=(Pa|0)){break ha}ja:{ka:{if((l|0)>0){j=H[ya>>2];break ka}j=H[wa>>2];b=j;if((l|0)<0){break ja}}b=j;j=H[x>>2]}H[xa>>2]=H[xa>>2]-((b+j|0)+2>>2);h=l;j=q;b=j;if((A|0)<=(b|0)){break ia}while(1){j=J+(b<<3)|0;H[j>>2]=H[j>>2]-((H[wa+(h<<3)>>2]+H[j+4>>2]|0)+2>>2);h=b;b=b+1|0;if((A|0)!=(b|0)){continue}break}j=A;break ia}la:{if(!sa){j=l;if((Pa|0)<=(j|0)){break la}while(1){i=J+(j<<3)|0;h=H[i+4>>2];ma:{na:{if((j|0)>=0){b=j+1|0;g=H[((j|0)<(Aa|0)?i:Ma)>>2];break na}g=H[J>>2];b=0;j=j+1|0;_=J;if(j){break ma}}if((b|0)>=(Aa|0)){j=b;_=Ma;break ma}j=b;_=J+(b<<3)|0}b=_;H[i+4>>2]=h-((H[b>>2]+g|0)+2>>2);if((j|0)<(Pa|0)){continue}break}break la}H[J>>2]=H[J>>2]/2;break ga}j=m;if((Oa|0)<=(j|0)){break ga}while(1){b=j<<3;g=b+J|0;h=H[g>>2];oa:{if((j|0)<0){i=H[wa>>2];_=wa;break oa}i=H[((j|0)<(ba|0)?g+4|0:La)>>2];_=wa;if(!j){break oa}_=(j|0)>(ba|0)?La:b+_a|0}b=_;H[g>>2]=h+(H[b>>2]+i>>1);j=j+1|0;if((Oa|0)!=(j|0)){continue}break}break ga}if((j|0)>=(Pa|0)){break ha}while(1){b=J+(j<<3)|0;g=b;i=H[b>>2];pa:{qa:{if((j|0)>0){h=H[(((j|0)<(Aa|0)?j:Aa)<<3)+_a>>2];break qa}h=H[wa>>2];b=wa;if((j|0)<0){break pa}}b=la;if((j|0)>=(Aa|0)){break pa}b=wa+(j<<3)|0}H[g>>2]=i-((H[b>>2]+h|0)+2>>2);j=j+1|0;if((Pa|0)!=(j|0)){continue}break}}if((m|0)>=(Oa|0)){break ga}b=m;j=b;if((b|0)<(z|0)){while(1){b=J+(j<<3)|0;j=j+1|0;H[b+4>>2]=H[b+4>>2]+(H[J+(j<<3)>>2]+H[b>>2]>>1);if((j|0)!=(z|0)){continue}break}b=z}if((b|0)>=(Oa|0)){break ga}while(1){j=b;ra:{sa:{if((b|0)>=0){_=H[((b|0)<(ba|0)?J+(b<<3)|0:za)>>2];h=b+1|0;break sa}_=H[J>>2];h=0;b=j+1|0;i=J;if(b){break ra}}if((h|0)>=(ba|0)){b=h;i=za;break ra}b=h;i=J+(b<<3)|0}h=wa+(j<<3)|0;H[h>>2]=H[h>>2]+(H[i>>2]+_>>1);if((b|0)<(Oa|0)){continue}break}}if(!ab(Na,D,v,db,V,pa,1,0)){break aa}}v=V;if((X|0)!=(v|0)){continue}break}}o=o+152|0;h=s<<1;b=ma<<1|1;b=b>>>0<h>>>0?h:b;ka=b>>>0<X>>>0?b:X;ua=J+(n<<5)|0;j=Ba<<5;La=j+bb|0;b=(n|0)<(Ba|0);pa=b?ua+28|0:La+12|0;sa=b?ua+24|0:La+8|0;va=b?ua+20|0:La+4|0;ja=(n|0)<0?Va:b?ua+16|0:La;q=(u|0)>(na|0)?na:u-1|0;ea=((b?n:Ba)<<5)+bb|0;ya=(n|0)<=0;v=ya?Va:ea;V=(Ba|0)>0;ba=V|(u|0)>1;_=ua+(cb<<4)|0;ma=(J+(4-(cb<<2)<<2)|0)+(k<<5)|0;s=(Ba|0)>(Qa|0)?Qa:Ba;m=n+1|0;h=C<<1;b=w<<1|1;z=b>>>0>h>>>0?h:b;A=J+(z<<4)|0;b=u<<5;xa=b+lb|0;za=j+lb|0;C=!u&(Ba|0)==1;Ma=b+bb|0;S=Ma+12|0;Y=Ma+8|0;da=Ma+4|0;w=ea+12|0;i=ea+8|0;while(1){ta:{ua:{va:{wa:{l=D;if(db>>>0>l>>>0){b=db-l|0;D=(b>>>0>=4?4:b)+l|0;Ra(Na,l,n,D,Qa,_,1,8);Ra(Na,l,pb,D,ob,ma,1,8);if(!cb){if(!ba){break ta}if((n|0)>=(Qa|0)){break ua}H[ua>>2]=H[ua>>2]-((H[v>>2]+H[ja>>2]|0)+2>>2);xa:{if(!ya){j=H[ea+4>>2];h=i;b=w;break xa}j=H[J+20>>2];if((n|0)<0){break wa}h=Q;b=kb}H[ua+4>>2]=H[ua+4>>2]-((H[va>>2]+j|0)+2>>2);H[ua+8>>2]=H[ua+8>>2]-((H[h>>2]+H[sa>>2]|0)+2>>2);j=H[pa>>2];b=H[b>>2];break va}if(C){H[J>>2]=H[J>>2]/2;H[J+8>>2]=H[J+8>>2]/2;H[J+12>>2]=H[J+12>>2]/2;H[J+4>>2]=H[J+4>>2]/2;break ta}b=n;if((Qa|0)>(b|0)){while(1){g=J+(b<<5)|0;ya:{if((b|0)<0){h=H[J>>2];j=V|(b|0)!=-1;H[g+16>>2]=H[g+16>>2]-(((j?h:H[za>>2])+h|0)+2>>2);h=H[wa>>2];H[g+20>>2]=H[g+20>>2]-(((j?h:H[za+4>>2])+h|0)+2>>2);h=H[J+8>>2];H[g+24>>2]=H[g+24>>2]-(((j?h:H[za+8>>2])+h|0)+2>>2);h=H[J+12>>2];H[g+28>>2]=H[g+28>>2]-(((j?h:H[za+12>>2])+h|0)+2>>2);b=b+1|0;break ya}j=b+1|0;za:{if((j|0)<(Ba|0)){b=J+(j<<5)|0;H[g+16>>2]=H[g+16>>2]-((H[g>>2]+H[b>>2]|0)+2>>2);H[g+20>>2]=H[g+20>>2]-((H[g+4>>2]+H[b+4>>2]|0)+2>>2);H[g+24>>2]=H[g+24>>2]-((H[g+8>>2]+H[b+8>>2]|0)+2>>2);H[g+28>>2]=H[g+28>>2]-((H[g+12>>2]+H[b+12>>2]|0)+2>>2);break za}h=H[g+16>>2];if((b|0)<(Ba|0)){H[g+16>>2]=h-((H[g>>2]+H[za>>2]|0)+2>>2);H[g+20>>2]=H[g+20>>2]-((H[g+4>>2]+H[za+4>>2]|0)+2>>2);H[g+24>>2]=H[g+24>>2]-((H[g+8>>2]+H[za+8>>2]|0)+2>>2);H[g+28>>2]=H[g+28>>2]-((H[g+12>>2]+H[za+12>>2]|0)+2>>2);break za}H[g+16>>2]=h-((H[za>>2]<<1)+2>>2);H[g+20>>2]=H[g+20>>2]-((H[za+4>>2]<<1)+2>>2);H[g+24>>2]=H[g+24>>2]-((H[za+8>>2]<<1)+2>>2);H[g+28>>2]=H[g+28>>2]-((H[za+12>>2]<<1)+2>>2)}b=j}if((Qa|0)!=(b|0)){continue}break}}b=k;if((na|0)<=(b|0)){break ta}while(1){g=J+(b<<5)|0;Aa:{if((b|0)<0){H[g>>2]=H[g>>2]+(H[Va>>2]<<1>>1);H[g+4>>2]=H[g+4>>2]+(H[J+20>>2]<<1>>1);H[g+8>>2]=H[g+8>>2]+(H[J+24>>2]<<1>>1);H[g+12>>2]=H[g+12>>2]+(H[J+28>>2]<<1>>1);break Aa}j=H[g>>2];if(!b){h=(b|0)<(u|0);H[g>>2]=j+(H[Va>>2]+H[(h?g+16|0:Ma)>>2]>>1);H[g+4>>2]=H[g+4>>2]+(H[J+20>>2]+H[(h?g+20|0:da)>>2]>>1);H[g+8>>2]=H[g+8>>2]+(H[J+24>>2]+H[(h?g+24|0:Y)>>2]>>1);H[g+12>>2]=H[g+12>>2]+(H[J+28>>2]+H[(h?g+28|0:S)>>2]>>1);break Aa}if((b|0)<=(u|0)){h=(b|0)<(u|0);H[g>>2]=j+(H[g-16>>2]+H[(h?g+16|0:Ma)>>2]>>1);H[g+4>>2]=H[g+4>>2]+(H[g-12>>2]+H[(h?g+20|0:da)>>2]>>1);H[g+8>>2]=H[g+8>>2]+(H[g-8>>2]+H[(h?g+24|0:Y)>>2]>>1);H[g+12>>2]=H[g+12>>2]+(H[g-4>>2]+H[(h?g+28|0:S)>>2]>>1);break Aa}H[g>>2]=j+(H[Ma>>2]<<1>>1);H[g+4>>2]=H[g+4>>2]+(H[Ma+4>>2]<<1>>1);H[g+8>>2]=H[g+8>>2]+(H[Ma+8>>2]<<1>>1);H[g+12>>2]=H[g+12>>2]+(H[Ma+12>>2]<<1>>1)}b=b+1|0;if((na|0)!=(b|0)){continue}break}break ta}ba=Z;u=X;$a=$a+1|0;if((fa|0)!=($a|0)){continue da}break ca}H[ua+4>>2]=H[ua+4>>2]-((j<<1)+2>>2);H[ua+8>>2]=H[ua+8>>2]-((H[Q>>2]<<1)+2>>2);j=H[kb>>2];b=j}H[ua+12>>2]=H[ua+12>>2]-((b+j|0)+2>>2);g=n;j=m;b=j;if((s|0)>(b|0)){while(1){h=J+(j<<5)|0;b=Va+(g<<5)|0;H[h>>2]=H[h>>2]-((H[b>>2]+H[h+16>>2]|0)+2>>2);H[h+4>>2]=H[h+4>>2]-((H[b+4>>2]+H[h+20>>2]|0)+2>>2);H[h+8>>2]=H[h+8>>2]-((H[b+8>>2]+H[h+24>>2]|0)+2>>2);H[h+12>>2]=H[h+12>>2]-((H[b+12>>2]+H[h+28>>2]|0)+2>>2);g=j;j=g+1|0;if((s|0)!=(j|0)){continue}break}b=s}if((b|0)>=(Qa|0)){break ua}while(1){h=b<<5;x=h+Va|0;ga=h+J|0;g=(b|0)<(Ba|0);Ba:{if((b|0)<=0){h=H[Va>>2];if((b|0)>=0){j=g?x:La;H[ga>>2]=H[ga>>2]-((h+H[j>>2]|0)+2>>2);H[ga+4>>2]=H[ga+4>>2]-((H[J+20>>2]+H[j+4>>2]|0)+2>>2);H[ga+8>>2]=H[ga+8>>2]-((H[J+24>>2]+H[j+8>>2]|0)+2>>2);h=(H[J+28>>2]+H[j+12>>2]|0)+2|0;break Ba}H[ga>>2]=H[ga>>2]-((h<<1)+2>>2);H[ga+4>>2]=H[ga+4>>2]-((H[J+20>>2]<<1)+2>>2);H[ga+8>>2]=H[ga+8>>2]-((H[J+24>>2]<<1)+2>>2);h=(H[J+28>>2]<<1)+2|0;break Ba}la=((g?b:Ba)<<5)+bb|0;j=H[la>>2]+2|0;h=H[ga>>2];if(!g){H[ga>>2]=h-(j+H[La>>2]>>2);H[ga+4>>2]=H[ga+4>>2]-((H[la+4>>2]+H[La+4>>2]|0)+2>>2);H[ga+8>>2]=H[ga+8>>2]-((H[la+8>>2]+H[La+8>>2]|0)+2>>2);h=(H[la+12>>2]+H[La+12>>2]|0)+2|0;break Ba}H[ga>>2]=h-(j+H[x>>2]>>2);H[ga+4>>2]=H[ga+4>>2]-((H[la+4>>2]+H[x+4>>2]|0)+2>>2);H[ga+8>>2]=H[ga+8>>2]-((H[la+8>>2]+H[x+8>>2]|0)+2>>2);h=(H[la+12>>2]+H[x+12>>2]|0)+2|0}H[ga+12>>2]=H[ga+12>>2]-(h>>2);b=b+1|0;if((Qa|0)!=(b|0)){continue}break}}if((k|0)>=(na|0)){break ta}h=k;b=h;if((q|0)>(b|0)){while(1){b=J+(h<<5)|0;H[b+16>>2]=H[b+16>>2]+(H[b+32>>2]+H[b>>2]>>1);H[b+20>>2]=H[b+20>>2]+(H[b+36>>2]+H[b+4>>2]>>1);H[b+24>>2]=H[b+24>>2]+(H[b+40>>2]+H[b+8>>2]>>1);H[b+28>>2]=H[b+28>>2]+(H[b+44>>2]+H[b+12>>2]>>1);h=h+1|0;if((q|0)!=(h|0)){continue}break}b=q}if((b|0)>=(na|0)){break ta}while(1){h=b<<5;x=h+Va|0;Ca:{Da:{Ea:{if((b|0)<0){h=H[J>>2];if((b|0)!=-1){break Ea}if((u|0)<=0){H[x>>2]=H[x>>2]+(h+H[xa>>2]>>1);H[x+4>>2]=H[x+4>>2]+(H[xa+4>>2]+H[J+4>>2]>>1);H[x+8>>2]=H[x+8>>2]+(H[xa+8>>2]+H[J+8>>2]>>1);g=H[J+12>>2];h=H[xa+12>>2];break Da}break Ea}j=h+J|0;h=b+1|0;Fa:{if((h|0)<(u|0)){b=J+(h<<5)|0;H[x>>2]=H[x>>2]+(H[b>>2]+H[j>>2]>>1);H[x+4>>2]=H[x+4>>2]+(H[b+4>>2]+H[j+4>>2]>>1);H[x+8>>2]=H[x+8>>2]+(H[b+8>>2]+H[j+8>>2]>>1);H[x+12>>2]=H[x+12>>2]+(H[b+12>>2]+H[j+12>>2]>>1);break Fa}if((b|0)>=(u|0)){H[x>>2]=H[x>>2]+H[xa>>2];H[x+4>>2]=H[x+4>>2]+H[xa+4>>2];H[x+8>>2]=H[x+8>>2]+H[xa+8>>2];H[x+12>>2]=H[x+12>>2]+H[xa+12>>2];break Fa}H[x>>2]=H[x>>2]+(H[xa>>2]+H[j>>2]>>1);H[x+4>>2]=H[x+4>>2]+(H[xa+4>>2]+H[j+4>>2]>>1);H[x+8>>2]=H[x+8>>2]+(H[xa+8>>2]+H[j+8>>2]>>1);H[x+12>>2]=H[x+12>>2]+(H[xa+12>>2]+H[j+12>>2]>>1)}b=h;break Ca}H[x>>2]=h+H[x>>2];H[x+4>>2]=H[x+4>>2]+H[J+4>>2];H[x+8>>2]=H[x+8>>2]+H[J+8>>2];g=H[J+12>>2];h=g}H[x+12>>2]=H[x+12>>2]+(g+h>>1);b=b+1|0}if((na|0)!=(b|0)){continue}break}}if(ab(Na,l,z,D,ka,A,1,4)){continue}break}break}break aa}Da(J);b=1}g=H[Ta-16>>2];i=H[jb>>2];j=H[aa>>2];h=H[Ta-8>>2];Ra(Na,g-i|0,H[Ta-12>>2]-j|0,h-i|0,H[Ta-4>>2]-j|0,H[p+52>>2],1,h-g|0);Xa(Na);break W}Xa(Na);Da(J);b=0;break W}Xa(Na);b=0;break W}b=0;Ua(z);Da(A)}oa=ta+32|0;if(b){break V}break b}k=0;o=0;aa=oa+-64|0;oa=aa;Ga:{Ha:{if(H[R+64>>2]){n=H[p+28>>2];s=n+N(H[p+24>>2],152)|0;l=H[s-152>>2];g=1;S=H[R+44>>2];sa=H[S+4>>2];if((m|0)==1){break Ga}X=m-1|0;o=X&1;Ia:{if((m|0)==2){i=0;b=n;break Ia}g=X&-2;i=0;b=n;while(1){h=H[b+160>>2]-H[b+152>>2]|0;j=h>>>0<i>>>0?i:h;h=H[b+164>>2]-H[b+156>>2]|0;j=h>>>0<j>>>0?j:h;h=H[b+312>>2]-H[b+304>>2]|0;j=h>>>0<j>>>0?j:h;h=H[b+316>>2]-H[b+308>>2]|0;i=h>>>0<j>>>0?j:h;b=b+304|0;k=k+2|0;if((g|0)!=(k|0)){continue}break}}g=0;if(o){h=H[b+160>>2]-H[b+152>>2]|0;h=h>>>0<i>>>0?i:h;b=H[b+164>>2]-H[b+156>>2]|0;i=b>>>0<h>>>0?h:b}if(i>>>0>134217727){break Ga}m=H[s-144>>2];k=H[n+4>>2];j=H[n+12>>2];h=H[n>>2];b=H[n+8>>2];V=i<<5;ea=Ja(V);H[aa+32>>2]=ea;if(!ea){break Ga}o=j-k|0;j=b-h|0;b=sa>>>1|0;ba=b>>>0<=2?2:b;x=m-l|0;ma=x<<5;na=N(x,28);D=N(x,24);fa=N(x,20);M=x<<4;W=N(x,12);ia=x<<3;H[aa>>2]=ea;_=ea+32|0;h=H[p+36>>2];while(1){l=o;H[aa+8>>2]=l;b=j;H[aa+40>>2]=b;ka=H[n+156>>2];pa=H[n+164>>2];i=H[n+160>>2];g=H[n+152>>2];H[aa+56>>2]=0;H[aa+52>>2]=b;H[aa+48>>2]=0;s=(g|0)%2|0;H[aa+44>>2]=s;j=i-g|0;Q=j-b|0;H[aa+60>>2]=Q;H[aa+36>>2]=Q;w=(sa|0)<2;o=pa-ka|0;Ja:{if(!(!w&o>>>0>15)){m=0;i=h;if(o>>>0<8){break Ja}v=0;while(1){b=aa+32|0;Eb(b,i,x,8);Wa(b);b=0;if(j){while(1){k=(b<<2)+i|0;g=ea+(b<<5)|0;L[k>>2]=L[g>>2];L[k+(x<<2)>>2]=L[g+4>>2];L[k+ia>>2]=L[g+8>>2];L[k+W>>2]=L[g+12>>2];b=b+1|0;if((j|0)!=(b|0)){continue}break}b=0;while(1){k=(b<<2)+i|0;g=ea+(b<<5)|0;L[k+M>>2]=L[g+16>>2];L[k+fa>>2]=L[g+20>>2];L[k+D>>2]=L[g+24>>2];L[k+na>>2]=L[g+28>>2];b=b+1|0;if((j|0)!=(b|0)){continue}break}}i=i+ma|0;b=v+15|0;m=v+8|0;v=m;if(b>>>0<o>>>0){continue}break}break Ja}i=o>>>3|0;C=i>>>0<sa>>>0?i:sa;q=(o>>>0)/(C>>>0)&-8;m=o&-8;k=0;i=h;while(1){A=Ga(48);if(!A){break Ha}g=Ja(V);H[A>>2]=g;if(!g){g=0;Ua(S);Da(A);Da(ea);break Ga}H[A+40>>2]=i;H[A+36>>2]=x;H[A+32>>2]=j;H[A+28>>2]=Q;H[A+24>>2]=0;H[A+20>>2]=b;H[A+16>>2]=0;H[A+12>>2]=s;H[A+8>>2]=b;H[A+4>>2]=Q;g=m-N(k,q)|0;k=k+1|0;g=(C|0)==(k|0)?g:q;H[A+44>>2]=g;ib(S,12,A);i=(N(g,x)<<2)+i|0;if((k|0)!=(C|0)){continue}break}Ua(S)}Ka:{if(m>>>0>=o>>>0){break Ka}b=aa+32|0;g=o-m|0;Eb(b,i,x,g);Wa(b);if(!j){break Ka}q=g&-4;C=g&3;s=0;k=ka+(m-pa|0)>>>0>4294967292;while(1){Q=(s<<2)+i|0;m=ea+(s<<5)|0;b=0;v=0;if(!k){while(1){L[Q+(N(b,x)<<2)>>2]=L[m+(b<<2)>>2];g=b|1;L[Q+(N(g,x)<<2)>>2]=L[m+(g<<2)>>2];g=b|2;L[Q+(N(g,x)<<2)>>2]=L[m+(g<<2)>>2];g=b|3;L[Q+(N(g,x)<<2)>>2]=L[m+(g<<2)>>2];b=b+4|0;v=v+4|0;if((q|0)!=(v|0)){continue}break}}v=0;if(C){while(1){L[Q+(N(b,x)<<2)>>2]=L[m+(b<<2)>>2];b=b+1|0;v=v+1|0;if((C|0)!=(v|0)){continue}break}}s=s+1|0;if((s|0)!=(j|0)){continue}break}}Y=o-l|0;H[aa+4>>2]=Y;b=H[n+156>>2];H[aa+28>>2]=Y;H[aa+24>>2]=0;H[aa+20>>2]=l;H[aa+16>>2]=0;va=(b|0)%2|0;H[aa+12>>2]=va;La:{if(!(!w&j>>>0>15)){k=h;if(j>>>0<8){break La}ca=o&-2;Z=o&1;z=Y&-2;A=Y&1;Q=l&-2;C=l&1;b=va<<5;ja=_-b|0;ya=b+ea|0;w=N(l,x)<<2;q=pa-1|0;s=(q|0)==(l+ka|0);i=j;while(1){b=0;v=0;Ma:{Na:{switch(l|0){default:while(1){u=(N(b,x)<<2)+k|0;g=H[u+4>>2];m=ya+(b<<6)|0;H[m>>2]=H[u>>2];H[m+4>>2]=g;g=H[u+28>>2];H[m+24>>2]=H[u+24>>2];H[m+28>>2]=g;g=H[u+20>>2];H[m+16>>2]=H[u+16>>2];H[m+20>>2]=g;g=H[u+12>>2];H[m+8>>2]=H[u+8>>2];H[m+12>>2]=g;g=b|1;u=ya+(g<<6)|0;m=(N(g,x)<<2)+k|0;g=H[m+28>>2];H[u+24>>2]=H[m+24>>2];H[u+28>>2]=g;g=H[m+20>>2];H[u+16>>2]=H[m+16>>2];H[u+20>>2]=g;g=H[m+12>>2];H[u+8>>2]=H[m+8>>2];H[u+12>>2]=g;g=H[m+4>>2];H[u>>2]=H[m>>2];H[u+4>>2]=g;b=b+2|0;v=v+2|0;if((Q|0)!=(v|0)){continue}break};break;case 0:break Ma;case 1:break Na}}if(!C){break Ma}m=ya+(b<<6)|0;g=(N(b,x)<<2)+k|0;b=H[g+4>>2];H[m>>2]=H[g>>2];H[m+4>>2]=b;b=H[g+28>>2];H[m+24>>2]=H[g+24>>2];H[m+28>>2]=b;b=H[g+20>>2];H[m+16>>2]=H[g+16>>2];H[m+20>>2]=b;b=H[g+12>>2];H[m+8>>2]=H[g+8>>2];H[m+12>>2]=b}Oa:{if((l|0)==(o|0)){break Oa}u=k+w|0;b=0;m=0;if(!s){while(1){da=u+(N(b,x)<<2)|0;g=H[da+4>>2];v=ja+(b<<6)|0;H[v>>2]=H[da>>2];H[v+4>>2]=g;g=H[da+28>>2];H[v+24>>2]=H[da+24>>2];H[v+28>>2]=g;g=H[da+20>>2];H[v+16>>2]=H[da+16>>2];H[v+20>>2]=g;g=H[da+12>>2];H[v+8>>2]=H[da+8>>2];H[v+12>>2]=g;g=b|1;da=ja+(g<<6)|0;v=u+(N(g,x)<<2)|0;g=H[v+28>>2];H[da+24>>2]=H[v+24>>2];H[da+28>>2]=g;g=H[v+20>>2];H[da+16>>2]=H[v+16>>2];H[da+20>>2]=g;g=H[v+12>>2];H[da+8>>2]=H[v+8>>2];H[da+12>>2]=g;g=H[v+4>>2];H[da>>2]=H[v>>2];H[da+4>>2]=g;b=b+2|0;m=m+2|0;if((z|0)!=(m|0)){continue}break}}if(!A){break Oa}m=ja+(b<<6)|0;g=u+(N(b,x)<<2)|0;b=H[g+4>>2];H[m>>2]=H[g>>2];H[m+4>>2]=b;b=H[g+28>>2];H[m+24>>2]=H[g+24>>2];H[m+28>>2]=b;b=H[g+20>>2];H[m+16>>2]=H[g+16>>2];H[m+20>>2]=b;b=H[g+12>>2];H[m+8>>2]=H[g+8>>2];H[m+12>>2]=b}Wa(aa);Pa:{if(!o){break Pa}b=0;v=0;if((q|0)!=(ka|0)){while(1){u=ea+(b<<5)|0;g=H[u+4>>2];m=(N(b,x)<<2)+k|0;H[m>>2]=H[u>>2];H[m+4>>2]=g;g=H[u+28>>2];H[m+24>>2]=H[u+24>>2];H[m+28>>2]=g;g=H[u+20>>2];H[m+16>>2]=H[u+16>>2];H[m+20>>2]=g;g=H[u+12>>2];H[m+8>>2]=H[u+8>>2];H[m+12>>2]=g;g=b|1;u=(N(g,x)<<2)+k|0;m=ea+(g<<5)|0;g=H[m+28>>2];H[u+24>>2]=H[m+24>>2];H[u+28>>2]=g;g=H[m+20>>2];H[u+16>>2]=H[m+16>>2];H[u+20>>2]=g;g=H[m+12>>2];H[u+8>>2]=H[m+8>>2];H[u+12>>2]=g;g=H[m+4>>2];H[u>>2]=H[m>>2];H[u+4>>2]=g;b=b+2|0;v=v+2|0;if((ca|0)!=(v|0)){continue}break}}if(!Z){break Pa}m=(N(b,x)<<2)+k|0;g=ea+(b<<5)|0;b=H[g+4>>2];H[m>>2]=H[g>>2];H[m+4>>2]=b;b=H[g+28>>2];H[m+24>>2]=H[g+24>>2];H[m+28>>2]=b;b=H[g+20>>2];H[m+16>>2]=H[g+16>>2];H[m+20>>2]=b;b=H[g+12>>2];H[m+8>>2]=H[g+8>>2];H[m+12>>2]=b}k=k+32|0;i=i-8|0;if(i>>>0>7){continue}break}break La}b=j>>>3|0;q=b>>>0<ba>>>0?b:ba;m=q>>>0<=1?1:q;s=(j>>>0)/(q>>>0)&-8;i=j&-8;g=0;k=h;while(1){w=Ga(48);if(!w){break Ha}b=Ja(V);H[w>>2]=b;if(!b){g=0;Ua(S);Da(w);Da(ea);break Ga}H[w+40>>2]=k;H[w+36>>2]=x;H[w+32>>2]=o;H[w+28>>2]=Y;H[w+24>>2]=0;H[w+20>>2]=l;H[w+16>>2]=0;H[w+12>>2]=va;H[w+8>>2]=l;H[w+4>>2]=Y;b=i-N(g,s)|0;g=g+1|0;b=(q|0)==(g|0)?b:s;H[w+44>>2]=b;ib(S,13,w);k=(b<<2)+k|0;if((g|0)!=(m|0)){continue}break}Ua(S)}w=j&7;Qa:{if(!w){break Qa}s=va<<5;Ra:{if(!l){break Ra}q=s+ea|0;C=w<<2;b=0;if((l|0)!=1){m=l&-2;i=0;while(1){g=!C;if(!g){B(q+(b<<6)|0,(N(b,x)<<2)+k|0,C)}if(!g){g=b|1;B(q+(g<<6)|0,(N(g,x)<<2)+k|0,C)}b=b+2|0;i=i+2|0;if((m|0)!=(i|0)){continue}break}}if(!(l&1)|!C){break Ra}B(q+(b<<6)|0,(N(b,x)<<2)+k|0,C)}Sa:{if((l|0)==(o|0)){break Sa}q=_-s|0;s=(N(l,x)<<2)+k|0;C=w<<2;b=0;if((ka|0)!=(pa+(l^-1)|0)){m=Y&-2;i=0;while(1){g=!C;if(!g){B(q+(b<<6)|0,s+(N(b,x)<<2)|0,C)}if(!g){g=b|1;B(q+(g<<6)|0,s+(N(g,x)<<2)|0,C)}b=b+2|0;i=i+2|0;if((m|0)!=(i|0)){continue}break}}if(!(Y&1)|!C){break Sa}B(q+(b<<6)|0,s+(N(b,x)<<2)|0,C)}Wa(aa);if(!o){break Qa}l=w<<2;b=0;if((pa|0)!=(ka+1|0)){m=o&-2;i=0;while(1){g=!l;if(!g){B((N(b,x)<<2)+k|0,ea+(b<<5)|0,l)}if(!g){g=b|1;B((N(g,x)<<2)+k|0,ea+(g<<5)|0,l)}b=b+2|0;i=i+2|0;if((m|0)!=(i|0)){continue}break}}if(!(o&1)|!l){break Qa}B((N(b,x)<<2)+k|0,ea+(b<<5)|0,l)}n=n+152|0;X=X-1|0;if(X){continue}break}g=1;Da(ea);break Ga}g=1;h=H[p+28>>2];x=h+N(m,152)|0;va=x-152|0;if(H[va>>2]==H[x-144>>2]){break Ga}ja=x-148|0;if(H[ja>>2]==H[x-140>>2]){break Ga}w=H[h+4>>2];q=H[h+12>>2];s=H[h>>2];l=H[h+8>>2];z=H[p+68>>2];A=H[p+64>>2];Q=H[p+60>>2];C=H[p+56>>2];la=kc(p,m);if(!la){g=0;break Ga}if((m|0)==1){i=H[x-16>>2];j=H[va>>2];h=H[ja>>2];b=H[x-8>>2];Ra(la,i-j|0,H[x-12>>2]-h|0,b-j|0,H[x-4>>2]-h|0,H[p+52>>2],1,b-i|0);Xa(la);break Ga}b=m-1|0;n=b&1;Ta:{if((m|0)==2){g=0;b=h;break Ta}k=b&-2;g=0;b=h;while(1){j=H[b+160>>2]-H[b+152>>2]|0;i=g>>>0>j>>>0?g:j;j=H[b+164>>2]-H[b+156>>2]|0;i=i>>>0>j>>>0?i:j;j=H[b+312>>2]-H[b+304>>2]|0;i=i>>>0>j>>>0?i:j;j=H[b+316>>2]-H[b+308>>2]|0;g=i>>>0>j>>>0?i:j;b=b+304|0;o=o+2|0;if((k|0)!=(o|0)){continue}break}}if(n){j=H[b+160>>2]-H[b+152>>2]|0;j=g>>>0>j>>>0?g:j;b=H[b+164>>2]-H[b+156>>2]|0;g=b>>>0<j>>>0?j:b}Ua:{if(g>>>0>=134217728){break Ua}ea=Ja(g<<5);H[aa+32>>2]=ea;if(!ea){break Ua}H[aa>>2]=ea;Va:{if(m){n=q-w|0;b=l-s|0;ya=ea+32|0;s=m;v=H[p+20>>2];S=1;da=0;while(1){H[aa+8>>2]=n;H[aa+40>>2]=b;g=H[h+164>>2];i=H[h+160>>2];k=H[h+156>>2];j=H[h+152>>2];na=(j|0)%2|0;H[aa+44>>2]=na;u=(k|0)%2|0;H[aa+12>>2]=u;w=i-j|0;ia=w-b|0;H[aa+36>>2]=ia;Z=g-k|0;D=Z-n|0;H[aa+4>>2]=D;m=C;o=m;k=Q;i=k;g=A;X=g;j=z;_=j;Wa:{if(!da&(v|0)==(S|0)){break Wa}M=v-S|0;i=0;o=0;if(m){g=M&31;if((M&63)>>>0>=32){l=-1<<g;j=0}else{j=-1<<g;l=j|(1<<g)-1&-1>>>32-g}j=m+(j^-1)|0;g=l^-1;g=j>>>0<C>>>0?g+1|0:g;k=M&31;if((M&63)>>>0>=32){o=g>>>k|0}else{o=((1<<k)-1&g)<<32-k|j>>>k}}if(Q){i=M&31;if((M&63)>>>0>=32){l=-1<<i;j=0}else{j=-1<<i;l=j|(1<<i)-1&-1>>>32-i}j=Q+(j^-1)|0;i=l^-1;i=j>>>0<Q>>>0?i+1|0:i;g=M&31;if((M&63)>>>0>=32){i=i>>>g|0}else{i=((1<<g)-1&i)<<32-g|j>>>g}}j=0;g=0;if(A){k=M&31;if((M&63)>>>0>=32){l=-1<<k;g=0}else{g=-1<<k;l=g|(1<<k)-1&-1>>>32-k}g=A+(g^-1)|0;k=l^-1;k=g>>>0<A>>>0?k+1|0:k;m=M&31;if((M&63)>>>0>=32){g=k>>>m|0}else{g=((1<<m)-1&k)<<32-m|g>>>m}}if(z){k=M&31;if((M&63)>>>0>=32){l=-1<<k;j=0}else{j=-1<<k;l=j|(1<<k)-1&-1>>>32-k}j=z+(j^-1)|0;k=l^-1;k=j>>>0<z>>>0?k+1|0:k;m=M&31;if((M&63)>>>0>=32){j=k>>>m|0}else{j=((1<<m)-1&k)<<32-m|j>>>m}}X=0;m=0;W=1<<M-1;if(W>>>0<C>>>0){m=M&31;if((M&63)>>>0>=32){l=-1<<m;k=0}else{k=-1<<m;l=k|(1<<m)-1&-1>>>32-m}q=k^-1;k=q+(C-W|0)|0;m=l^-1;m=k>>>0<q>>>0?m+1|0:m;l=M&31;if((M&63)>>>0>=32){m=m>>>l|0}else{m=((1<<l)-1&m)<<32-l|k>>>l}}if(A>>>0>W>>>0){l=M&31;if((M&63)>>>0>=32){l=-1<<l;k=0}else{k=-1<<l;l=k|(1<<l)-1&-1>>>32-l}q=k^-1;k=q+(A-W|0)|0;l=l^-1;l=k>>>0<q>>>0?l+1|0:l;q=M&31;if((M&63)>>>0>=32){X=l>>>q|0}else{X=((1<<q)-1&l)<<32-q|k>>>q}}_=0;k=0;if(Q>>>0>W>>>0){l=M&31;if((M&63)>>>0>=32){l=-1<<l;k=0}else{k=-1<<l;l=k|(1<<l)-1&-1>>>32-l}q=k^-1;k=q+(Q-W|0)|0;l=l^-1;l=k>>>0<q>>>0?l+1|0:l;q=M&31;if((M&63)>>>0>=32){k=l>>>q|0}else{k=((1<<q)-1&l)<<32-q|k>>>q}}if(z>>>0<=W>>>0){break Wa}l=M&31;if((M&63)>>>0>=32){l=-1<<l;q=0}else{q=-1<<l;l=q|(1<<l)-1&-1>>>32-l}ca=q^-1;q=ca+(z-W|0)|0;l=l^-1;ca=q>>>0<ca>>>0?l+1|0:l;l=q;q=M&31;if((M&63)>>>0>=32){_=ca>>>q|0}else{_=((1<<q)-1&ca)<<32-q|l>>>q}}ca=H[h+180>>2];l=X-ca|0;q=l>>>0<=X>>>0?l:0;l=q+4|0;l=l>>>0<q>>>0?-1:l;ba=l>>>0<ia>>>0?l:ia;q=H[h+216>>2];l=g-q|0;l=g>>>0>=l>>>0?l:0;g=l+4|0;g=g>>>0<l>>>0?-1:g;sa=b>>>0>g>>>0?g:b;l=(na?ba:sa)<<1;g=(na?sa:ba)<<1|1;fa=g>>>0<l>>>0?l:g;M=fa>>>0<w>>>0;g=m-ca|0;m=g>>>0<=m>>>0?g:0;g=m-4|0;V=g>>>0<=m>>>0?g:0;g=o-q|0;o=g>>>0<=o>>>0?g:0;g=o-4|0;ka=g>>>0<=o>>>0?g:0;W=(na?V:ka)<<1;ia=(na?ka:V)<<1|1;ca=W>>>0<ia>>>0;X=H[h+184>>2];g=i-X|0;g=g>>>0<=i>>>0?g:0;i=g-4|0;l=g>>>0>=i>>>0?i:0;m=l;q=H[h+220>>2];i=k-q|0;g=i>>>0<=k>>>0?i:0;i=g-4|0;k=g>>>0>=i>>>0?i:0;o=k;i=j-X|0;i=i>>>0<=j>>>0?i:0;j=i+4|0;j=i>>>0>j>>>0?-1:j;pa=j>>>0<n>>>0?j:n;j=pa;i=_-q|0;g=i>>>0<=_>>>0?i:0;i=g+4|0;i=g>>>0>i>>>0?-1:i;g=i>>>0<D>>>0?i:D;X=g;if(u){X=j;m=k;o=l;j=g}Y=M?fa:w;_=ca?W:ia;H[aa+60>>2]=ba;H[aa+56>>2]=V;H[aa+52>>2]=sa;H[aa+48>>2]=ka;Xa:{if(Z>>>0<8){b=7;i=0;break Xa}i=na<<5;ma=(ya-i|0)+(V<<6)|0;na=(i+ea|0)+(ka<<6)|0;D=b+ba|0;fa=b+V|0;M=g+n|0;W=k+n|0;ia=ea+(_<<5)|0;i=0;while(1){b=i|7;Ya:{if(!(i>>>0<pa>>>0&b>>>0>=l>>>0|i>>>0<M>>>0&b>>>0>=W>>>0)){i=i+8|0;break Ya}b=Z-i|0;ca=b>>>0>=8?8:b;b=0;while(1){V=b+i|0;ba=V+1|0;q=b<<2;Ra(la,ka,V,sa,ba,q+na|0,16,0);Ra(la,fa,V,D,ba,q+ma|0,16,0);b=b+1|0;if((ca|0)!=(b|0)){continue}break}Wa(aa+32|0);b=i;i=i+8|0;if(!ab(la,_,b,Y,i,ia,8,1)){break Va}}b=i|7;if(Z>>>0>b>>>0){continue}break}}if(!(!(i>>>0<pa>>>0&b>>>0>=l>>>0)&(g+n>>>0<=i>>>0|k+n>>>0>b>>>0)|i>>>0>=Z>>>0)){fa=aa+32|0;D=0;ia=Z-i|0;if(ia){while(1){M=i+D|0;W=M+1|0;b=H[fa+16>>2];ca=D<<2;Ra(la,b,M,H[fa+20>>2],W,ca+((H[fa>>2]+(H[fa+12>>2]<<5)|0)+(b<<6)|0)|0,16,0);q=H[fa+24>>2];b=H[fa+8>>2];Ra(la,q+b|0,M,b+H[fa+28>>2]|0,W,(ca+((H[fa>>2]-(H[fa+12>>2]<<5)|0)+(q<<6)|0)|0)+32|0,16,0);D=D+1|0;if((ia|0)!=(D|0)){continue}break}}Wa(fa);if(!ab(la,_,i,Y,Z,ea+(_<<5)|0,8,1)){break Va}}H[aa+28>>2]=g;H[aa+24>>2]=k;H[aa+20>>2]=pa;H[aa+16>>2]=l;if(Y>>>0>_>>>0){j=j<<1;b=X<<1|1;b=b>>>0<j>>>0?j:b;ia=b>>>0<Z>>>0?b:Z;b=u<<5;ca=(ya-b|0)+(k<<6)|0;X=(b+ea|0)+(l<<6)|0;q=g+n|0;g=k+n|0;j=m<<1;b=o<<1|1;i=b>>>0>j>>>0?j:b;j=ea+(i<<5)|0;while(1){b=Y-_|0;b=(b>>>0>=8?8:b)+_|0;Ra(la,_,l,b,pa,X,1,16);Ra(la,_,g,b,q,ca,1,16);Wa(aa);if(!ab(la,_,i,b,ia,j,1,8)){break Va}_=_+8|0;if(Y>>>0>_>>>0){continue}break}}h=h+152|0;b=w;n=Z;S=S+1|0;da=S?da:da+1|0;if(da|(s|0)!=(S|0)){continue}break}}g=1;i=H[x-16>>2];j=H[va>>2];h=H[ja>>2];b=H[x-8>>2];Ra(la,i-j|0,H[x-12>>2]-h|0,b-j|0,H[x-4>>2]-h|0,H[p+52>>2],1,b-i|0);Xa(la);Da(ea);break Ga}Xa(la);Da(ea);g=0;break Ga}Xa(la);g=0;break Ga}g=0;Ua(S);Da(ea)}oa=aa- -64|0;if(g){break V}break b}E=E+1080|0;t=t+52|0;p=p+76|0;r=r+1|0;if(r>>>0<K[qa+16>>2]){continue}break}E=H[R+32>>2];qa=H[H[R+20>>2]>>2]}i=H[E+16>>2];Za:{if(H[R+68>>2]|!i){break Za}t=H[qa+20>>2];k=H[t+28>>2];_a:{$a:{g=H[R+64>>2];if(g){r=H[qa+16>>2];if(r>>>0<3){break _a}j=H[t+24>>2];if(!((j|0)==H[t+100>>2]&(j|0)==H[t+176>>2])){Ca(f,1,10089,0);break b}h=H[H[R+24>>2]+24>>2];b=H[h+36>>2];ab:{if((b|0)!=H[h+88>>2]|(b|0)!=H[h+140>>2]){break ab}h=N(j,152);b=h+k|0;b=N(H[b-140>>2]-H[b-148>>2]|0,H[b-144>>2]-H[b-152>>2]|0);j=h+H[t+104>>2]|0;if((b|0)!=(N(H[j-140>>2]-H[j-148>>2]|0,H[j-144>>2]-H[j-152>>2]|0)|0)){break ab}h=h+H[t+180>>2]|0;if((N(H[h-140>>2]-H[h-148>>2]|0,H[h-144>>2]-H[h-152>>2]|0)|0)==(b|0)){break $a}}Ca(f,1,10089,0);break b}r=H[qa+16>>2];if(r>>>0<3){break _a}b=H[H[R+24>>2]+24>>2];h=H[b+36>>2];bb:{if((h|0)!=H[b+88>>2]){break bb}j=H[b+140>>2];if((j|0)!=(h|0)){break bb}h=N(h,152);b=k+h|0;b=N(H[b+148>>2]-H[b+140>>2]|0,H[b+144>>2]-H[b+136>>2]|0);h=h+H[t+104>>2]|0;if((b|0)!=(N(H[h+148>>2]-H[h+140>>2]|0,H[h+144>>2]-H[h+136>>2]|0)|0)){break bb}h=H[t+180>>2]+N(j,152)|0;if((N(H[h+148>>2]-H[h+140>>2]|0,H[h+144>>2]-H[h+136>>2]|0)|0)==(b|0)){break $a}}Ca(f,1,10089,0);break b}if((i|0)==2){if(!H[E+5608>>2]){break Za}z=Ga(r<<2);if(!z){break b}A=H[qa+16>>2];cb:{if(!A){break cb}db:{eb:{if(H[R+64>>2]){o=A&3;h=0;if(A>>>0>=4){break eb}p=0;break db}o=A&3;h=0;fb:{if(A>>>0<4){p=0;break fb}k=z+12|0;r=z+8|0;i=z+4|0;j=A&-4;p=0;g=0;while(1){n=p<<2;H[n+z>>2]=H[t+52>>2];H[i+n>>2]=H[t+128>>2];H[n+r>>2]=H[t+204>>2];H[k+n>>2]=H[t+280>>2];p=p+4|0;t=t+304|0;g=g+4|0;if((j|0)!=(g|0)){continue}break}}if(!o){break cb}while(1){H[z+(p<<2)>>2]=H[t+52>>2];p=p+1|0;t=t+76|0;h=h+1|0;if((o|0)!=(h|0)){continue}break}break cb}k=z+12|0;r=z+8|0;i=z+4|0;j=A&-4;p=0;g=0;while(1){n=p<<2;H[n+z>>2]=H[t+36>>2];H[i+n>>2]=H[t+112>>2];H[n+r>>2]=H[t+188>>2];H[k+n>>2]=H[t+264>>2];p=p+4|0;t=t+304|0;g=g+4|0;if((j|0)!=(g|0)){continue}break}}if(!o){break cb}while(1){H[z+(p<<2)>>2]=H[t+36>>2];p=p+1|0;t=t+76|0;h=h+1|0;if((o|0)!=(h|0)){continue}break}}h=H[E+5608>>2];n=0;Q=Ga(A<<3);j=0;gb:{if(!Q){break gb}if(!(!b|!A)){m=Q+(A<<2)|0;o=Q+12|0;r=Q+8|0;i=Q+4|0;t=A&-4;w=A&3;l=A-1|0;while(1){p=0;k=0;if(l>>>0>=3){while(1){g=p<<2;L[g+Q>>2]=L[H[g+z>>2]>>2];j=g|4;L[j+Q>>2]=L[H[j+z>>2]>>2];j=g|8;L[j+Q>>2]=L[H[j+z>>2]>>2];j=g|12;L[j+Q>>2]=L[H[j+z>>2]>>2];p=p+4|0;k=k+4|0;if((t|0)!=(k|0)){continue}break}}s=0;if(w){while(1){j=p<<2;L[j+Q>>2]=L[H[j+z>>2]>>2];p=p+1|0;s=s+1|0;if((w|0)!=(s|0)){continue}break}}g=0;p=h;while(1){j=g<<2;C=j+m|0;H[C>>2]=0;ha=O(0);s=0;k=0;if(l>>>0>2){while(1){q=s<<2;ha=O(O(L[p>>2]*L[q+Q>>2])+ha);L[C>>2]=ha;ha=O(O(L[p+4>>2]*L[i+q>>2])+ha);L[C>>2]=ha;ha=O(O(L[p+8>>2]*L[q+r>>2])+ha);L[C>>2]=ha;ha=O(O(L[p+12>>2]*L[o+q>>2])+ha);L[C>>2]=ha;s=s+4|0;p=p+16|0;k=k+4|0;if((t|0)!=(k|0)){continue}break}}k=0;if(w){while(1){ha=O(O(L[p>>2]*L[Q+(s<<2)>>2])+ha);L[C>>2]=ha;s=s+1|0;p=p+4|0;k=k+1|0;if((w|0)!=(k|0)){continue}break}}j=j+z|0;k=H[j>>2];H[j>>2]=k+4;L[k>>2]=ha;g=g+1|0;if((A|0)!=(g|0)){continue}break}n=n+1|0;if((n|0)!=(b|0)){continue}break}}Da(Q);j=1}b=j;Da(z);if(b){break Za}break b}if(H[H[E+5584>>2]+20>>2]==1){if(g){oc(H[t+36>>2],H[t+112>>2],H[t+188>>2],b);break Za}oc(H[t+52>>2],H[t+128>>2],H[t+204>>2],b);break Za}if(g){nc(H[t+36>>2],H[t+112>>2],H[t+188>>2],b);break Za}nc(H[t+52>>2],H[t+128>>2],H[t+204>>2],b);break Za}H[Ha>>2]=r;Ca(f,1,10150,Ha)}w=H[H[R+20>>2]>>2];if(!H[w+16>>2]){$=1;break b}q=H[R+68>>2];n=H[w+20>>2];b=H[H[R+32>>2]+5584>>2];o=H[H[R+24>>2]+24>>2];j=0;while(1){hb:{if(H[q+(j<<2)>>2]?0:q){break hb}h=H[n+28>>2];i=h+N(H[o+36>>2],152)|0;ib:{if(!H[R+64>>2]){g=H[i+148>>2]-H[i+140>>2]|0;qa=H[i+144>>2]-H[i+136>>2]|0;k=0;r=52;break ib}h=h+N(H[n+24>>2],152)|0;qa=H[i+8>>2]-H[i>>2]|0;k=H[h-144>>2]-(qa+H[h-152>>2]|0)|0;g=H[i+12>>2]-H[i+4>>2]|0;r=36}h=H[o+24>>2];jb:{if(H[o+32>>2]){h=1<<h-1;p=h-1|0;i=0-h|0;break jb}p=-1<<h^-1;i=0}if(!qa|!g){break hb}$=H[n+r>>2];if(H[b+20>>2]==1){l=qa&-2;m=qa&1;t=0;h=k<<2;while(1){r=0;if((qa|0)!=1){while(1){k=H[b+1076>>2]+H[$>>2]|0;H[$>>2]=(i|0)>(k|0)?i:(k|0)<(p|0)?k:p;k=H[b+1076>>2]+H[$+4>>2]|0;H[$+4>>2]=(i|0)>(k|0)?i:(k|0)<(p|0)?k:p;$=$+8|0;r=r+2|0;if((l|0)!=(r|0)){continue}break}}if(m){r=H[b+1076>>2]+H[$>>2]|0;H[$>>2]=(i|0)>(r|0)?i:(p|0)>(r|0)?r:p;$=$+4|0}$=$+h|0;t=t+1|0;if((t|0)!=(g|0)){continue}break}break hb}t=i>>31;h=0;while(1){r=0;while(1){ha=L[$>>2];m=p;kb:{if(ha>O(2147483648)){break kb}m=i;if(ha<O(-2147483648)){break kb}m=H[b+1076>>2];l=m;m=l>>31;eb=O(T(ha));sb=O(ha-eb);if(sb<O(.5)){ha=eb}else{ha=O(U(ha));fb=ha;lb:{if(sb>O(.5)){break lb}ha=O(eb*O(.5));fb=O(ha-O(T(ha)))==O(0)?eb:fb}ha=fb}if(O(P(ha))<O(2147483648)){s=~~ha}else{s=-2147483648}m=m+(s>>31)|0;C=m+1|0;v=m;m=l+s|0;l=s>>>0>m>>>0?C:v;m=(l|0)<=(t|0)&i>>>0>m>>>0|(l|0)<(t|0)?i:m>>>0<p>>>0&(l|0)<=0|(l|0)<0?m:p}H[$>>2]=m;$=$+4|0;r=r+1|0;if((qa|0)!=(r|0)){continue}break}$=(k<<2)+$|0;h=h+1|0;if((g|0)!=(h|0)){continue}break}}n=n+76|0;b=b+1080|0;o=o+52|0;$=1;j=j+1|0;if(j>>>0<K[w+16>>2]){continue}break}break b}$=0;Ca(f,1,3372,0)}oa=Ha+16|0;if(!$){mb(Ya);H[a+8>>2]=H[a+8>>2]|32768;Ca(f,1,11451,0);break a}mb:{if(!c){break mb}b=0;m=H[a+232>>2];h=bc(m,1);if(!((h|0)==-1|d>>>0<h>>>0)){nb:{b=1;d=H[m+24>>2];if(!H[d+16>>2]){break nb}i=H[d+24>>2];k=H[H[H[m+20>>2]>>2]+20>>2];while(1){b=H[i+24>>2];g=b&7;j=b>>>3|0;b=H[k+28>>2];o=b+N(H[i+36>>2],152)|0;ob:{if(H[m+64>>2]){b=b+N(H[k+24>>2],152)|0;d=H[o+8>>2]-H[o>>2]|0;r=H[b-144>>2]-(d+H[b-152>>2]|0)|0;h=H[o+12>>2]-H[o+4>>2]|0;b=36;break ob}h=H[o+148>>2]-H[o+140>>2]|0;d=H[o+144>>2]-H[o+136>>2]|0;r=0;b=52}b=H[b+k>>2];pb:{qb:{rb:{sb:{j=j+((g|0)!=0)|0;switch(((j|0)==3?4:j)-1|0){case 0:break rb;case 1:break qb;case 3:break sb;default:break pb}}if(!h){break pb}l=d<<2;if((h|0)!=1){o=h&-2;j=0;while(1){g=!l;if(!g){B(c,b,l)}d=r<<2;n=d+(b+l|0)|0;b=c+l|0;if(!g){B(b,n,l)}c=b+l|0;b=d+(l+n|0)|0;j=j+2|0;if((o|0)!=(j|0)){continue}break}}if(!(h&1)){break pb}if(l){B(c,b,l)}c=c+l|0;break pb}j=!h|!d;if(H[i+32>>2]){if(j){break pb}o=d&-8;n=d&7;j=0;g=d-1>>>0<7;while(1){d=0;if(!g){while(1){F[c|0]=H[b>>2];F[c+1|0]=H[b+4>>2];F[c+2|0]=H[b+8>>2];F[c+3|0]=H[b+12>>2];F[c+4|0]=H[b+16>>2];F[c+5|0]=H[b+20>>2];F[c+6|0]=H[b+24>>2];F[c+7|0]=H[b+28>>2];c=c+8|0;b=b+32|0;d=d+8|0;if((o|0)!=(d|0)){continue}break}}d=0;if(n){while(1){F[c|0]=H[b>>2];c=c+1|0;b=b+4|0;d=d+1|0;if((n|0)!=(d|0)){continue}break}}b=(r<<2)+b|0;j=j+1|0;if((h|0)!=(j|0)){continue}break}break pb}if(j){break pb}o=d&-8;n=d&7;j=0;g=d-1>>>0<7;r=r<<2;while(1){d=0;if(!g){while(1){F[c|0]=H[b>>2];F[c+1|0]=H[b+4>>2];F[c+2|0]=H[b+8>>2];F[c+3|0]=H[b+12>>2];F[c+4|0]=H[b+16>>2];F[c+5|0]=H[b+20>>2];F[c+6|0]=H[b+24>>2];F[c+7|0]=H[b+28>>2];c=c+8|0;b=b+32|0;d=d+8|0;if((o|0)!=(d|0)){continue}break}}d=0;if(n){while(1){F[c|0]=H[b>>2];c=c+1|0;b=b+4|0;d=d+1|0;if((n|0)!=(d|0)){continue}break}}b=b+r|0;j=j+1|0;if((h|0)!=(j|0)){continue}break}break pb}j=!h|!d;if(H[i+32>>2]){if(j){break pb}o=d&-8;n=d&7;j=0;g=d-1>>>0<7;while(1){d=0;if(!g){while(1){G[c>>1]=H[b>>2];G[c+2>>1]=H[b+4>>2];G[c+4>>1]=H[b+8>>2];G[c+6>>1]=H[b+12>>2];G[c+8>>1]=H[b+16>>2];G[c+10>>1]=H[b+20>>2];G[c+12>>1]=H[b+24>>2];G[c+14>>1]=H[b+28>>2];c=c+16|0;b=b+32|0;d=d+8|0;if((o|0)!=(d|0)){continue}break}}d=0;if(n){while(1){G[c>>1]=H[b>>2];c=c+2|0;b=b+4|0;d=d+1|0;if((n|0)!=(d|0)){continue}break}}b=(r<<2)+b|0;j=j+1|0;if((h|0)!=(j|0)){continue}break}break pb}if(j){break pb}o=d&-8;n=d&7;j=0;g=d-1>>>0<7;while(1){d=0;if(!g){while(1){G[c>>1]=H[b>>2];G[c+2>>1]=H[b+4>>2];G[c+4>>1]=H[b+8>>2];G[c+6>>1]=H[b+12>>2];G[c+8>>1]=H[b+16>>2];G[c+10>>1]=H[b+20>>2];G[c+12>>1]=H[b+24>>2];G[c+14>>1]=H[b+28>>2];c=c+16|0;b=b+32|0;d=d+8|0;if((o|0)!=(d|0)){continue}break}}d=0;if(n){while(1){G[c>>1]=H[b>>2];c=c+2|0;b=b+4|0;d=d+1|0;if((n|0)!=(d|0)){continue}break}}b=(r<<2)+b|0;j=j+1|0;if((h|0)!=(j|0)){continue}break}}k=k+76|0;i=i+52|0;b=1;tb=tb+1|0;if(tb>>>0<K[H[m+24>>2]+16>>2]){continue}break}}}if(!b){break a}b=H[Ya+5596>>2];if(!b){break mb}Da(b);H[Ya+5596>>2]=0;H[Ya+5600>>2]=0}F[a+92|0]=I[a+92|0]&254;H[a+8>>2]=H[a+8>>2]&-129;gb=1;c=Sa(e);b=H[a+8>>2];if(!(c|ra)&(b|0)==64|(b|0)==256){break a}if((Ka(e,Za+10|0,2,f)|0)!=2){Ca(f,H[a+208>>2]?1:2,2472,0);gb=!H[a+208>>2];break a}Ea(Za+10|0,Za+12|0,2);b=H[Za+12>>2];if((b|0)==65424){break a}if((b|0)==65497){H[a+8>>2]=256;H[a+228>>2]=0;break a}if(!(Sa(e)|ra)){H[a+8>>2]=64;Ca(f,2,8419,0);break a}gb=0;Ca(f,1,8306,0)}oa=Za+16|0;return gb|0}function Za(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,v=0,x=0,z=0,A=0,C=0,D=0,E=0,J=0,M=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=O(0),Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,pa=0,qa=0,ta=0,ua=0,va=0,wa=0,xa=O(0);s=oa-80|0;oa=s;H[s+40>>2]=65424;z=N(H[a+132>>2],H[a+128>>2]);a:{b:{c:{l=H[a+8>>2];d:{if((l|0)!=8){j=0;if((l|0)!=256){break a}H[s+40>>2]=65497;break d}if(F[a+92|0]&1){break d}C=z&-2;E=z&1;R=s+77|0;S=s+76|0;T=s+72|0;n=65424;e:{f:{while(1){g:{h:{i:{j:{k:{l:{m:{n:{l=H[a+84>>2];if(!l){break n}p=l;l=H[a+80>>2];if(p>>>0<=l>>>0){break n}o=H[a+88>>2]+(l<<3)|0;n=H[o>>2];o=H[o+4>>2];H[a+80>>2]=l+1;if(!fb(j,n,o,k)){Ca(k,1,5440,0);j=0;break a}if((Ka(j,H[a+16>>2],2,k)|0)!=2){Ca(k,1,2472,0);j=0;break a}Ea(H[a+16>>2],s+40|0,2);if(H[s+40>>2]==65424){break m}Ca(k,1,4073,0);j=0;break a}if((n|0)==65427){break l}}while(1){if(!(Sa(j)|ra)){H[a+8>>2]=64;break l}if((Ka(j,H[a+16>>2],2,k)|0)!=2){Ca(k,1,2472,0);j=0;break a}Ea(H[a+16>>2],s+36|0,2);if(K[s+36>>2]<=1){Ca(k,1,6048,0);j=0;break a}o:{if(H[s+40>>2]!=32896){break o}if(Sa(j)|ra){break o}H[a+8>>2]=64;break l}r=H[a+8>>2];p:{if(!(r&16)){n=H[s+36>>2];break p}n=H[s+36>>2];l=H[a+24>>2];if(!l){break p}o=n+2|0;if(o>>>0>l>>>0){Ca(k,1,8370,0);j=0;break a}H[a+24>>2]=l-o}o=n-2|0;H[s+36>>2]=o;l=24912;t=H[s+40>>2];while(1){n=l;m=H[l>>2];if(m){l=l+12|0;if((m|0)!=(t|0)){continue}}break}if(!(r&H[n+4>>2])){Ca(k,1,5397,0);j=0;break a}q:{if(K[a+20>>2]>=o>>>0){l=H[a+16>>2];break q}l=Sa(j);r=ra;if((r|0)<0){l=1}else{l=l>>>0<o>>>0&(r|0)<=0}if(l){Ca(k,1,5797,0);j=0;break a}l=Ia(H[a+16>>2],H[s+36>>2]);if(!l){Da(H[a+16>>2]);H[a+16>>2]=0;H[a+20>>2]=0;Ca(k,1,4973,0);j=0;break a}H[a+16>>2]=l;o=H[s+36>>2];H[a+20>>2]=o}l=Ka(j,l,o,k);if((l|0)!=H[s+36>>2]){Ca(k,1,2472,0);j=0;break a}o=H[n+8>>2];if(!o){Ca(k,1,11725,0);j=0;break a}if(!(sa[o|0](a,H[a+16>>2],l,k)|0)){H[s+32>>2]=H[s+40>>2];Ca(k,1,13959,s+32|0);j=0;break a}n=H[j+56>>2];t=H[s+36>>2];A=H[a+224>>2];o=H[A+40>>2];p=H[a+228>>2];x=N(p,40);l=o+x|0;M=H[l+20>>2];r=M+1|0;q=H[l+28>>2];if(r>>>0>q>>>0){X=O(O(q>>>0)+O(100));if(X<O(4294967296)&X>=O(0)){o=~~X>>>0}else{o=0}H[l+28>>2]=o;r=Ia(H[l+24>>2],N(o,24));o=H[A+40>>2];l=x+o|0;if(!r){break k}H[l+24>>2]=r;M=H[l+20>>2];r=M+1|0}o=o+x|0;l=H[o+24>>2]+N(M,24)|0;H[l+16>>2]=t+4;n=(n-t|0)-4|0;H[l+8>>2]=n;H[l+12>>2]=n>>31;G[l>>1]=m;H[o+20>>2]=r;r:{if((m|0)!=65424){break r}l=H[o+16>>2];s:{if(!l){break s}p=H[o+4>>2];o=H[o+12>>2];if(p>>>0<=o>>>0){break s}l=l+N(o,24)|0;H[l>>2]=n;H[l+4>>2]=0}l=(H[j+56>>2]-H[s+36>>2]|0)-4|0;o=H[a+48>>2];n=H[a+52>>2];if((n|0)>0){p=1}else{p=l>>>0<=o>>>0&(n|0)>=0}if(p){break r}H[a+48>>2]=l;H[a+52>>2]=0}if(I[a+92|0]&4){if((ub(j,H[a+24>>2],k)|0)!=H[a+24>>2]|ra){Ca(k,1,2472,0);j=0;break a}H[s+40>>2]=65427;break l}if((Ka(j,H[a+16>>2],2,k)|0)!=2){Ca(k,1,2472,0);j=0;break a}Ea(H[a+16>>2],s+40|0,2);if(H[s+40>>2]!=65427){continue}break}}if(!(!(Sa(j)|ra)&H[a+8>>2]==64)){l=I[a+92|0];if(!(l&4)){l=N(H[a+228>>2],5644);o=H[a+180>>2];t:{u:{if(H[a+56>>2]){m=Sa(j);break u}m=H[a+24>>2];if(m>>>0<2){break t}}m=m-2|0;H[a+24>>2]=m}A=l+o|0;if(!m){break j}l=Sa(j);o=ra;if((o|0)<0){l=1}else{l=l>>>0<m>>>0&(o|0)<=0}if(l){if(H[a+208>>2]){Ca(k,1,5842,0);j=0;break a}Ca(k,2,5842,0)}l=H[a+24>>2];if(l>>>0>=4294967294){Ca(k,1,1480,0);j=0;break a}o=H[A+5596>>2];v:{if(o){n=H[A+5600>>2];if(n>>>0>-3-l>>>0){Ca(k,1,1211,0);j=0;break a}l=Ia(o,(l+n|0)+2|0);if(l){H[A+5596>>2]=l;break j}Da(H[A+5596>>2]);H[A+5596>>2]=0;break v}l=Ga(l+2|0);H[A+5596>>2]=l;if(l){break j}}Ca(k,1,6176,0);j=0;break a}H[a+8>>2]=8;F[a+92|0]=l&250;break i}n=H[s+40>>2];break g}Da(H[l+24>>2]);a=H[A+40>>2]+N(p,40)|0;H[a+28>>2]=0;H[a+20>>2]=0;H[a+24>>2]=0;Ca(k,1,3863,0);j=0;break a}x=H[j+56>>2];n=x-2|0;t=H[j+60>>2];r=t-(x>>>0<2)|0;p=H[a+224>>2];P=H[p+40>>2];D=H[a+228>>2];q=N(D,40);o=P+q|0;l=H[o+16>>2]+N(H[o+12>>2],24)|0;H[l+8>>2]=n;H[l+12>>2]=r;r=l;l=t;v=H[a+24>>2];x=v+x|0;H[r+16>>2]=x;H[r+20>>2]=v>>>0>x>>>0?l+1|0:l;t=H[a+24>>2];M=H[o+20>>2];r=M+1|0;l=H[o+28>>2];w:{if(r>>>0<=l>>>0){l=H[o+24>>2];break w}X=O(O(l>>>0)+O(100));if(X<O(4294967296)&X>=O(0)){l=~~X>>>0}else{l=0}H[o+28>>2]=l;l=Ia(H[o+24>>2],N(l,24));P=H[p+40>>2];o=q+P|0;if(!l){break f}H[o+24>>2]=l;M=H[o+20>>2];r=M+1|0}l=N(M,24)+l|0;H[l+16>>2]=t+2;H[l+8>>2]=n;H[l+12>>2]=n>>31;G[l>>1]=65427;H[(q+P|0)+20>>2]=r;x:{if(m){m=Ka(j,H[A+5596>>2]+H[A+5600>>2]|0,H[a+24>>2],k);l=8;if((m|0)==H[a+24>>2]){break x}l=64;if((m|0)!=-1){break x}Ca(k,1,2472,0);j=0;break a}m=0;l=H[a+24>>2]?64:8}H[a+8>>2]=l;H[A+5600>>2]=H[A+5600>>2]+m;y:{if(F[a+92|0]&1){break y}l=H[a+44>>2];if(H[a+76>>2]|((l|0)<0|(l|0)!=H[a+228>>2])){break y}if(!Fb(j)){break y}o=H[a+228>>2];n=H[a+180>>2]+N(o,5644)|0;l=H[n+5592>>2];o=H[H[a+224>>2]+40>>2]+N(o,40)|0;if((l|0)!=H[o+4>>2]){break y}p=l;l=H[n+5588>>2]+1|0;if(p>>>0<=l>>>0){break y}z:{o=H[o+16>>2]+N(l,24)|0;l=H[o>>2];o=H[o+4>>2];if((l|0)==H[j+56>>2]&(o|0)==H[j+60>>2]){break z}if(fb(j,l,o,k)){break z}Ca(k,1,5440,0);j=0;break a}if((Ka(j,H[a+16>>2],2,k)|0)!=2){Ca(k,1,2472,0);j=0;break a}Ea(H[a+16>>2],s+40|0,2);if(H[s+40>>2]==65424){break h}Ca(k,1,4073,0);j=0;break a}l=I[a+92|0];if((l&9)!=1){break i}F[a+92|0]=l|8;r=H[a+228>>2];if(H[(H[a+180>>2]+N(r,5644)|0)+5592>>2]==1){break i}if(!Fb(j)){break i}n=H[j+60>>2];t=n;o=H[j+56>>2];if((n&o)==-1){break i}A:{while(1){l=1;n=s+70|0;if((Ka(j,n,2,k)|0)!=2){break A}Ea(n,s- -64|0,2);if(H[s+64>>2]!=65424){break A}m=2472;if((Ka(j,n,2,k)|0)!=2){break c}Ea(n,s+60|0,2);if(H[s+60>>2]!=10){m=6048;break c}H[s+60>>2]=8;n=Ka(j,s+70|0,8,k);if((n|0)!=H[s+60>>2]){break c}if((n|0)!=8){m=4047;break c}Ea(s+70|0,s+56|0,2);Ea(T,s+52|0,4);Ea(S,s+48|0,1);Ea(R,s+44|0,1);if((r|0)!=H[s+56>>2]){n=H[s+52>>2];if(n>>>0<14){break A}n=n-12|0;H[s+52>>2]=n;n=ub(j,n,k);if(!ra&H[s+52>>2]==(n|0)){continue}break A}break}l=H[s+48>>2]!=H[s+44>>2]}if(!zc(j,o,t,k)){break b}if(l){break i}F[a+92|0]=I[a+92|0]&238|16;B:{if(!z){break B}o=H[a+180>>2];n=0;l=0;if((z|0)!=1){while(1){m=o+N(n,5644)|0;r=H[m+5592>>2];if(r){H[m+5592>>2]=r+1}m=o+N(n|1,5644)|0;r=H[m+5592>>2];if(r){H[m+5592>>2]=r+1}n=n+2|0;l=l+2|0;if((C|0)!=(l|0)){continue}break}}if(!E){break B}l=o+N(n,5644)|0;o=H[l+5592>>2];if(!o){break B}H[l+5592>>2]=o+1}Ca(k,2,9035,0)}if(F[a+92|0]&1){break h}if((Ka(j,H[a+16>>2],2,k)|0)!=2){if(!(!z|(z|0)!=(H[a+228>>2]+1|0))){j=H[a+180>>2];n=0;while(1){l=j+N(n,5644)|0;if(!(H[l+5588>>2]|H[l+5592>>2])){break e}n=n+1|0;if((z|0)!=(n|0)){continue}break}}Ca(k,1,2472,0);j=0;break a}Ea(H[a+16>>2],s+40|0,2)}n=H[s+40>>2];if(F[a+92|0]&1){break g}if((n|0)!=65497){continue}}break}if(H[a+8>>2]==256|(n|0)!=65497){break d}H[a+8>>2]=256;H[a+228>>2]=0;break d}Da(H[o+24>>2]);a=H[p+40>>2]+N(D,40)|0;H[a+28>>2]=0;H[a+20>>2]=0;H[a+24>>2]=0;Ca(k,1,3863,0);j=0;break a}H[s+16>>2]=n;Ca(k,4,11004,s+16|0);H[a+228>>2]=n;H[s+40>>2]=65497;H[a+8>>2]=256}n=H[a+228>>2];j=H[a+180>>2];C:{D:{if(F[a+92|0]&1){break D}E:{F:{if(n>>>0>=z>>>0){break F}m=j+N(n,5644)|0;while(1){if(H[m+5596>>2]){break F}n=n+1|0;H[a+228>>2]=n;m=m+5644|0;if((n|0)!=(z|0)){continue}break}break E}if((n|0)!=(z|0)){break D}}H[i>>2]=0;break C}G:{H:{l=j+N(n,5644)|0;if(H[l+5172>>2]){a=6837}else{if(!(I[l+5640|0]&2)){break G}r=H[l+5160>>2];I:{if(!r){m=0;break I}z=H[l+5164>>2];j=0;m=0;n=0;if(r>>>0>=4){A=r&-4;o=0;while(1){t=z+(n<<3)|0;m=H[t+28>>2]+(H[t+20>>2]+(H[t+12>>2]+(H[t+4>>2]+m|0)|0)|0)|0;n=n+4|0;o=o+4|0;if((A|0)!=(o|0)){continue}break}}o=r&3;if(!o){break I}while(1){m=H[(z+(n<<3)|0)+4>>2]+m|0;n=n+1|0;j=j+1|0;if((o|0)!=(j|0)){continue}break}}j=Ga(m);H[l+5172>>2]=j;if(j){break H}a=4009}Ca(k,1,a,0);Ca(k,1,8059,0);j=0;break a}H[l+5180>>2]=m;m=H[l+5164>>2];j=H[l+5160>>2];if(j){o=0;n=0;while(1){r=n<<3;t=r+m|0;z=H[t>>2];if(z){j=H[t+4>>2];if(j){B(H[l+5172>>2]+o|0,z,j)}j=r+H[l+5164>>2]|0;t=H[j+4>>2];Da(H[j>>2]);m=H[l+5164>>2];j=r+m|0;H[j>>2]=0;H[j+4>>2]=0;o=o+t|0;j=H[l+5160>>2]}n=n+1|0;if(n>>>0<j>>>0){continue}break}}H[l+5160>>2]=0;Da(m);H[l+5164>>2]=0;H[l+5168>>2]=H[l+5172>>2];H[l+5176>>2]=H[l+5180>>2]}l=H[a+232>>2];Y=H[l+28>>2];o=H[a+228>>2];M=H[(H[Y+76>>2]+N(o,5644)|0)+5584>>2];j=H[l+24>>2];Z=H[j+24>>2];n=H[Y+24>>2];m=(o>>>0)/(n>>>0)|0;U=H[H[l+20>>2]>>2];l=o-N(m,n)|0;n=H[Y+12>>2];l=H[Y+4>>2]+N(l,n)|0;o=H[j>>2];o=l>>>0>o>>>0?l:o;H[U>>2]=o;n=l+n|0;l=l>>>0>n>>>0?-1:n;n=H[j+8>>2];l=l>>>0<n>>>0?l:n;H[U+8>>2]=l;J:{K:{if(!((l|0)>(o|0)&(o|0)>=0)){Ca(k,1,6682,0);break K}n=H[U+20>>2];l=m;m=H[Y+16>>2];l=H[Y+8>>2]+N(l,m)|0;o=H[j+4>>2];o=l>>>0>o>>>0?l:o;H[U+4>>2]=o;m=l+m|0;l=l>>>0>m>>>0?-1:m;j=H[j+12>>2];j=j>>>0>l>>>0?l:j;H[U+12>>2]=j;if(!((j|0)>(o|0)&(o|0)>=0)){Ca(k,1,6644,0);break K}L:{if(H[M+4>>2]){if(H[U+16>>2]){break L}j=1;break J}Ca(k,1,5358,0);break K}M:{N:{while(1){H[Z+36>>2]=0;j=H[Z>>2];m=j>>31;z=j-1|0;l=H[U>>2];r=l;o=z+l|0;x=m-!j|0;l=x+(l>>31)|0;va=n,wa=Je(o,o>>>0<r>>>0?l+1|0:l,j,m),H[va>>2]=wa;o=H[Z+4>>2];t=o>>31;r=o-1|0;l=H[U+4>>2];p=l;A=r+l|0;q=t-!o|0;l=q+(l>>31)|0;va=n,wa=Je(A,p>>>0>A>>>0?l+1|0:l,o,t),H[va+4>>2]=wa;l=H[U+8>>2];A=l;z=l+z|0;l=(l>>31)+x|0;va=n,wa=Je(z,z>>>0<A>>>0?l+1|0:l,j,m),H[va+8>>2]=wa;j=H[U+12>>2];H[n+16>>2]=ga;l=q+(j>>31)|0;j=j+r|0;l=j>>>0<r>>>0?l+1|0:l;va=n,wa=Je(j,l,o,t),H[va+12>>2]=wa;j=H[M+4>>2];H[n+20>>2]=j;l=H[Y+80>>2];H[n+24>>2]=j>>>0<l>>>0?1:j-l|0;Da(H[n+52>>2]);H[n+68>>2]=0;H[n+60>>2]=0;H[n+64>>2]=0;H[n+52>>2]=0;H[n+56>>2]=0;j=N(j,152);l=H[n+28>>2];O:{if(!l){l=Ga(j);H[n+28>>2]=l;if(!l){break K}H[n+32>>2]=j;if(!j){break O}y(l,0,j);break O}if(j>>>0<=K[n+32>>2]){break O}l=Ia(l,j);if(!l){Ca(k,1,3090,0);Da(H[n+28>>2]);H[n+28>>2]=0;H[n+32>>2]=0;break K}H[n+28>>2]=l;o=H[n+32>>2];m=j-o|0;if(m){y(l+o|0,0,m)}H[n+32>>2]=j}j=H[n+20>>2];if(j){ja=M+944|0;ka=M+812|0;ea=M+28|0;o=H[n+28>>2];_=0;while(1){t=j-1|0;m=t&31;if((t&63)>>>0>=32){l=-1<<m;r=0}else{r=-1<<m;l=r|(1<<m)-1&-1>>>32-m}z=r^-1;r=H[n>>2];m=z+r|0;A=l^-1;l=A+(r>>31)|0;l=m>>>0<r>>>0?l+1|0:l;r=m;m=t&31;if((t&63)>>>0>=32){p=l>>m}else{p=((1<<m)-1&l)<<32-m|r>>>m}H[o>>2]=p;l=H[n+4>>2];r=l;m=l+z|0;l=(l>>31)+A|0;l=m>>>0<r>>>0?l+1|0:l;r=m;m=t&31;if((t&63)>>>0>=32){q=l>>m}else{q=((1<<m)-1&l)<<32-m|r>>>m}H[o+4>>2]=q;l=H[n+8>>2];r=l;m=l+z|0;l=(l>>31)+A|0;l=m>>>0<r>>>0?l+1|0:l;r=m;m=t&31;if((t&63)>>>0>=32){r=l>>m}else{r=((1<<m)-1&l)<<32-m|r>>>m}H[o+8>>2]=r;l=H[n+12>>2];x=l;m=l+z|0;l=(l>>31)+A|0;l=m>>>0<x>>>0?l+1|0:l;x=m;m=t&31;if((t&63)>>>0>=32){x=l>>m}else{x=((1<<m)-1&l)<<32-m|x>>>m}H[o+12>>2]=x;C=r>>31;E=_<<2;R=H[E+ka>>2];m=R&31;if((R&63)>>>0>=32){l=1<<m;v=0}else{v=1<<m;l=v-1&1>>>32-m}P=v;m=P+r|0;l=l+C|0;C=m-1|0;m=(m>>>0<P>>>0?l+1|0:l)-!m|0;l=R&31;if((R&63)>>>0>=32){l=m>>l}else{l=((1<<l)-1&m)<<32-l|C>>>l}C=l<<R;if((C|0)<0){break M}S=x>>31;P=H[E+ja>>2];m=P&31;if((P&63)>>>0>=32){l=-1<<m;m=0}else{l=(1<<m)-1&-1>>>32-m;m=-1<<m;l=l|m}E=m^-1;m=E+x|0;l=(l^-1)+S|0;l=m>>>0<E>>>0?l+1|0:l;E=m;m=P&31;if((P&63)>>>0>=32){l=l>>m}else{l=((1<<m)-1&l)<<32-m|E>>>m}l=l<<P;if((l|0)<0){break M}$=q&-1<<P;x=(q|0)!=(x|0)?l-$>>P:0;H[o+20>>2]=x;aa=p&-1<<R;m=(p|0)!=(r|0)?C-aa>>R:0;H[o+16>>2]=m;Ie(m,0,x);if(!(!m|!ra)){break N}ca=N(m,x);if(ca>>>0>=107374183){break N}V=N(ca,40);if(_){P=P-1|0;R=R-1|0;l=$>>31;m=$+1|0;$=((m?l:l+1|0)&1)<<31|m>>>1;l=aa>>31;m=aa+1|0;aa=((m?l:l+1|0)&1)<<31|m>>>1;l=3}else{l=1}H[o+24>>2]=l;m=o+28|0;x=j;r=j&31;if((j&63)>>>0>=32){l=1<<r;j=0}else{j=1<<r;l=j-1&1>>>32-r}ia=j;r=l;j=H[M+12>>2];S=j>>>0<P>>>0?j:P;j=S&31;if((S&63)>>>0>=32){l=-1<<j;j=0}else{l=(1<<j)-1&-1>>>32-j;j=-1<<j;l=l|j}la=j^-1;ma=l^-1;j=H[M+8>>2];T=j>>>0<R>>>0?j:R;j=T&31;if((T&63)>>>0>=32){l=-1<<j;j=0}else{l=(1<<j)-1&-1>>>32-j;j=-1<<j;l=l|j}na=j^-1;pa=l^-1;fa=0;while(1){P:{if(!_){l=H[n+4>>2];p=l;j=l+z|0;l=(l>>31)+A|0;l=j>>>0<p>>>0?l+1|0:l;p=j;j=t&31;if((t&63)>>>0>=32){Q=l>>j}else{Q=((1<<j)-1&l)<<32-j|p>>>j}l=H[n>>2];p=l;j=l+z|0;l=(l>>31)+A|0;l=j>>>0<p>>>0?l+1|0:l;p=j;j=t&31;if((t&63)>>>0>=32){ba=l>>j}else{ba=((1<<j)-1&l)<<32-j|p>>>j}j=0;p=z;C=p;q=A;E=q;l=t;break P}j=fa+1|0;p=j>>>1|0;q=t&31;if((t&63)>>>0>=32){l=p<<q;p=0}else{l=(1<<q)-1&p>>>32-q;p=p<<q}p=p^-1;C=p+ia|0;l=(l^-1)+r|0;l=p>>>0>C>>>0?l+1|0:l;q=H[n+4>>2];p=q+C|0;E=l;l=l+(q>>31)|0;l=p>>>0<q>>>0?l+1|0:l;q=p;p=x&31;if((x&63)>>>0>=32){Q=l>>p}else{Q=((1<<p)-1&l)<<32-p|q>>>p}p=j&1;q=t&31;if((t&63)>>>0>=32){l=p<<q;p=0}else{l=(1<<q)-1&p>>>32-q;p=p<<q}q=p^-1;p=q+ia|0;l=(l^-1)+r|0;v=H[n>>2];D=v+p|0;q=p>>>0<q>>>0?l+1|0:l;l=q+(v>>31)|0;l=v>>>0>D>>>0?l+1|0:l;v=D;D=x&31;if((x&63)>>>0>=32){ba=l>>D}else{ba=((1<<D)-1&l)<<32-D|v>>>D}l=x}D=l;v=H[n+8>>2];ha=v>>31;J=H[n+12>>2];H[m+4>>2]=Q;H[m>>2]=ba;H[m+16>>2]=j;l=(J>>31)+E|0;C=C+J|0;l=C>>>0<J>>>0?l+1|0:l;E=C;C=D&31;if((D&63)>>>0>=32){l=l>>C}else{l=((1<<C)-1&l)<<32-C|E>>>C}H[m+12>>2]=l;l=q+ha|0;p=p+v|0;l=p>>>0<v>>>0?l+1|0:l;q=p;p=D&31;if((D&63)>>>0>=32){l=l>>p}else{l=((1<<p)-1&l)<<32-p|q>>>p}H[m+8>>2]=l;da=1;p=H[ea>>2];j=(H[Z+24>>2]+(!H[M+20>>2]|!j?0:(j|0)==3?2:1)|0)-p|0;Q:{if((j|0)>=1024){da=898846567431158e293;if(j>>>0<2047){j=j-1023|0;break Q}da=Infinity;j=(j>>>0>=3069?3069:j)-2046|0;break Q}if((j|0)>-1023){break Q}da=2004168360008973e-307;if(j>>>0>4294965304){j=j+969|0;break Q}da=0;j=(j>>>0<=4294964336?-2960:j)+1938|0}qa=+H[ea+4>>2]*.00048828125+1;u(0,0);u(1,j+1023<<20);va=m,xa=O(qa*(da*+w())),L[va+32>>2]=xa;H[m+28>>2]=(p+H[M+804>>2]|0)-1;j=H[m+20>>2];R:{S:{if(!(j|!ca)){j=Ga(V);H[m+20>>2]=j;if(!j){Ca(k,1,2854,0);break K}if(V){y(j,0,V)}H[m+24>>2]=V;break S}if(V>>>0>K[m+24>>2]){j=Ia(j,V);if(!j){Ca(k,1,2854,0);Da(H[m+20>>2]);H[m+20>>2]=0;H[m+24>>2]=0;break K}H[m+20>>2]=j;l=H[m+24>>2];p=V-l|0;if(p){y(j+l|0,0,p)}H[m+24>>2]=V}if(!ca){break R}}j=H[m+20>>2];C=0;while(1){p=H[o+16>>2];l=(C>>>0)/(p>>>0)|0;p=C-N(l,p)|0;q=(p<<R)+aa|0;E=H[m>>2];E=(q|0)>(E|0)?q:E;H[j>>2]=E;q=(l<<P)+$|0;D=H[m+4>>2];D=(q|0)>(D|0)?q:D;H[j+4>>2]=D;p=(p+1<<R)+aa|0;q=H[m+8>>2];p=(p|0)<(q|0)?p:q;H[j+8>>2]=p;l=(l+1<<P)+$|0;q=H[m+12>>2];q=(l|0)<(q|0)?l:q;H[j+12>>2]=q;l=(p>>31)+pa|0;v=p;p=p+na|0;l=v>>>0>p>>>0?l+1|0:l;E=E>>T;v=p;p=T&31;if((T&63)>>>0>=32){l=l>>p}else{l=((1<<p)-1&l)<<32-p|v>>>p}v=l-E<<T>>T;H[j+16>>2]=v;l=(q>>31)+ma|0;p=q+la|0;l=p>>>0<q>>>0?l+1|0:l;D=D>>S;q=p;p=S&31;if((S&63)>>>0>=32){l=l>>p}else{l=((1<<p)-1&l)<<32-p|q>>>p}l=l-D<<S>>S;H[j+20>>2]=l;p=N(l,v);Ie(p,0,68);if(ra){Ca(k,1,2935,0);break K}l=N(p,68);q=H[j+24>>2];T:{U:{if(!(q|!p)){q=Ga(l);H[j+24>>2]=q;if(!q){break K}if(!l){break U}y(q,0,l);break U}if(l>>>0<=K[j+28>>2]){break T}q=Ia(q,l);if(!q){Da(H[j+24>>2]);H[j+24>>2]=0;H[j+28>>2]=0;Ca(k,1,2549,0);break K}H[j+24>>2]=q;v=H[j+28>>2];J=l-v|0;if(!J){break U}y(q+v|0,0,J)}H[j+28>>2]=l}l=H[j+20>>2];q=H[j+16>>2];v=H[j+32>>2];V:{if(!v){l=sc(q,l,k);break V}l=qc(v,q,l,k)}H[j+32>>2]=l;l=H[j+20>>2];q=H[j+16>>2];v=H[j+36>>2];W:{if(!v){l=sc(q,l,k);break W}l=qc(v,q,l,k)}H[j+36>>2]=l;if(p){ba=D+1|0;ha=E+1|0;q=0;while(1){W=H[j+16>>2];v=(q>>>0)/(W>>>0)|0;l=H[j+24>>2]+N(q,68)|0;Q=H[l>>2];X:{if(Q){ta=H[l+56>>2];J=H[l+48>>2];ua=H[l+4>>2];Da(H[l+60>>2]);H[l+48>>2]=0;H[l+52>>2]=0;H[l- -64>>2]=0;H[l+56>>2]=0;H[l+60>>2]=0;H[l+40>>2]=0;H[l+44>>2]=0;H[l+32>>2]=0;H[l+36>>2]=0;H[l+24>>2]=0;H[l+28>>2]=0;H[l+16>>2]=0;H[l+20>>2]=0;H[l+8>>2]=0;H[l+12>>2]=0;H[l>>2]=Q;H[l+48>>2]=J;Y:{if(!J){break Y}J=N(J,24);if(!J){break Y}y(Q,0,J)}H[l+56>>2]=ta;H[l+4>>2]=ua;break X}J=Fa(10,24);H[l>>2]=J;if(!J){break K}H[l+48>>2]=10}J=q-N(v,W)|0;Q=J+E<<T;W=H[j>>2];H[l+8>>2]=(Q|0)>(W|0)?Q:W;Q=v+D<<S;W=H[j+4>>2];H[l+12>>2]=(Q|0)>(W|0)?Q:W;J=J+ha<<T;Q=H[j+8>>2];H[l+16>>2]=(J|0)<(Q|0)?J:Q;Q=l;l=v+ba<<S;v=H[j+12>>2];H[Q+20>>2]=(l|0)<(v|0)?l:v;q=q+1|0;if((p|0)!=(q|0)){continue}break}}j=j+40|0;C=C+1|0;if((C|0)!=(ca|0)){continue}break}}ea=ea+8|0;m=m+36|0;fa=fa+1|0;if(fa>>>0<K[o+24>>2]){continue}break}o=o+152|0;j=t;_=_+1|0;if(_>>>0<K[n+20>>2]){continue}break}}Z=Z+52|0;n=n+76|0;M=M+1080|0;ga=ga+1|0;if(ga>>>0<K[U+16>>2]){continue}break}j=1;break J}Ca(k,1,2982,0);break K}Ca(k,1,2373,0)}j=0}if(!j){Ca(k,1,3668,0);j=0;break a}j=H[a+228>>2];H[s+4>>2]=N(H[a+128>>2],H[a+132>>2]);H[s>>2]=j+1;Ca(k,4,11825,s);H[b>>2]=H[a+228>>2];H[i>>2]=1;if(c){b=bc(H[a+232>>2],0);H[c>>2]=b;j=0;if((b|0)==-1){break a}}b=H[H[H[a+232>>2]+20>>2]>>2];H[d>>2]=H[b>>2];H[e>>2]=H[b+4>>2];H[f>>2]=H[b+8>>2];H[g>>2]=H[b+12>>2];H[h>>2]=H[b+16>>2];H[a+8>>2]=H[a+8>>2]|128}j=1;break a}Ca(k,1,m,0)}Ca(k,1,3702,0);j=0}oa=s+80|0;return j|0}function fc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,L=0,M=0,O=0,P=0,Q=0,R=0,S=0,T=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{switch(H[a+84>>2]){case 0:k:{c=H[a+52>>2];b=H[a+196>>2];if(c>>>0<b>>>0){q=H[a+64>>2];if(q>>>0<b+1>>>0){break k}}Ca(H[a+236>>2],1,8491,0);break b}if(!H[a+44>>2]){k=H[a+36>>2];b=0;break i}H[a+44>>2]=0;i=H[a+68>>2];b=1;break i;case 1:l:{c=H[a+52>>2];b=H[a+196>>2];if(c>>>0<b>>>0){q=H[a+64>>2];if(q>>>0<b+1>>>0){break l}}Ca(H[a+236>>2],1,8536,0);break b}if(!H[a+44>>2]){e=H[a+36>>2];b=0;break e}H[a+44>>2]=0;i=H[a+48>>2];b=1;break e;case 2:m:{A=H[a+52>>2];x=H[a+196>>2];if(A>>>0<x>>>0){r=H[a+64>>2];if(r>>>0<x+1>>>0){break m}}Ca(H[a+236>>2],1,8671,0);break b}if(!H[a+44>>2]){y=H[a+40>>2];break f}H[a+228>>2]=0;H[a+232>>2]=0;H[a+44>>2]=0;j=H[a+200>>2];while(1){O=j+(u<<4)|0;l=H[O+8>>2];if(l){q=H[O+12>>2];b=0;while(1){g=l+(b^-1)|0;d=q+(b<<4)|0;s=g+H[d>>2]|0;n:{if(s>>>0>31){break n}c=H[O>>2];if(c>>>0>-1>>>s>>>0){break n}c=c<<s;k=k?c>>>0>k>>>0?k:c:c;H[a+228>>2]=k}g=g+H[d+4>>2]|0;o:{if(g>>>0>31){break o}c=H[O+4>>2];if(c>>>0>-1>>>g>>>0){break o}c=c<<g;i=i?c>>>0>i>>>0?i:c:c;H[a+232>>2]=i}b=b+1|0;if((l|0)!=(b|0)){continue}break}}u=u+1|0;if((x|0)!=(u|0)){continue}break};if(!k|!i){break d}if(!I[a|0]){H[a+108>>2]=H[a+208>>2];H[a+100>>2]=H[a+204>>2];H[a+112>>2]=H[a+216>>2];H[a+104>>2]=H[a+212>>2]}o=H[a+48>>2];b=1;break f;case 3:p:{A=H[a+52>>2];l=H[a+196>>2];if(A>>>0<l>>>0){P=H[a+64>>2];if(P>>>0<l+1>>>0){break p}}Ca(H[a+236>>2],1,8626,0);break b}if(!H[a+44>>2]){B=H[a+200>>2];e=H[a+28>>2];y=B+(e<<4)|0;E=H[a+40>>2];break g}H[a+228>>2]=0;H[a+232>>2]=0;H[a+44>>2]=0;B=H[a+200>>2];while(1){x=(p<<4)+B|0;s=H[x+8>>2];if(s){q=H[x+12>>2];b=0;while(1){g=s+(b^-1)|0;d=q+(b<<4)|0;j=g+H[d>>2]|0;q:{if(j>>>0>31){break q}c=H[x>>2];if(c>>>0>-1>>>j>>>0){break q}c=c<<j;k=k?c>>>0>k>>>0?k:c:c;H[a+228>>2]=k}g=g+H[d+4>>2]|0;r:{if(g>>>0>31){break r}c=H[x+4>>2];if(c>>>0>-1>>>g>>>0){break r}c=c<<g;i=i?c>>>0>i>>>0?i:c:c;H[a+232>>2]=i}b=b+1|0;if((s|0)!=(b|0)){continue}break}}p=p+1|0;if((l|0)!=(p|0)){continue}break};if(!k|!i){break d}s:{if(I[a|0]){p=H[a+108>>2];break s}p=H[a+208>>2];H[a+108>>2]=p;H[a+100>>2]=H[a+204>>2];H[a+112>>2]=H[a+216>>2];H[a+104>>2]=H[a+212>>2]}b=1;break g;case 4:break j;default:break d}}t:{p=H[a+52>>2];b=H[a+196>>2];if(p>>>0<b>>>0){r=H[a+64>>2];if(r>>>0<b+1>>>0){break t}}Ca(H[a+236>>2],1,8581,0);break d}if(!H[a+44>>2]){p=H[a+28>>2];o=H[a+200>>2]+(p<<4)|0;u=H[a+40>>2];b=0;break h}H[a+28>>2]=p;H[a+44>>2]=0;b=1;break h}u:while(1){v:{w:{if(!b){k=k+1|0;break w}H[a+40>>2]=i;if(K[a+56>>2]<=i>>>0){break b}e=H[a+48>>2];b=0;break v}b=1}x:while(1){y:{z:{A:{B:{if(!b){H[a+32>>2]=e;if(K[a+60>>2]<=e>>>0){break B}H[a+28>>2]=c;b=c;o=0;break y}H[a+36>>2]=k;if(K[a+76>>2]<=k>>>0){b=H[a+28>>2];o=1;break y}b=((N(H[a+16>>2],H[a+32>>2])+N(H[a+12>>2],H[a+40>>2])|0)+N(H[a+20>>2],H[a+28>>2])|0)+N(H[a+24>>2],k)|0;if(b>>>0>=K[a+8>>2]){break c}b=H[a+4>>2]+(b<<1)|0;if(J[b>>1]){break A}break a}i=H[a+40>>2]+1|0;break z}b=0;continue u}b=1;continue u}while(1){C:{D:{E:{if(!o){if(b>>>0>=q>>>0){break E}g=H[a+32>>2];d=H[a+200>>2]+(b<<4)|0;if(g>>>0>=K[d+8>>2]){break C}if(!I[a|0]){b=H[d+12>>2]+(g<<4)|0;H[a+76>>2]=N(H[b+12>>2],H[b+8>>2])}k=H[a+72>>2];b=1;continue x}b=b+1|0;H[a+28>>2]=b;break D}e=H[a+32>>2]+1|0;b=0;continue x}o=0;continue}o=1;continue}}}}F:while(1){G:{H:{if(!b){u=u+1|0;H[a+40>>2]=u;break H}if(p>>>0>=r>>>0){break b}H[a+228>>2]=0;H[a+232>>2]=0;o=H[a+200>>2]+(p<<4)|0;s=H[o+8>>2];if(!s){break b}q=H[o+12>>2];k=0;e=0;b=0;while(1){g=s+(b^-1)|0;d=q+(b<<4)|0;j=g+H[d>>2]|0;I:{if(j>>>0>31){break I}c=H[o>>2];if(c>>>0>-1>>>j>>>0){break I}c=c<<j;e=e?c>>>0>e>>>0?e:c:c;H[a+228>>2]=e}g=g+H[d+4>>2]|0;J:{if(g>>>0>31){break J}c=H[o+4>>2];if(c>>>0>-1>>>g>>>0){break J}c=c<<g;k=k?c>>>0>k>>>0?k:c:c;H[a+232>>2]=k}b=b+1|0;if((s|0)!=(b|0)){continue}break}if(!e|!k){break d}K:{if(I[a|0]){k=H[a+108>>2];break K}k=H[a+208>>2];H[a+108>>2]=k;H[a+100>>2]=H[a+204>>2];H[a+112>>2]=H[a+216>>2];H[a+104>>2]=H[a+212>>2]}b=0;break G}b=1}L:while(1){M:{N:{O:{P:{if(!b){H[a+224>>2]=k;if(K[a+112>>2]<=k>>>0){break P}B=H[a+100>>2];b=0;break M}if(K[a+56>>2]<=u>>>0){i=H[a+32>>2];b=1;break M}b=((N(H[a+16>>2],H[a+32>>2])+N(H[a+12>>2],u)|0)+N(H[a+20>>2],p)|0)+N(H[a+24>>2],H[a+36>>2])|0;if(b>>>0>=K[a+8>>2]){break c}b=H[a+4>>2]+(b<<1)|0;if(J[b>>1]){break O}break a}p=p+1|0;H[a+28>>2]=p;break N}b=0;continue F}b=1;continue F}while(1){Q:{R:{S:{T:{if(!b){H[a+220>>2]=B;if(K[a+104>>2]<=B>>>0){break S}i=H[a+48>>2];break T}i=i+1|0}H[a+32>>2]=i;b=H[a+60>>2];d=H[o+8>>2];if((b>>>0<d>>>0?b:d)>>>0>i>>>0){g=H[o>>2];c=g;n=d+(i^-1)|0;m=n;d=m&31;if((m&63)>>>0>=32){b=c<<d;v=0}else{b=(1<<d)-1&g>>>32-d;v=g<<d}q=c;f=b;c=v;d=m&31;if((m&63)>>>0>=32){b=b>>>d|0}else{b=((1<<d)-1&b)<<32-d|c>>>d}if((q|0)!=(b|0)){break Q}b=m&31;if((m&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}c=H[o+4>>2];if((b&c)!=(c|0)){break Q}d=m&31;if((m&63)>>>0>=32){b=c<<d;w=0}else{b=(1<<d)-1&c>>>32-d;w=c<<d}C=b;q=b-!w|0;h=q;F=w-1|0;d=H[a+216>>2];j=F+d|0;O=Ke(j,d>>>0>j>>>0?h+1|0:h,w,b);b=h;L=H[a+208>>2];d=F+L|0;b=L>>>0>d>>>0?b+1|0:b;s=Ke(d,b,w,C);A=v-1|0;j=H[a+212>>2];l=A+j|0;d=f-!v|0;b=d;x=Ke(l,l>>>0<j>>>0?b+1|0:b,v,f);D=H[a+204>>2];j=A+D|0;b=D>>>0>j>>>0?b+1|0:b;j=Ke(j,b,v,f);z=H[o+12>>2]+(i<<4)|0;M=H[z>>2];t=M+n|0;b=t&31;if((t&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}if((g|0)!=(b&g)){break Q}h=c;P=H[z+4>>2];n=P+n|0;e=n&31;if((n&63)>>>0>=32){b=c<<e;e=0}else{b=(1<<e)-1&c>>>32-e;e=c<<e}c=e;l=n&31;if((n&63)>>>0>=32){c=b>>>l|0}else{c=((1<<l)-1&b)<<32-l|c>>>l}if((h|0)!=(c|0)){break Q}l=H[a+224>>2];e=!!(Le(l,e,b)|ra);b=n&31;if((n&63)>>>0>=32){h=-1<<b;b=0}else{c=(1<<b)-1&-1>>>32-b;b=-1<<b;h=c|b}b=b^-1;c=h^-1;Q=e;R=b;b=0;n=s;e=m&31;if((m&63)>>>0>=32){h=n<<e;b=0}else{h=(1<<e)-1&n>>>32-e|b<<e;b=n<<e}if(Q&(!(R&b|c&h)|(l|0)!=(L|0))){break Q}n=t&31;c=H[a+220>>2];if((t&63)>>>0>=32){b=g<<n;e=0}else{b=(1<<n)-1&g>>>32-n;e=g<<n}n=!!(Le(c,e,b)|ra);b=t&31;if((t&63)>>>0>=32){h=-1<<b;b=0}else{e=(1<<b)-1&-1>>>32-b;b=-1<<b;h=e|b}b=b^-1;g=h^-1;e=n;Q=b;b=0;n=j;t=m&31;if((m&63)>>>0>=32){h=j<<t;b=0}else{h=(1<<t)-1&n>>>32-t|b<<t;b=n<<t}if(e&(!(Q&b|g&h)|(c|0)!=(D|0))){break Q}n=H[z+8>>2];if(!n|(!H[z+12>>2]|(j|0)==(x|0))){break Q}if((s|0)==(O|0)){break Q}u=H[a+68>>2];H[a+40>>2]=u;b=d;c=c+A|0;b=c>>>0<A>>>0?b+1|0:b;g=(Ke(c,b,v,f)>>>M)-(j>>>M)|0;b=q;c=l+F|0;b=c>>>0<l>>>0?b+1|0:b;S=a,T=N(n,(Ke(c,b,w,C)>>>P)-(s>>>P)|0)+g|0,H[S+36>>2]=T;b=1;continue L}c=H[a+220>>2];b=H[a+228>>2];B=c+b-(c>>>0)%(b>>>0)|0;break R}c=H[a+224>>2];b=H[a+232>>2];k=c+b-(c>>>0)%(b>>>0)|0;b=0;continue L}b=0;continue}b=1;continue}}}}U:while(1){V:{W:{if(!b){E=E+1|0;H[a+40>>2]=E;break W}H[a+224>>2]=p;if(K[a+112>>2]<=p>>>0){break b}v=H[a+100>>2];b=0;break V}b=1}X:while(1){Y:{Z:{_:{$:{if(!b){H[a+220>>2]=v;if(K[a+104>>2]<=v>>>0){break $}H[a+28>>2]=A;e=A;b=0;break Y}if(K[a+56>>2]<=E>>>0){u=H[a+32>>2];b=1;break Y}b=((N(H[a+16>>2],H[a+32>>2])+N(H[a+12>>2],E)|0)+N(H[a+20>>2],e)|0)+N(H[a+24>>2],H[a+36>>2])|0;if(b>>>0>=K[a+8>>2]){break c}b=H[a+4>>2]+(b<<1)|0;if(J[b>>1]){break _}break a}c=H[a+224>>2];b=H[a+232>>2];p=c+b-(c>>>0)%(b>>>0)|0;break Z}b=0;continue U}b=1;continue U}while(1){aa:{ba:{ca:{da:{if(!b){if(e>>>0>=P>>>0){break ca}u=H[a+48>>2];H[a+32>>2]=u;y=(e<<4)+B|0;break da}u=u+1|0;H[a+32>>2]=u}b=H[a+60>>2];d=H[y+8>>2];if((b>>>0<d>>>0?b:d)>>>0>u>>>0){g=H[y>>2];c=g;f=d+(u^-1)|0;i=f;d=f&31;if((f&63)>>>0>=32){b=c<<d;k=0}else{b=(1<<d)-1&g>>>32-d;k=g<<d}q=c;t=b;c=k;d=i&31;if((i&63)>>>0>=32){b=b>>>d|0}else{b=((1<<d)-1&b)<<32-d|c>>>d}if((q|0)!=(b|0)){break aa}b=i&31;if((i&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}c=H[y+4>>2];if((b&c)!=(c|0)){break aa}d=i&31;if((i&63)>>>0>=32){b=c<<d;o=0}else{b=(1<<d)-1&c>>>32-d;o=c<<d}n=b;q=b-!o|0;h=q;F=o-1|0;d=H[a+216>>2];j=F+d|0;O=Ke(j,d>>>0>j>>>0?h+1|0:h,o,b);b=h;w=H[a+208>>2];d=w+F|0;b=w>>>0>d>>>0?b+1|0:b;s=Ke(d,b,o,n);C=k-1|0;j=H[a+212>>2];l=C+j|0;d=t-!k|0;b=d;x=Ke(l,l>>>0<j>>>0?b+1|0:b,k,t);L=H[a+204>>2];j=C+L|0;b=L>>>0>j>>>0?b+1|0:b;j=Ke(j,b,k,t);D=H[y+12>>2]+(u<<4)|0;z=H[D>>2];m=z+f|0;b=m&31;if((m&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}if((g|0)!=(b&g)){break aa}h=c;M=H[D+4>>2];f=M+f|0;r=f&31;if((f&63)>>>0>=32){b=c<<r;r=0}else{b=(1<<r)-1&c>>>32-r;r=c<<r}c=r;l=f&31;if((f&63)>>>0>=32){c=b>>>l|0}else{c=((1<<l)-1&b)<<32-l|c>>>l}if((h|0)!=(c|0)){break aa}l=H[a+224>>2];r=!!(Le(l,r,b)|ra);b=f&31;if((f&63)>>>0>=32){h=-1<<b;b=0}else{c=(1<<b)-1&-1>>>32-b;b=-1<<b;h=c|b}b=b^-1;c=h^-1;Q=r;R=b;b=0;f=s;r=i&31;if((i&63)>>>0>=32){h=f<<r;b=0}else{h=(1<<r)-1&f>>>32-r|b<<r;b=f<<r}if(Q&(!(R&b|c&h)|(l|0)!=(w|0))){break aa}f=m&31;c=H[a+220>>2];if((m&63)>>>0>=32){b=g<<f;f=0}else{b=(1<<f)-1&g>>>32-f;f=g<<f}f=!!(Le(c,f,b)|ra);b=m&31;if((m&63)>>>0>=32){h=-1<<b;b=0}else{g=(1<<b)-1&-1>>>32-b;b=-1<<b;h=g|b}b=b^-1;g=h^-1;r=f;w=b;b=0;f=j;m=i&31;if((i&63)>>>0>=32){h=f<<m;b=0}else{h=(1<<m)-1&f>>>32-m|b<<m;b=f<<m}if(r&(!(w&b|g&h)|(c|0)!=(L|0))){break aa}f=H[D+8>>2];if(!f|(!H[D+12>>2]|(j|0)==(x|0))){break aa}if((s|0)==(O|0)){break aa}E=H[a+68>>2];H[a+40>>2]=E;b=d;c=c+C|0;b=c>>>0<C>>>0?b+1|0:b;g=(Ke(c,b,k,t)>>>z)-(j>>>z)|0;b=q;c=l+F|0;b=c>>>0<l>>>0?b+1|0:b;S=a,T=N(f,(Ke(c,b,o,n)>>>M)-(s>>>M)|0)+g|0,H[S+36>>2]=T;b=1;continue X}e=e+1|0;H[a+28>>2]=e;break ba}c=H[a+220>>2];b=H[a+228>>2];v=c+b-(c>>>0)%(b>>>0)|0;b=0;continue X}b=0;continue}b=1;continue}}}}ea:while(1){fa:{ga:{if(!b){y=y+1|0;H[a+40>>2]=y;break ga}H[a+32>>2]=o;if(K[a+60>>2]<=o>>>0){break b}E=H[a+108>>2];b=0;break fa}b=1}ha:while(1){ia:{ja:{ka:{la:{if(!b){H[a+224>>2]=E;if(K[a+112>>2]<=E>>>0){break la}B=H[a+100>>2];b=0;break ia}if(K[a+56>>2]<=y>>>0){p=H[a+28>>2];b=1;break ia}b=((N(H[a+16>>2],H[a+32>>2])+N(H[a+12>>2],y)|0)+N(H[a+20>>2],H[a+28>>2])|0)+N(H[a+24>>2],H[a+36>>2])|0;if(b>>>0>=K[a+8>>2]){break c}b=H[a+4>>2]+(b<<1)|0;if(J[b>>1]){break ka}break a}o=H[a+32>>2]+1|0;break ja}b=0;continue ea}b=1;continue ea}while(1){ma:{na:{oa:{pa:{if(!b){H[a+220>>2]=B;if(K[a+104>>2]<=B>>>0){break oa}H[a+28>>2]=A;p=A;break pa}p=p+1|0;H[a+28>>2]=p}if(p>>>0<r>>>0){m=H[a+32>>2];e=H[a+200>>2]+(p<<4)|0;b=H[e+8>>2];if(m>>>0>=b>>>0){break ma}g=H[e>>2];c=g;f=b+(m^-1)|0;i=f;d=f&31;if((f&63)>>>0>=32){b=c<<d;v=0}else{b=(1<<d)-1&g>>>32-d;v=g<<d}q=c;t=b;c=v;d=i&31;if((i&63)>>>0>=32){b=b>>>d|0}else{b=((1<<d)-1&b)<<32-d|c>>>d}if((q|0)!=(b|0)){break ma}b=i&31;if((i&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}c=H[e+4>>2];if((b&c)!=(c|0)){break ma}d=i&31;if((i&63)>>>0>=32){b=c<<d;w=0}else{b=(1<<d)-1&c>>>32-d;w=c<<d}n=b;q=b-!w|0;h=q;F=w-1|0;d=H[a+216>>2];j=F+d|0;O=Ke(j,d>>>0>j>>>0?h+1|0:h,w,b);b=h;L=H[a+208>>2];d=F+L|0;b=L>>>0>d>>>0?b+1|0:b;s=Ke(d,b,w,n);C=v-1|0;j=H[a+212>>2];l=C+j|0;d=t-!v|0;b=d;x=Ke(l,l>>>0<j>>>0?b+1|0:b,v,t);D=H[a+204>>2];j=C+D|0;b=D>>>0>j>>>0?b+1|0:b;j=Ke(j,b,v,t);z=H[e+12>>2]+(m<<4)|0;M=H[z>>2];m=M+f|0;b=m&31;if((m&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}if((g|0)!=(b&g)){break ma}h=c;P=H[z+4>>2];f=P+f|0;e=f&31;if((f&63)>>>0>=32){b=c<<e;e=0}else{b=(1<<e)-1&c>>>32-e;e=c<<e}c=e;l=f&31;if((f&63)>>>0>=32){c=b>>>l|0}else{c=((1<<l)-1&b)<<32-l|c>>>l}if((h|0)!=(c|0)){break ma}l=H[a+224>>2];e=!!(Le(l,e,b)|ra);b=f&31;if((f&63)>>>0>=32){h=-1<<b;b=0}else{c=(1<<b)-1&-1>>>32-b;b=-1<<b;h=c|b}b=b^-1;c=h^-1;k=e;Q=b;b=0;f=s;e=i&31;if((i&63)>>>0>=32){h=f<<e;b=0}else{h=(1<<e)-1&f>>>32-e|b<<e;b=f<<e}if(k&(!(Q&b|c&h)|(l|0)!=(L|0))){break ma}f=m&31;c=H[a+220>>2];if((m&63)>>>0>=32){b=g<<f;f=0}else{b=(1<<f)-1&g>>>32-f;f=g<<f}f=!!(Le(c,f,b)|ra);b=m&31;if((m&63)>>>0>=32){h=-1<<b;b=0}else{e=(1<<b)-1&-1>>>32-b;b=-1<<b;h=e|b}b=b^-1;g=h^-1;e=f;k=b;b=0;f=j;m=i&31;if((i&63)>>>0>=32){h=f<<m;b=0}else{h=(1<<m)-1&f>>>32-m|b<<m;b=f<<m}if(e&(!(k&b|g&h)|(c|0)!=(D|0))){break ma}f=H[z+8>>2];if(!f|(!H[z+12>>2]|(j|0)==(x|0))){break ma}if((s|0)==(O|0)){break ma}y=H[a+68>>2];H[a+40>>2]=y;b=d;c=c+C|0;b=c>>>0<C>>>0?b+1|0:b;g=(Ke(c,b,v,t)>>>M)-(j>>>M)|0;b=q;c=l+F|0;b=c>>>0<l>>>0?b+1|0:b;S=a,T=N(f,(Ke(c,b,w,n)>>>P)-(s>>>P)|0)+g|0,H[S+36>>2]=T;b=1;continue ha}c=H[a+220>>2];b=H[a+228>>2];B=c+b-(c>>>0)%(b>>>0)|0;break na}c=H[a+224>>2];b=H[a+232>>2];E=c+b-(c>>>0)%(b>>>0)|0;b=0;continue ha}b=0;continue}b=1;continue}}}}qa:while(1){ra:{sa:{if(!b){e=e+1|0;break sa}H[a+32>>2]=i;if(K[a+60>>2]<=i>>>0){break b}k=H[a+68>>2];b=0;break ra}b=1}ta:while(1){ua:{va:{wa:{xa:{if(!b){H[a+40>>2]=k;if(K[a+56>>2]<=k>>>0){break xa}H[a+28>>2]=c;b=c;o=0;break ua}H[a+36>>2]=e;if(K[a+76>>2]<=e>>>0){b=H[a+28>>2];o=1;break ua}b=((N(H[a+16>>2],H[a+32>>2])+N(H[a+12>>2],H[a+40>>2])|0)+N(H[a+20>>2],H[a+28>>2])|0)+N(H[a+24>>2],e)|0;if(b>>>0>=K[a+8>>2]){break c}b=H[a+4>>2]+(b<<1)|0;if(J[b>>1]){break wa}break a}i=H[a+32>>2]+1|0;break va}b=0;continue qa}b=1;continue qa}while(1){ya:{za:{Aa:{if(!o){if(b>>>0>=q>>>0){break Aa}g=H[a+32>>2];d=H[a+200>>2]+(b<<4)|0;if(g>>>0>=K[d+8>>2]){break ya}if(!I[a|0]){b=H[d+12>>2]+(g<<4)|0;H[a+76>>2]=N(H[b+12>>2],H[b+8>>2])}e=H[a+72>>2];b=1;continue ta}b=b+1|0;H[a+28>>2]=b;break za}k=H[a+40>>2]+1|0;b=0;continue ta}o=0;continue}o=1;continue}}}}return 0}Ca(H[a+236>>2],1,1343,0)}return 0}G[b>>1]=1;return 1}function yd(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=O(0),t=0,u=0,v=0,w=O(0),x=0,z=0,A=0,C=O(0),D=O(0),E=O(0),G=0,J=0,K=0,L=0,M=0,Q=O(0),R=0,S=0,T=0;n=oa-8320|0;oa=n;H[n+64>>2]=0;j=2;h=H[a>>2];a:{b:{if((h|0)==176622093){break b}if((h|0)!=1375686655){if(!((h|0)!=201326592|H[a+4>>2]!=538988650)&H[a+8>>2]==176622093){break b}Y(1101);j=1;break a}j=0}h=Fa(1,96);g=0;c:{if(!h){break c}H[h+76>>2]=1;d:{e:{f:{switch(j|0){case 0:H[h+88>>2]=68;H[h+84>>2]=69;H[h+80>>2]=70;H[h+16>>2]=71;H[h+4>>2]=72;H[h+28>>2]=73;H[h+24>>2]=74;H[h+20>>2]=75;H[h>>2]=76;H[h+92>>2]=77;H[h+44>>2]=78;H[h+40>>2]=79;H[h+36>>2]=80;H[h+32>>2]=81;H[h+12>>2]=82;H[h+8>>2]=83;g=Ub();H[h+48>>2]=g;if(g){break e}break d;case 2:break f;default:break d}}H[h+88>>2]=84;H[h+84>>2]=85;H[h+80>>2]=86;H[h+16>>2]=87;H[h+4>>2]=88;H[h+92>>2]=89;H[h+44>>2]=90;H[h+40>>2]=91;H[h+36>>2]=92;H[h+32>>2]=93;H[h+28>>2]=94;H[h+24>>2]=95;H[h+20>>2]=96;H[h+12>>2]=97;H[h+8>>2]=98;H[h>>2]=99;g=Fa(1,136);g:{if(g){l=Ub();H[g>>2]=l;h:{if(!l){break h}H[g+108>>2]=0;H[g+112>>2]=0;F[g+124|0]=0;H[g+116>>2]=0;H[g+120>>2]=0;l=tb();H[g+4>>2]=l;if(!l){break h}l=tb();H[g+8>>2]=l;if(!l){break h}break g}Pc(g)}g=0}H[h+48>>2]=g;if(!g){break d}}H[h+72>>2]=1;H[h+64>>2]=1;H[h+60>>2]=0;H[h+52>>2]=0;H[h+56>>2]=0;H[h+68>>2]=1;g=h;break c}Da(h);g=0}if(g){H[g+60>>2]=0;H[g+72>>2]=100}if(g){H[g+56>>2]=0;H[g+68>>2]=101}if(g){H[g+52>>2]=0;H[g+64>>2]=102}h=n+68|0;if(h){y(h,0,8248);H[h+8248>>2]=0;H[h+8200>>2]=-1;H[h+8204>>2]=-1}if(d){H[n+8316>>2]=H[n+8316>>2]|1}H[n+60>>2]=b;H[n+56>>2]=a;H[n+52>>2]=a;j=1;b=0;h=n+52|0;i:{if(!h){break i}a=Fa(1,72);if(a){j:{H[a+64>>2]=1048576;l=Ga(1048576);H[a+32>>2]=l;if(!l){Da(a);a=0;break j}H[a+36>>2]=l;H[a+28>>2]=2;H[a+24>>2]=3;H[a+20>>2]=4;H[a+16>>2]=5;H[a+44>>2]=6;H[a+40>>2]=8;H[a+68>>2]=H[a+68>>2]|2}}else{a=0}if(!a){break i}if(a){H[a+4>>2]=0;H[a>>2]=h}b=H[h+8>>2];if(a){H[a+8>>2]=b;H[a+12>>2]=0}if(!(!a|!(I[a+68|0]&2))){H[a+16>>2]=64}if(a){H[a+24>>2]=66}if(a){H[a+28>>2]=67}b=a}a=n+68|0;if(!g|!a){a=0}else{k:{if(!H[g+76>>2]){Ca(g+52|0,1,9902,0);a=0;break k}sa[H[g+24>>2]](H[g+48>>2],a);a=1}}if(!a){Y(1153);jb(b);lb(g);break a}if(!b|!g){a=0}else{l:{if(!H[g+76>>2]){Ca(g+52|0,1,9983,0);a=0;break l}a=sa[H[g>>2]](b,H[g+48>>2],n- -64|0,g+52|0)|0}}if(!a){Y(1181);jb(b);lb(g);Va(H[n+64>>2]);break a}m:{if(!f){break m}if(g){a=sa[H[g+40>>2]](H[g+48>>2],f,g+52|0)|0}else{a=0}if(a){break m}Y(1116);jb(b);lb(g);Va(H[n+64>>2]);break a}a=H[n+64>>2];f=0;n:{if(!H[g+76>>2]|(!g|!b)){a=f}else{a=sa[H[g+4>>2]](H[g+48>>2],b,a,g+52|0)|0}if(a){if(!(!H[g+76>>2]|(!g|!b))){f=sa[H[g+16>>2]](H[g+48>>2],b,g+52|0)|0}if(f){break n}}Y(1316);lb(g);jb(b);Va(H[n+64>>2]);break a}jb(b);lb(g);k=H[n+64>>2];a=H[k+28>>2];if(a){Da(a);k=H[n+64>>2];H[k+28>>2]=0;H[k+32>>2]=0}v=H[k+16>>2];o:{p:{if(!c){if(!(!e|(v|0)!=4)){p=1;v=4;break o}q:{b=H[k+20>>2];if(!((b|0)==3|(v|0)!=3)){a=H[k+24>>2];if(H[a>>2]!=H[a+4>>2]|H[a+52>>2]==1){break q}H[k+20>>2]=3;break p}if(v>>>0>2){break q}H[k+20>>2]=2;break o}r:{switch(b-3|0){case 2:s:{t:{if(v>>>0<4){break t}f=H[k+24>>2];a=H[f>>2];if((a|0)!=H[f+52>>2]|(a|0)!=H[f+104>>2]|(a|0)!=H[f+156>>2]){break t}a=H[f+4>>2];if((a|0)!=H[f+56>>2]|(a|0)!=H[f+108>>2]){break t}if((a|0)==H[f+160>>2]){break s}}H[n+20>>2]=1053;H[n+16>>2]=1373;Ha(26080,8179,n+16|0);break o}g=N(H[f+12>>2],H[f+8>>2]);C=O(O(1)/O((-1<<H[f+180>>2]^-1)>>>0));D=O(O(1)/O((-1<<H[f+128>>2]^-1)>>>0));w=O(O(1)/O((-1<<H[f+76>>2]^-1)>>>0));Q=O(O(1)/O((-1<<H[f+24>>2]^-1)>>>0));a=0;while(1){if((a|0)!=(g|0)){h=a<<2;b=h+H[f+148>>2]|0;l=H[b>>2];c=h+H[f+96>>2]|0;j=H[c>>2];m=h+H[f+44>>2]|0;s=O(O(1)-O(C*O(H[h+H[f+200>>2]>>2])));E=O(O(O(O(1)-O(Q*O(H[m>>2])))*O(255))*s);if(O(P(E))<O(2147483648)){h=~~E}else{h=-2147483648}H[m>>2]=h;E=O(O(O(O(1)-O(w*O(j|0)))*O(255))*s);if(O(P(E))<O(2147483648)){h=~~E}else{h=-2147483648}H[c>>2]=h;s=O(O(O(O(1)-O(D*O(l|0)))*O(255))*s);if(O(P(s))<O(2147483648)){c=~~s}else{c=-2147483648}H[b>>2]=c;a=a+1|0;continue}break};Da(H[f+200>>2]);a=H[k+24>>2];H[a+128>>2]=8;H[a+76>>2]=8;H[a+24>>2]=8;H[a+200>>2]=0;H[k+20>>2]=1;a=H[k+16>>2]-1|0;H[k+16>>2]=a;i=3;while(1){if(a>>>0<=i>>>0){break o}a=H[k+24>>2]+N(i,52)|0;B(a,a+52|0,52);i=i+1|0;a=H[k+16>>2];continue};case 0:break p;case 1:break r;default:break o}}g=H[k+24>>2];a=H[g>>2];u:{v:{if((a|0)!=H[g+52>>2]|(a|0)!=H[g+104>>2]){break v}a=H[g+4>>2];if((a|0)!=H[g+56>>2]){break v}if((a|0)==H[g+108>>2]){break u}}H[n+36>>2]=1115;H[n+32>>2]=1373;Ha(26080,8221,n+32|0);break o}a=H[g+24>>2];b=-1<<a^-1;a=1<<a-1;l=H[g+136>>2]?0:a;j=H[g+84>>2]?0:a;m=N(H[g+12>>2],H[g+8>>2]);a=0;while(1){if((a|0)!=(m|0)){c=a<<2;i=c+H[g+44>>2]|0;f=c+H[g+148>>2]|0;s=O(H[f>>2]-l|0);h=c+H[g+96>>2]|0;C=O(H[h>>2]-j|0);D=O(H[i>>2]);w=O(O(O(s*O(1.4019900560379028))+O(O(C*O(-3680000008898787e-20))+D))+O(.5));if(O(P(w))<O(2147483648)){c=~~w}else{c=-2147483648}H[i>>2]=(b|0)<(c|0)?b:(c|0)>0?c:0;w=O(O(O(s*O(-.7141128182411194))+O(O(D*O(1.0003000497817993))+O(C*O(-.34412500262260437))))+O(.5));if(O(P(w))<O(2147483648)){c=~~w}else{c=-2147483648}H[h>>2]=(b|0)<(c|0)?b:(c|0)>0?c:0;s=O(O(O(s*O(-7999999979801942e-21))+O(O(D*O(.9998229742050171))+O(C*O(1.7720400094985962))))+O(.5));if(O(P(s))<O(2147483648)){c=~~s}else{c=-2147483648}H[f>>2]=(b|0)<(c|0)?b:(c|0)>0?c:0;a=a+1|0;continue}break}H[k+20>>2]=1;break o}v=c>>>0>v>>>0?v:c;p=1;break o}w:{x:{c=H[k+24>>2];if(H[c>>2]!=1){break x}y:{switch(H[c+52>>2]-1|0){case 1:if(H[c+104>>2]!=2){break x}if(!(H[c+4>>2]!=1|H[c+56>>2]!=2|H[c+108>>2]!=2)){b=H[c+24>>2];i=H[c+148>>2];a=H[c+96>>2];j=H[c+44>>2];G=H[c+60>>2];q=H[c+8>>2];f=H[c+12>>2];c=N(q,f)<<2;h=Ja(c);g=Ja(c);l=Ja(c);if(!(!h|!g|!l)){m=-1<<b^-1;o=1<<b-1;b=H[k+4>>2]&1;L=f-b|0;K=H[k>>2]&1;x=q-K|0;if(!b){c=l;f=g;b=h;break w}c=l;f=g;b=h;while(1){if((p|0)==(q|0)){break w}La(o,m,H[j>>2],0,0,b,f,c);p=p+1|0;c=c+4|0;f=f+4|0;b=b+4|0;j=j+4|0;continue}}Da(h);Da(g);Da(l);break o}if(H[c+4>>2]!=1|H[c+56>>2]!=1|H[c+108>>2]!=1){break x}a=H[c+24>>2];f=H[c+148>>2];b=H[c+96>>2];i=H[c+44>>2];t=H[c+60>>2];h=H[c+8>>2];x=H[c+12>>2];c=N(h,x)<<2;g=Ja(c);l=Ja(c);m=Ja(c);if(!(!g|!l|!m)){o=-1<<a^-1;r=1<<a-1;z=H[k>>2]&1;a=h-z|0;G=a&1;u=a>>>1|0;J=a&-2;a=m;j=l;c=g;while(1){if((q|0)!=(x|0)){if(z){La(r,o,H[i>>2],0,0,c,j,a);j=j+4|0;c=c+4|0;i=i+4|0;a=a+4|0}h=0;while(1){if(h>>>0<J>>>0){La(r,o,H[i>>2],H[b>>2],H[f>>2],c,j,a);La(r,o,H[i+4>>2],H[b>>2],H[f>>2],c+4|0,j+4|0,a+4|0);h=h+2|0;f=f+4|0;b=b+4|0;a=a+8|0;j=j+8|0;c=c+8|0;i=i+8|0;continue}break}z:{if(!G){break z}h=H[i>>2];A:{if((t|0)==(u|0)){La(r,o,h,0,0,c,j,a);break A}La(r,o,h,H[b>>2],H[f>>2],c,j,a)}a=a+4|0;j=j+4|0;c=c+4|0;i=i+4|0;if(t>>>0<=u>>>0){break z}f=f+4|0;b=b+4|0}q=q+1|0;continue}break}Da(H[H[k+24>>2]+44>>2]);a=H[k+24>>2];H[a+44>>2]=g;Da(H[a+96>>2]);a=H[k+24>>2];H[a+96>>2]=l;Da(H[a+148>>2]);a=H[k+24>>2];H[a+148>>2]=m;b=H[a+8>>2];H[a+112>>2]=b;H[a+60>>2]=b;b=H[a+12>>2];H[a+116>>2]=b;H[a+64>>2]=b;b=H[a>>2];H[a+104>>2]=b;H[a+52>>2]=b;b=H[a+4>>2];H[a+108>>2]=b;H[a+56>>2]=b;H[k+20>>2]=1;break o}Da(g);Da(l);Da(m);break o;case 0:break y;default:break x}}if(H[c+104>>2]!=1|H[c+4>>2]!=1|(H[c+56>>2]!=1|H[c+108>>2]!=1)){break x}b=H[c+24>>2];i=H[c+148>>2];a=H[c+96>>2];j=H[c+44>>2];o=N(H[c+12>>2],H[c+8>>2]);c=o<<2;g=Ja(c);l=Ja(c);m=Ja(c);if(!(!g|!l|!m)){q=-1<<b^-1;r=1<<b-1;c=0;f=m;b=l;h=g;while(1){if((c|0)!=(o|0)){La(r,q,H[j>>2],H[a>>2],H[i>>2],h,b,f);c=c+1|0;f=f+4|0;b=b+4|0;h=h+4|0;i=i+4|0;a=a+4|0;j=j+4|0;continue}break}Da(H[H[k+24>>2]+44>>2]);a=H[k+24>>2];H[a+44>>2]=g;Da(H[a+96>>2]);a=H[k+24>>2];H[a+96>>2]=l;Da(H[a+148>>2]);H[H[k+24>>2]+148>>2]=m;H[k+20>>2]=1;break o}Da(g);Da(l);Da(m);break o}H[n+4>>2]=463;H[n>>2]=1373;Ha(26080,8264,n);break o}J=x>>>1|0;z=x&-2;R=L&-2;u=q<<2;while(1){if(M>>>0<R>>>0){p=c+u|0;r=f+u|0;q=b+u|0;t=j+u|0;if(K){La(o,m,H[j>>2],0,0,b,f,c);La(o,m,H[t>>2],H[a>>2],H[i>>2],q,r,p);p=p+4|0;r=r+4|0;q=q+4|0;t=t+4|0;c=c+4|0;f=f+4|0;j=j+4|0;b=b+4|0}A=0;while(1){if(z>>>0>A>>>0){La(o,m,H[j>>2],H[a>>2],H[i>>2],b,f,c);La(o,m,H[j+4>>2],H[a>>2],H[i>>2],b+4|0,f+4|0,c+4|0);La(o,m,H[t>>2],H[a>>2],H[i>>2],q,r,p);La(o,m,H[t+4>>2],H[a>>2],H[i>>2],q+4|0,r+4|0,p+4|0);A=A+2|0;i=i+4|0;a=a+4|0;p=p+8|0;r=r+8|0;q=q+8|0;t=t+8|0;c=c+8|0;f=f+8|0;b=b+8|0;j=j+8|0;continue}break}B:{if((x|0)==(z|0)){break B}A=H[j>>2];C:{if((G|0)==(J|0)){La(o,m,A,0,0,b,f,c);La(o,m,H[t>>2],0,0,q,r,p);break C}La(o,m,A,H[a>>2],H[i>>2],b,f,c);La(o,m,H[t>>2],H[a>>2],H[i>>2],q,r,p)}c=c+4|0;f=f+4|0;b=b+4|0;j=j+4|0;if(G>>>0<=J>>>0){break B}i=i+4|0;a=a+4|0}M=M+2|0;c=c+u|0;f=f+u|0;b=b+u|0;j=j+u|0;continue}break}D:{if(!(L&1)){break D}if(K){La(o,m,H[j>>2],0,0,b,f,c);c=c+4|0;f=f+4|0;j=j+4|0;b=b+4|0}p=0;while(1){if(p>>>0<z>>>0){La(o,m,H[j>>2],H[a>>2],H[i>>2],b,f,c);La(o,m,H[j+4>>2],H[a>>2],H[i>>2],b+4|0,f+4|0,c+4|0);p=p+2|0;i=i+4|0;a=a+4|0;c=c+8|0;f=f+8|0;b=b+8|0;j=j+8|0;continue}break}if((x|0)==(z|0)){break D}j=H[j>>2];if((G|0)==(J|0)){La(o,m,j,0,0,b,f,c);break D}La(o,m,j,H[a>>2],H[i>>2],b,f,c)}Da(H[H[k+24>>2]+44>>2]);a=H[k+24>>2];H[a+44>>2]=h;Da(H[a+96>>2]);a=H[k+24>>2];H[a+96>>2]=g;Da(H[a+148>>2]);a=H[k+24>>2];H[a+148>>2]=l;b=H[a+8>>2];H[a+112>>2]=b;H[a+60>>2]=b;b=H[a+12>>2];H[a+116>>2]=b;H[a+64>>2]=b;b=H[a>>2];H[a+104>>2]=b;H[a+52>>2]=b;b=H[a+4>>2];H[a+108>>2]=b;H[a+56>>2]=b;H[k+20>>2]=1;p=0}f=H[n+64>>2];E:{if(d){break E}b=0;while(1){if((b|0)==(v|0)){break E}d=H[f+24>>2]+N(b,52)|0;a=H[d+24>>2];if((a|0)!=8){F:{if(a>>>0<=7){h=N(H[d+12>>2],H[d+8>>2]);g=H[d+44>>2];if(H[d+32>>2]){c=1<<a-1;i=0;while(1){if((h|0)==(i|0)){break F}a=g+(i<<2)|0;l=a;a=H[a>>2];j=a>>31<<7|a>>>25;S=l,T=Je(a<<7,j,c,0),H[S>>2]=T;i=i+1|0;continue}}a=-1<<a^-1;i=0;while(1){if((h|0)==(i|0)){break F}c=g+(i<<2)|0;l=Ke(Ie(H[c>>2],0,255),ra,a,0);H[c>>2]=l;i=i+1|0;continue}}a=a-8|0;c=N(H[d+12>>2],H[d+8>>2]);h=H[d+44>>2];i=0;if(H[d+32>>2]){while(1){if((c|0)==(i|0)){break F}g=h+(i<<2)|0;H[g>>2]=H[g>>2]>>a;i=i+1|0;continue}}while(1){if((c|0)==(i|0)){break F}g=h+(i<<2)|0;H[g>>2]=H[g>>2]>>>a;i=i+1|0;continue}}H[d+24>>2]=8}b=b+1|0;continue}}a=H[f+24>>2];b=N(H[a+12>>2],H[a+8>>2]);G:{if(!p){if(H[f+20>>2]==2){if(H[f+16>>2]==1){na(H[a+44>>2],b|0);break G}if(!e){break G}ea(H[a+44>>2],H[a+96>>2],b|0);break G}da(H[a+44>>2],H[a+96>>2],H[a+148>>2],b|0);break G}H:{switch(v-1|0){case 0:ca(H[a+44>>2],b|0);break G;case 2:ba(H[a+44>>2],H[a+96>>2],H[a+148>>2],b|0);break G;case 3:break H;default:break G}}aa(H[a+44>>2],H[a+96>>2],H[a+148>>2],H[a+200>>2],b|0)}Va(H[n+64>>2]);j=0}oa=n+8320|0;return j|0}function mc(a,b,c,d,e,f,g,h,i){var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,z=0,A=0,C=0,D=0,E=0,F=0,G=0,I=0,J=0,L=0,M=0;j=H[a>>2];a:{if(j>>>0<d>>>0|b>>>0>=d>>>0|b>>>0>=j>>>0){break a}j=H[a+4>>2];if(j>>>0<e>>>0|c>>>0>=e>>>0|c>>>0>=j>>>0){break a}C=(c>>>0)/K[a+12>>2]|0;s=H[a+8>>2];F=(b>>>0)/(s>>>0)|0;J=(N(s,F)-b|0)+s|0;x=c;while(1){k=H[a+12>>2];j=k;j=(c|0)==(x|0)?j-((c>>>0)%(j>>>0)|0)|0:j;u=e-x|0;r=j>>>0<u>>>0?j:u;z=r&-4;v=r&3;L=r&-8;G=r&7;w=r-1|0;M=(g|0)==2&(r|0)==1;I=N(k-j|0,s);A=(N(x-c|0,h)<<2)+f|0;D=F;u=b;while(1){j=(b|0)==(u|0)?J:s;k=d-u|0;q=j>>>0<k>>>0?j:k;k=s-j|0;l=D<<2;j=H[l+(H[a+24>>2]+(N(H[a+16>>2],C)<<2)|0)>>2];b:{c:{d:{e:{f:{g:{if(i){h:{i:{j:{k:{if(j){l=((I<<2)+j|0)+(k<<2)|0;j=u-b|0;if((g|0)==1){break h}m=(N(g,j)<<2)+A|0;if((q|0)==1){break i}if(M){break j}if((g|0)!=8|q>>>0<=7){break k}if(!r){break b}o=q&-4;k=0;while(1){j=0;while(1){H[(j<<5)+m>>2]=H[(j<<2)+l>>2];n=j|1;H[(n<<5)+m>>2]=H[(n<<2)+l>>2];n=j|2;H[(n<<5)+m>>2]=H[(n<<2)+l>>2];n=j|3;H[(n<<5)+m>>2]=H[(n<<2)+l>>2];j=j+4|0;if(o>>>0>j>>>0){continue}break}if(j>>>0<q>>>0){while(1){H[(j<<5)+m>>2]=H[(j<<2)+l>>2];j=j+1|0;if((q|0)!=(j|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;k=k+1|0;if((r|0)!=(k|0)){continue}break}break b}if((g|0)!=1){if(!r){break b}p=q&-4;n=q&3;l=(N(u-b|0,g)<<2)+A|0;o=0;while(1){l:{if(!q){break l}m=0;j=0;k=0;if(q>>>0>=4){while(1){H[(N(g,j)<<2)+l>>2]=0;H[(N(j|1,g)<<2)+l>>2]=0;H[(N(j|2,g)<<2)+l>>2]=0;H[(N(j|3,g)<<2)+l>>2]=0;j=j+4|0;k=k+4|0;if((p|0)!=(k|0)){continue}break}}if(!n){break l}while(1){H[(N(g,j)<<2)+l>>2]=0;j=j+1|0;m=m+1|0;if((n|0)!=(m|0)){continue}break}}l=(h<<2)+l|0;o=o+1|0;if((r|0)!=(o|0)){continue}break}break b}if(!r){break b}l=q<<2;k=(u-b<<2)+A|0;o=0;if(w>>>0>=7){break g}break f}if(!r){break b}E=q&-4;p=q&3;n=0;break c}j=0;k=q&-4;if(k){while(1){H[(j<<3)+m>>2]=H[(j<<2)+l>>2];o=j|1;H[(o<<3)+m>>2]=H[(o<<2)+l>>2];o=j|2;H[(o<<3)+m>>2]=H[(o<<2)+l>>2];o=j|3;H[(o<<3)+m>>2]=H[(o<<2)+l>>2];j=j+4|0;if(k>>>0>j>>>0){continue}break}}if(j>>>0>=q>>>0){break b}o=0;k=j;n=q-j&3;if(n){while(1){H[(k<<3)+m>>2]=H[(k<<2)+l>>2];k=k+1|0;o=o+1|0;if((n|0)!=(o|0)){continue}break}}if(j-q>>>0>4294967292){break b}while(1){H[(k<<3)+m>>2]=H[(k<<2)+l>>2];j=k+1|0;H[(j<<3)+m>>2]=H[(j<<2)+l>>2];j=k+2|0;H[(j<<3)+m>>2]=H[(j<<2)+l>>2];j=k+3|0;H[(j<<3)+m>>2]=H[(j<<2)+l>>2];k=k+4|0;if((q|0)!=(k|0)){continue}break}break b}if(!r){break b}k=0;if(w>>>0>=3){while(1){H[m>>2]=H[l>>2];j=h<<2;m=j+m|0;p=l;l=s<<2;o=p+l|0;H[m>>2]=H[o>>2];m=j+m|0;o=l+o|0;H[m>>2]=H[o>>2];m=j+m|0;o=l+o|0;H[m>>2]=H[o>>2];l=l+o|0;m=j+m|0;k=k+4|0;if((z|0)!=(k|0)){continue}break}}j=0;if(!v){break b}while(1){H[m>>2]=H[l>>2];l=(s<<2)+l|0;m=(h<<2)+m|0;j=j+1|0;if((v|0)!=(j|0)){continue}break}break b}j=(j<<2)+A|0;if((q|0)!=4){if(!r){break b}m=q<<2;o=0;if(w>>>0>=3){break e}break d}if(!r){break b}o=0;if(w>>>0>=3){while(1){k=H[l+4>>2];H[j>>2]=H[l>>2];H[j+4>>2]=k;k=H[l+12>>2];H[j+8>>2]=H[l+8>>2];H[j+12>>2]=k;k=l;l=s<<2;k=k+l|0;n=H[k+12>>2];m=h<<2;j=m+j|0;H[j+8>>2]=H[k+8>>2];H[j+12>>2]=n;n=H[k+4>>2];H[j>>2]=H[k>>2];H[j+4>>2]=n;k=l+k|0;n=H[k+12>>2];j=j+m|0;H[j+8>>2]=H[k+8>>2];H[j+12>>2]=n;n=H[k+4>>2];H[j>>2]=H[k>>2];H[j+4>>2]=n;k=l+k|0;n=H[k+12>>2];j=j+m|0;H[j+8>>2]=H[k+8>>2];H[j+12>>2]=n;n=H[k+4>>2];H[j>>2]=H[k>>2];H[j+4>>2]=n;l=l+k|0;j=j+m|0;o=o+4|0;if((z|0)!=(o|0)){continue}break}}m=0;if(!v){break b}while(1){k=H[l+4>>2];H[j>>2]=H[l>>2];H[j+4>>2]=k;k=H[l+12>>2];H[j+8>>2]=H[l+8>>2];H[j+12>>2]=k;l=(s<<2)+l|0;j=(h<<2)+j|0;m=m+1|0;if((v|0)!=(m|0)){continue}break}break b}if(!j){j=Fa(1,N(H[a+8>>2],H[a+12>>2])<<2);if(!j){return 0}H[l+(H[a+24>>2]+(N(H[a+16>>2],C)<<2)|0)>>2]=j}l=((I<<2)+j|0)+(k<<2)|0;j=u-b|0;m:{n:{o:{p:{q:{r:{if((g|0)!=1){m=(N(g,j)<<2)+A|0;if((q|0)==1){break r}if((g|0)!=8|q>>>0<=7){break q}if(!r){break b}o=q&-4;k=0;while(1){j=0;while(1){H[(j<<2)+l>>2]=H[(j<<5)+m>>2];n=j|1;H[(n<<2)+l>>2]=H[(n<<5)+m>>2];n=j|2;H[(n<<2)+l>>2]=H[(n<<5)+m>>2];n=j|3;H[(n<<2)+l>>2]=H[(n<<5)+m>>2];j=j+4|0;if(o>>>0>j>>>0){continue}break}if(j>>>0<q>>>0){while(1){H[(j<<2)+l>>2]=H[(j<<5)+m>>2];j=j+1|0;if((q|0)!=(j|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;k=k+1|0;if((r|0)!=(k|0)){continue}break}break b}j=(j<<2)+A|0;if((q|0)==4){break p}if(!r){break b}m=q<<2;o=0;if(w>>>0>=3){break o}break n}if(!r){break b}o=0;if(w>>>0>=3){while(1){H[l>>2]=H[m>>2];j=s<<2;l=j+l|0;k=h<<2;m=k+m|0;H[l>>2]=H[m>>2];l=j+l|0;m=k+m|0;H[l>>2]=H[m>>2];l=j+l|0;m=k+m|0;H[l>>2]=H[m>>2];l=j+l|0;m=k+m|0;o=o+4|0;if((z|0)!=(o|0)){continue}break}}j=0;if(!v){break b}while(1){H[l>>2]=H[m>>2];l=(s<<2)+l|0;m=(h<<2)+m|0;j=j+1|0;if((v|0)!=(j|0)){continue}break}break b}if(!r){break b}E=q&-4;p=q&3;n=0;break m}if(!r){break b}o=0;if(w>>>0>=3){while(1){k=H[j+4>>2];H[l>>2]=H[j>>2];H[l+4>>2]=k;k=H[j+12>>2];H[l+8>>2]=H[j+8>>2];H[l+12>>2]=k;m=h<<2;j=m+j|0;n=H[j+12>>2];k=l;l=s<<2;k=k+l|0;H[k+8>>2]=H[j+8>>2];H[k+12>>2]=n;n=H[j+4>>2];H[k>>2]=H[j>>2];H[k+4>>2]=n;j=j+m|0;n=H[j+12>>2];k=l+k|0;H[k+8>>2]=H[j+8>>2];H[k+12>>2]=n;n=H[j+4>>2];H[k>>2]=H[j>>2];H[k+4>>2]=n;j=j+m|0;n=H[j+12>>2];k=l+k|0;H[k+8>>2]=H[j+8>>2];H[k+12>>2]=n;n=H[j+4>>2];H[k>>2]=H[j>>2];H[k+4>>2]=n;j=j+m|0;l=l+k|0;o=o+4|0;if((z|0)!=(o|0)){continue}break}}m=0;if(!v){break b}while(1){k=H[j+4>>2];H[l>>2]=H[j>>2];H[l+4>>2]=k;k=H[j+12>>2];H[l+8>>2]=H[j+8>>2];H[l+12>>2]=k;j=(h<<2)+j|0;l=(s<<2)+l|0;m=m+1|0;if((v|0)!=(m|0)){continue}break}break b}while(1){k=!m;if(!k){B(l,j,m)}p=j;j=h<<2;n=p+j|0;p=l;l=s<<2;p=p+l|0;if(!k){B(p,n,m)}n=j+n|0;p=l+p|0;if(!k){B(p,n,m)}n=j+n|0;p=l+p|0;if(!k){B(p,n,m)}j=j+n|0;l=l+p|0;o=o+4|0;if((z|0)!=(o|0)){continue}break}}k=0;if(!v){break b}while(1){if(m){B(l,j,m)}j=(h<<2)+j|0;l=(s<<2)+l|0;k=k+1|0;if((v|0)!=(k|0)){continue}break}break b}while(1){s:{if(!q){break s}k=0;j=0;o=0;if(q>>>0>=4){while(1){H[(j<<2)+l>>2]=H[(N(g,j)<<2)+m>>2];t=j|1;H[(t<<2)+l>>2]=H[(N(g,t)<<2)+m>>2];t=j|2;H[(t<<2)+l>>2]=H[(N(g,t)<<2)+m>>2];t=j|3;H[(t<<2)+l>>2]=H[(N(g,t)<<2)+m>>2];j=j+4|0;o=o+4|0;if((E|0)!=(o|0)){continue}break}}if(!p){break s}while(1){H[(j<<2)+l>>2]=H[(N(g,j)<<2)+m>>2];j=j+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;n=n+1|0;if((r|0)!=(n|0)){continue}break}break b}while(1){j=!l;if(!j){y(k,0,l)}p=k;k=h<<2;m=p+k|0;if(!j){y(m,0,l)}m=k+m|0;if(!j){y(m,0,l)}m=k+m|0;if(!j){y(m,0,l)}m=k+m|0;if(!j){y(m,0,l)}m=k+m|0;if(!j){y(m,0,l)}m=k+m|0;if(!j){y(m,0,l)}m=k+m|0;if(!j){y(m,0,l)}k=k+m|0;o=o+8|0;if((L|0)!=(o|0)){continue}break}}j=0;if(!G){break b}while(1){if(l){y(k,0,l)}k=(h<<2)+k|0;j=j+1|0;if((G|0)!=(j|0)){continue}break}break b}while(1){k=!m;if(!k){B(j,l,m)}p=l;l=s<<2;n=p+l|0;p=j;j=h<<2;p=p+j|0;if(!k){B(p,n,m)}n=l+n|0;p=j+p|0;if(!k){B(p,n,m)}n=l+n|0;p=j+p|0;if(!k){B(p,n,m)}l=l+n|0;j=j+p|0;o=o+4|0;if((z|0)!=(o|0)){continue}break}}k=0;if(!v){break b}while(1){if(m){B(j,l,m)}l=(s<<2)+l|0;j=(h<<2)+j|0;k=k+1|0;if((v|0)!=(k|0)){continue}break}break b}while(1){t:{if(!q){break t}k=0;j=0;o=0;if(q>>>0>=4){while(1){H[(N(g,j)<<2)+m>>2]=H[(j<<2)+l>>2];t=j|1;H[(N(t,g)<<2)+m>>2]=H[(t<<2)+l>>2];t=j|2;H[(N(t,g)<<2)+m>>2]=H[(t<<2)+l>>2];t=j|3;H[(N(t,g)<<2)+m>>2]=H[(t<<2)+l>>2];j=j+4|0;o=o+4|0;if((E|0)!=(o|0)){continue}break}}if(!p){break t}while(1){H[(N(g,j)<<2)+m>>2]=H[(j<<2)+l>>2];j=j+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;n=n+1|0;if((r|0)!=(n|0)){continue}break}}D=D+1|0;u=q+u|0;if(u>>>0<d>>>0){continue}break}C=C+1|0;x=r+x|0;if(x>>>0<e>>>0){continue}break}}return 1}function Qc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;h=oa-240|0;oa=h;r=1;a:{if(H[H[a>>2]+60>>2]|H[a+128>>2]){break a}b:{k=H[a+116>>2];c:{if(!k){d=H[a+120>>2];break c}f=H[b+16>>2];g=J[k+4>>1];d=H[a+120>>2];if(!(!d|!H[d+12>>2])){f=I[d+18|0]}d:{if(g){k=H[k>>2];while(1){i=k+N(e,6)|0;j=J[i>>1];if(j>>>0>=f>>>0){H[h+180>>2]=f;H[h+176>>2]=j;Ca(c,1,13715,h+176|0);r=0;break a}e:{i=J[i+4>>1];if(!i|(i|0)==65535){break e}i=i-1|0;if(i>>>0<f>>>0){break e}H[h+164>>2]=f;H[h+160>>2]=i;Ca(c,1,13715,h+160|0);r=0;break a}e=e+1|0;if((g|0)!=(e|0)){continue}break}break d}if(f){break b}break c}while(1){f=f-1|0;e=0;while(1){if(J[k+N(e,6)>>1]!=(f|0)){e=e+1|0;if((g|0)!=(e|0)){continue}break b}break}if(f){continue}break}}f:{if(!d){break f}k=H[d+12>>2];if(!k){break f}g:{d=I[d+18|0];h:{if(d){e=0;j=1;while(1){g=H[b+16>>2];f=J[k+(e<<2)>>1];if(g>>>0<=f>>>0){H[h+148>>2]=g;H[h+144>>2]=f;Ca(c,1,13715,h+144|0);j=0}e=e+1|0;if((d|0)!=(e|0)){continue}break}g=Fa(d,4);if(!g){break h}e=0;while(1){f=k+(e<<2)|0;i=I[f+2|0];i:{if(i>>>0>=2){H[h+68>>2]=i;H[h+64>>2]=e;Ca(c,1,12094,h- -64|0);j=0;break i}f=I[f+3|0];if(f>>>0>=d>>>0){H[h+128>>2]=f;Ca(c,1,12038,h+128|0);j=0;break i}l=(i|0)!=1;m=(f<<2)+g|0;if(!(l|!H[m>>2])){H[h+80>>2]=f;Ca(c,1,11527,h+80|0);j=0;break i}if(!(i|!f)){H[h+100>>2]=f;H[h+96>>2]=e;Ca(c,1,11901,h+96|0);j=0;break i}if(!(l|(e|0)==(f|0))){H[h+120>>2]=f;H[h+116>>2]=e;H[h+112>>2]=e;Ca(c,1,11937,h+112|0);j=0;break i}H[m>>2]=1}e=e+1|0;if((d|0)!=(e|0)){continue}break}j=!j;e=0;while(1){j:{f=e<<2;if(I[(f+k|0)+2|0]?H[f+g>>2]:1){e=e+1|0;if((d|0)!=(e|0)){continue}if(j&1){break j}if(H[b+16>>2]!=1){break g}e=0;while(1){if(H[(e<<2)+g>>2]){e=e+1|0;if((d|0)!=(e|0)){continue}break g}break}i=0;Ca(c,2,9253,0);e=0;if(d>>>0>=4){j=d&252;f=0;while(1){m=k+(e<<2)|0;F[m+3|0]=e;F[m+2|0]=1;m=e|1;l=k+(m<<2)|0;F[l+3|0]=m;F[l+2|0]=1;m=e|2;l=k+(m<<2)|0;F[l+3|0]=m;F[l+2|0]=1;m=e|3;l=k+(m<<2)|0;F[l+3|0]=m;F[l+2|0]=1;e=e+4|0;f=f+4|0;if((j|0)!=(f|0)){continue}break}}d=d&3;if(!d){break g}while(1){f=k+(e<<2)|0;F[f+3|0]=e;F[f+2|0]=1;e=e+1|0;i=i+1|0;if((d|0)!=(i|0)){continue}break}break g}H[h+48>>2]=e;j=1;Ca(c,1,11101,h+48|0);e=e+1|0;if((d|0)!=(e|0)){continue}}break}Da(g);r=0;break a}g=Fa(d,4);if(g){break g}}r=0;Ca(c,1,12285,0);break a}Da(g)}d=H[a+120>>2];k:{if(!d){break k}t=H[d+12>>2];if(!t){Da(H[d+4>>2]);Da(H[H[a+120>>2]+8>>2]);Da(H[H[a+120>>2]>>2]);d=H[a+120>>2];g=H[d+12>>2];if(g){Da(g);d=H[a+120>>2]}Da(d);H[a+120>>2]=0;break k}m=H[b+24>>2];l:{k=I[d+18|0];m:{if(k){v=H[d>>2];j=H[d+4>>2];l=H[d+8>>2];e=0;n:{while(1){if(H[(m+N(J[t+(e<<2)>>1],52)|0)+44>>2]){e=e+1|0;if((k|0)!=(e|0)){continue}break n}break}H[h+32>>2]=e;Ca(c,1,13877,h+32|0);r=0;break a}g=Ga(N(k,52));if(!g){break m}i=0;while(1){d=t+(i<<2)|0;e=J[d>>1];f=N(I[d+2|0]?I[d+3|0]:i,52)+g|0;d=m+N(e,52)|0;e=H[d+4>>2];H[f>>2]=H[d>>2];H[f+4>>2]=e;H[f+48>>2]=H[d+48>>2];e=H[d+44>>2];H[f+40>>2]=H[d+40>>2];H[f+44>>2]=e;e=H[d+36>>2];H[f+32>>2]=H[d+32>>2];H[f+36>>2]=e;e=H[d+28>>2];H[f+24>>2]=H[d+24>>2];H[f+28>>2]=e;e=H[d+20>>2];H[f+16>>2]=H[d+16>>2];H[f+20>>2]=e;e=H[d+12>>2];H[f+8>>2]=H[d+8>>2];H[f+12>>2]=e;f=N(i,52)+g|0;d=Ja(N(H[d+8>>2],H[d+12>>2])<<2);H[f+44>>2]=d;if(!d){if(i){a=i&65535;while(1){Da(H[(N(a,52)+g|0)-8>>2]);a=a-1|0;if(a){continue}break}}Da(g);r=0;Ca(c,1,13825,0);break a}H[f+24>>2]=I[i+l|0];H[f+32>>2]=I[i+j|0];i=i+1|0;if((k|0)!=(i|0)){continue}break}u=J[H[a+120>>2]+16>>1];n=u-1|0;while(1){d=N(o,52)+g|0;i=N(H[d+12>>2],H[d+8>>2]);f=t+(o<<2)|0;e=H[(m+N(J[f>>1],52)|0)+44>>2];o:{if(!I[f+2|0]){if(!i){break o}l=H[d+44>>2];j=0;f=0;if(i>>>0>=4){q=i&-4;d=0;while(1){p=f<<2;H[p+l>>2]=H[e+p>>2];s=p|4;H[s+l>>2]=H[e+s>>2];s=p|8;H[s+l>>2]=H[e+s>>2];p=p|12;H[p+l>>2]=H[e+p>>2];f=f+4|0;d=d+4|0;if((q|0)!=(d|0)){continue}break}}d=i&3;if(!d){break o}while(1){i=f<<2;H[i+l>>2]=H[e+i>>2];f=f+1|0;j=j+1|0;if((d|0)!=(j|0)){continue}break}break o}if(!i){break o}d=I[f+3|0];j=(d<<2)+v|0;l=H[(N(d,52)+g|0)+44>>2];f=0;if((i|0)!=1){s=i&-2;d=0;while(1){q=f<<2;p=H[q+e>>2];H[l+q>>2]=H[j+(N(k,(p|0)>=0?(p|0)<(u|0)?p:n:0)<<2)>>2];q=q|4;p=H[q+e>>2];H[l+q>>2]=H[j+(N(k,(p|0)>=0?(p|0)<(u|0)?p:n:0)<<2)>>2];f=f+2|0;d=d+2|0;if((s|0)!=(d|0)){continue}break}}if(!(i&1)){break o}f=f<<2;d=H[f+e>>2];H[f+l>>2]=H[j+(N(k,(d|0)>=0?(d|0)<(u|0)?d:n:0)<<2)>>2]}o=o+1|0;if((k|0)!=(o|0)){continue}break}break l}g=Ga(N(k,52));if(g){break l}}r=0;Ca(c,1,13825,0);break a}d=H[b+16>>2];if(d){e=0;while(1){f=H[(m+N(e,52)|0)+44>>2];if(f){Da(f)}e=e+1|0;if((d|0)!=(e|0)){continue}break}}Da(m);H[b+16>>2]=k;H[b+24>>2]=g}e=H[a+116>>2];if(!e){break a}j=H[e>>2];l=J[e+4>>1];if(l){t=j+6|0;e=0;u=l-2&65535;i=1;while(1){d=H[b+16>>2];p=N(e,6)+j|0;f=J[p>>1];p:{if(d>>>0<=f>>>0){H[h+20>>2]=d;H[h+16>>2]=f;Ca(c,2,7334,h+16|0);break p}g=J[p+4>>1];if((g+1&65535)>>>0<=1){G[(H[b+24>>2]+N(f,52)|0)+48>>1]=J[p+2>>1];break p}k=g-1|0;m=k&65535;if(m>>>0>=d>>>0){H[h+4>>2]=d;H[h>>2]=m;Ca(c,2,7293,h);break p}q:{if(J[p+2>>1]|(f|0)==(m|0)){break q}g=H[b+24>>2];d=g+N(f,52)|0;H[h+232>>2]=H[d+48>>2];n=H[d+44>>2];H[h+224>>2]=H[d+40>>2];H[h+228>>2]=n;n=H[d+36>>2];H[h+216>>2]=H[d+32>>2];H[h+220>>2]=n;n=H[d+28>>2];H[h+208>>2]=H[d+24>>2];H[h+212>>2]=n;n=H[d+20>>2];H[h+200>>2]=H[d+16>>2];H[h+204>>2]=n;n=H[d+12>>2];H[h+192>>2]=H[d+8>>2];H[h+196>>2]=n;n=H[d+4>>2];H[h+184>>2]=H[d>>2];H[h+188>>2]=n;n=N(m,52);g=n+g|0;H[d+48>>2]=H[g+48>>2];o=H[g+44>>2];H[d+40>>2]=H[g+40>>2];H[d+44>>2]=o;o=H[g+36>>2];H[d+32>>2]=H[g+32>>2];H[d+36>>2]=o;o=H[g+28>>2];H[d+24>>2]=H[g+24>>2];H[d+28>>2]=o;o=H[g+20>>2];H[d+16>>2]=H[g+16>>2];H[d+20>>2]=o;o=H[g+12>>2];H[d+8>>2]=H[g+8>>2];H[d+12>>2]=o;o=H[g+4>>2];H[d>>2]=H[g>>2];H[d+4>>2]=o;g=H[h+188>>2];d=n+H[b+24>>2]|0;H[d>>2]=H[h+184>>2];H[d+4>>2]=g;H[d+48>>2]=H[h+232>>2];g=H[h+228>>2];H[d+40>>2]=H[h+224>>2];H[d+44>>2]=g;g=H[h+220>>2];H[d+32>>2]=H[h+216>>2];H[d+36>>2]=g;g=H[h+212>>2];H[d+24>>2]=H[h+208>>2];H[d+28>>2]=g;g=H[h+204>>2];H[d+16>>2]=H[h+200>>2];H[d+20>>2]=g;g=H[h+196>>2];H[d+8>>2]=H[h+192>>2];H[d+12>>2]=g;if(l>>>0<=e+1>>>0){break q}g=i;if(!(e-l&1)){g=k;d=N(i,6)+j|0;n=J[d>>1];r:{if((n|0)!=(f|0)){g=f;if((n|0)!=(m|0)){break r}}G[d>>1]=g}g=i+1|0}if((u|0)==(e&65535)){break q}while(1){d=k;n=N(g,6);o=n+j|0;q=J[o>>1];s:{if((q|0)!=(f|0)){d=f;if((m|0)!=(q|0)){break s}}G[o>>1]=d}d=k;n=n+t|0;o=J[n>>1];t:{if((o|0)!=(f|0)){d=f;if((m|0)!=(o|0)){break t}}G[n>>1]=d}g=g+2|0;if((l|0)!=(g&65535)){continue}break}}G[(H[b+24>>2]+N(f,52)|0)+48>>1]=J[p+2>>1]}i=i+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}e=H[a+116>>2];j=H[e>>2]}if(j){Da(j);e=H[a+116>>2]}Da(e);H[a+116>>2]=0;break a}r=0;Ca(c,1,9499,0)}oa=h+240|0;return r}function $c(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=O(0),p=0;k=oa-48|0;oa=k;H[a+8>>2]=1;a:{b:{d=k+40|0;c:{if((Ka(b,d,2,c)|0)!=2){break c}Ea(d,k+44|0,2);if(H[k+44>>2]!=65359){break c}H[a+8>>2]=2;d=H[b+56>>2];e=d-2|0;d=H[b+60>>2]-(d>>>0<2)|0;g=H[a+224>>2];H[g>>2]=e;H[g+4>>2]=d;H[k+16>>2]=e;H[k+20>>2]=d;Ca(c,4,12769,k+16|0);f=H[a+224>>2];j=H[f>>2];e=H[f+24>>2];d=e+1|0;g=H[f+32>>2];if(d>>>0<=g>>>0){g=H[f+28>>2];break b}o=O(O(g>>>0)+O(100));if(o<O(4294967296)&o>=O(0)){d=~~o>>>0}else{d=0}H[f+32>>2]=d;g=Ia(H[f+28>>2],N(d,24));if(g){H[f+28>>2]=g;e=H[f+24>>2];d=e+1|0;break b}Da(H[f+28>>2]);H[f+32>>2]=0;H[f+24>>2]=0;H[f+28>>2]=0;Ca(c,1,3899,0)}Ca(c,1,15656,0);a=0;break a}e=N(e,24)+g|0;H[e+16>>2]=2;H[e+8>>2]=j;H[e+12>>2]=j>>31;G[e>>1]=65359;H[f+24>>2]=d;if((Ka(b,H[a+16>>2],2,c)|0)!=2){Ca(c,1,2472,0);a=0;break a}Ea(H[a+16>>2],k+40|0,2);d:{e:{g=H[k+40>>2];if((g|0)!=65424){while(1){e=24912;if(g>>>0<=65279){H[k>>2]=g;Ca(c,1,2268,k);a=0;break a}while(1){d=e;f=H[d>>2];if(f){e=d+12|0;if((f|0)!=(g|0)){continue}}break}f:{g:{if(f){break g}h=2;Ca(c,2,3847,0);e=2472;h:{i:{if((Ka(b,H[a+16>>2],2,c)|0)!=2){break i}while(1){Ea(H[a+16>>2],k+44|0,2);f=24912;g=H[k+44>>2];if(g>>>0>=65280){while(1){d=f;i=H[d>>2];if(i){f=d+12|0;if((g|0)!=(i|0)){continue}}break}if(!(H[d+4>>2]&H[a+8>>2])){e=5397;break i}if(i){if((i|0)==65424){H[k+40>>2]=65424;break f}j=H[b+56>>2];f=H[a+224>>2];g=H[f+24>>2];e=g+1|0;d=H[f+32>>2];if(e>>>0<=d>>>0){d=H[f+28>>2];break h}o=O(O(d>>>0)+O(100));if(o<O(4294967296)&o>=O(0)){d=~~o>>>0}else{d=0}H[f+32>>2]=d;d=Ia(H[f+28>>2],N(d,24));if(d){H[f+28>>2]=d;g=H[f+24>>2];e=g+1|0;break h}Da(H[f+28>>2]);H[f+32>>2]=0;H[f+24>>2]=0;H[f+28>>2]=0;e=3899;break i}h=h+2|0}if((Ka(b,H[a+16>>2],2,c)|0)==2){continue}break}}Ca(c,1,e,0);Ca(c,1,9847,0);a=0;break a}d=N(g,24)+d|0;H[d+16>>2]=h;g=j-h|0;H[d+8>>2]=g;H[d+12>>2]=g>>31;G[d>>1]=0;H[f+24>>2]=e;H[k+40>>2]=i;g=24912;if((i|0)==65424){break f}while(1){d=g;f=H[d>>2];if(!f){break g}g=d+12|0;if((f|0)!=(i|0)){continue}break}}if(!(H[d+4>>2]&H[a+8>>2])){Ca(c,1,5397,0);a=0;break a}if((Ka(b,H[a+16>>2],2,c)|0)!=2){Ca(c,1,2472,0);a=0;break a}Ea(H[a+16>>2],k+36|0,2);e=H[k+36>>2];if(e>>>0<=1){Ca(c,1,6074,0);a=0;break a}e=e-2|0;H[k+36>>2]=e;g=H[a+16>>2];if(K[a+20>>2]<e>>>0){g=Ia(g,e);if(!g){Da(H[a+16>>2]);H[a+16>>2]=0;H[a+20>>2]=0;Ca(c,1,4973,0);a=0;break a}H[a+16>>2]=g;e=H[k+36>>2];H[a+20>>2]=e}e=Ka(b,g,e,c);if((e|0)!=H[k+36>>2]){Ca(c,1,2472,0);a=0;break a}if(!(sa[H[d+8>>2]](a,H[a+16>>2],e,c)|0)){Ca(c,1,2490,0);a=0;break a}j=H[b+56>>2];i=H[k+36>>2];d=H[a+224>>2];g=H[d+24>>2];h=g+1|0;e=H[d+32>>2];j:{if(h>>>0<=e>>>0){e=H[d+28>>2];break j}o=O(O(e>>>0)+O(100));if(o<O(4294967296)&o>=O(0)){e=~~o>>>0}else{e=0}H[d+32>>2]=e;e=Ia(H[d+28>>2],N(e,24));if(!e){break d}H[d+28>>2]=e;g=H[d+24>>2];h=g+1|0}e=N(g,24)+e|0;H[e+16>>2]=i+4;g=(j-i|0)-4|0;H[e+8>>2]=g;H[e+12>>2]=g>>31;G[e>>1]=f;H[d+24>>2]=h;if((Ka(b,H[a+16>>2],2,c)|0)!=2){Ca(c,1,2472,0);a=0;break a}m=(f|0)==65372?1:m;l=(f|0)==65362?1:l;n=(f|0)==65361?1:n;Ea(H[a+16>>2],k+40|0,2);g=H[k+40>>2];if((g|0)!=65424){continue}}break}if(n){break e}}Ca(c,1,4785,0);a=0;break a}if(!l){Ca(c,1,4831,0);a=0;break a}if(!m){Ca(c,1,4877,0);a=0;break a}d=0;e=0;h=0;j=oa-16|0;oa=j;m=1;k:{if(!(F[a+212|0]&1)){break k}l:{f=H[a+136>>2];if(!f){break l}m:{while(1){g=H[a+140>>2]+(h<<3)|0;l=H[g>>2];if(l){i=H[g+4>>2];g=d-i|0;g=d>>>0>=g>>>0?g:0;if(d>>>0<i>>>0){f=i-d|0;l=d+l|0;while(1){if(f>>>0<4){d=5671;break m}Ea(l,j+12|0,4);d=H[j+12>>2];if((d^-1)>>>0<e>>>0){d=5645;break m}i=f-4|0;n=i>>>0<d>>>0;g=n?d-i|0:g;e=d+e|0;f=i-d|0;l=((n?0:d)+l|0)+4|0;if(d>>>0<i>>>0){continue}break}f=H[a+136>>2]}d=g}h=h+1|0;if(h>>>0<f>>>0){continue}break}if(!d){break l}m=0;Ca(c,1,3067,0);break k}m=0;Ca(c,1,d,0);break k}d=Ga(e);H[a+160>>2]=d;if(!d){m=0;Ca(c,1,4337,0);break k}H[a+148>>2]=e;h=H[a+140>>2];n:{f=H[a+136>>2];if(f){e=0;d=0;g=0;while(1){l=g<<3;n=l+h|0;i=H[n>>2];if(i){h=H[a+160>>2]+d|0;f=H[n+4>>2];o:{if(f>>>0<=e>>>0){if(f){B(h,i,f)}d=d+f|0;e=e-f|0;break o}if(e){B(h,i,e)}d=d+e|0;h=f-e|0;e=e+i|0;while(1){if(h>>>0<4){break n}Ea(e,j+8|0,4);e=e+4|0;i=H[a+160>>2]+d|0;f=h-4|0;h=H[j+8>>2];if(f>>>0<h>>>0){if(f){B(i,e,f)}d=d+f|0;e=H[j+8>>2]-f|0;break o}if(h){B(i,e,h)}h=H[j+8>>2];d=h+d|0;e=e+h|0;h=f-h|0;if(h){continue}break}e=0}Da(H[l+H[a+140>>2]>>2]);h=H[a+140>>2];f=l+h|0;H[f>>2]=0;H[f+4>>2]=0;f=H[a+136>>2]}g=g+1|0;if(g>>>0<f>>>0){continue}break}e=H[a+148>>2];d=H[a+160>>2]}H[a+168>>2]=e;H[a+144>>2]=d;H[a+136>>2]=0;Da(h);H[a+140>>2]=0;break k}m=0;Ca(c,1,5671,0)}oa=j+16|0;if(!m){Ca(c,1,8085,0);a=0;break a}Ca(c,4,11754,0);d=H[a+224>>2];e=H[b+56>>2];e=e-2|0;H[d+8>>2]=e;H[d+12>>2]=0;b=0;h=0;i=oa-16|0;oa=i;g=H[a+68>>2];p:{if(!g){H[a+76>>2]=1;break p}if(H[a+76>>2]){break p}d=H[a+72>>2];j=H[a+224>>2];e=H[j+40>>2];if((g|0)!=1){m=d+8|0;l=g&-2;while(1){n=b<<3;p=J[n+d>>1];f=e+N(p,40)|0;H[f>>2]=p;H[f+8>>2]=H[f+8>>2]+1;n=J[m+n>>1];f=e+N(n,40)|0;H[f>>2]=n;H[f+8>>2]=H[f+8>>2]+1;b=b+2|0;h=h+2|0;if((l|0)!=(h|0)){continue}break}}if(g&1){f=J[(b<<3)+d>>1];b=e+N(f,40)|0;H[b>>2]=f;H[b+8>>2]=H[b+8>>2]+1}f=H[j+36>>2];q:{if(f){b=0;while(1){if(!H[(e+N(b,40)|0)+8>>2]){H[i>>2]=b;Ca(c,1,9304,i);break q}b=b+1|0;if((f|0)!=(b|0)){continue}break}}f=H[j+8>>2];b=H[j+12>>2];e=0;while(1){r:{l=e<<3;m=H[H[a+224>>2]+40>>2]+N(J[l+d>>1],40)|0;h=H[m+16>>2];if(!h){h=Fa(H[m+8>>2],24);H[m+16>>2]=h;if(!h){break r}g=H[a+68>>2];d=H[a+72>>2]}p=h;h=H[m+4>>2];j=p+N(h,24)|0;H[j>>2]=f;H[j+4>>2]=b;l=H[(d+l|0)+4>>2];f=l+f|0;H[j+16>>2]=f;b=f>>>0<l>>>0?b+1|0:b;H[j+20>>2]=b;H[m+4>>2]=h+1;e=e+1|0;if(g>>>0>e>>>0){continue}break p}break}Ca(c,1,6882,0)}H[a+76>>2]=1;if(!H[a+68>>2]){break p}d=H[H[a+224>>2]+40>>2];b=0;while(1){c=N(J[H[a+72>>2]+(b<<3)>>1],40);d=c+d|0;H[d+8>>2]=0;Da(H[d+16>>2]);d=H[H[a+224>>2]+40>>2];H[(c+d|0)+16>>2]=0;b=b+1|0;if(b>>>0<K[a+68>>2]){continue}break}}oa=i+16|0;H[a+8>>2]=8;a=1;break a}Da(H[d+28>>2]);H[d+32>>2]=0;H[d+24>>2]=0;H[d+28>>2]=0;Ca(c,1,3899,0);a=0}oa=k+48|0;return a|0}function ve(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;f=oa-160|0;oa=f;a:{if(c>>>0<=35){c=0;Ca(d,1,6095,0);break a}c=c-36|0;h=(c>>>0)/3|0;if((N(h,3)|0)!=(c|0)){c=0;Ca(d,1,6095,0);break a}j=H[a+96>>2];c=f+156|0;Ea(b,c,2);G[a+104>>1]=H[f+156>>2];Ea(b+2|0,j+8|0,4);Ea(b+6|0,j+12|0,4);Ea(b+10|0,j,4);Ea(b+14|0,j+4|0,4);Ea(b+18|0,a+116|0,4);Ea(b+22|0,a+120|0,4);Ea(b+26|0,a+108|0,4);Ea(b+30|0,a+112|0,4);Ea(b+34|0,c,2);b:{c:{d:{c=H[f+156>>2];if(c>>>0<=16384){H[j+16>>2]=c;if((c|0)!=(h|0)){H[f+132>>2]=h;H[f+128>>2]=c;Ca(d,1,14980,f+128|0);c=0;break a}c=H[j+4>>2];g=H[j+12>>2];l=H[j+8>>2];e=H[j>>2];if(!(c>>>0<g>>>0&l>>>0>e>>>0)){H[f+120>>2]=g-c;H[f+124>>2]=0-(c>>>0>g>>>0);H[f+112>>2]=l-e;H[f+116>>2]=0-(e>>>0>l>>>0);Ca(d,1,14542,f+112|0);c=0;break a}i=H[a+116>>2];k=H[a+120>>2];if(!(k?i:0)){H[f+4>>2]=k;H[f>>2]=i;Ca(d,1,15094,f);c=0;break a}e:{n=H[a+108>>2];f:{if(n>>>0>e>>>0){break f}i=i+n|0;if(e>>>0>=(i>>>0<n>>>0?-1:i)>>>0){break f}i=H[a+112>>2];if(i>>>0>c>>>0){break f}k=i+k|0;if(c>>>0<(i>>>0>k>>>0?-1:k)>>>0){break e}}c=0;Ca(d,1,2792,0);break a}g:{if(H[a+248>>2]){break g}i=H[a+240>>2];if(!i){break g}k=H[a+244>>2];if(!k){break g}e=l-e|0;c=g-c|0;if((e|0)==(i|0)&(c|0)==(k|0)){break g}H[f+108>>2]=c;H[f+104>>2]=e;H[f+100>>2]=k;H[f+96>>2]=i;Ca(d,1,14006,f+96|0);c=0;break a}e=Fa(h,52);H[j+24>>2]=e;if(!e){break d}h:{if(!H[j+16>>2]){break h}c=f+152|0;Ea(b+36|0,c,1);h=H[f+152>>2];k=h>>>7|0;H[e+32>>2]=k;n=(h&127)+1|0;H[e+24>>2]=n;l=H[a+248>>2];Ea(b+37|0,c,1);H[e>>2]=H[f+152>>2];Ea(b+38|0,c,1);g=H[f+152>>2];H[e+4>>2]=g;c=0;i=H[e>>2];if(i-256>>>0<4294967041){h=0;break b}h=0;if(g-256>>>0<4294967041){break b}g=H[e+24>>2];if(g>>>0>31){break c}H[e+36>>2]=0;H[e+40>>2]=H[a+184>>2];h=1;if(K[j+16>>2]<=1){break h}k=l?0:k;l=l?0:n;b=b+39|0;while(1){Ea(b,f+152|0,1);i=H[f+152>>2];g=i>>>7|0;H[e+84>>2]=g;i=(i&127)+1|0;H[e+76>>2]=i;if(!(H[a+248>>2]|(I[a+212|0]&4|(i|0)==(l|0)&(g|0)==(k|0)))){H[f+84>>2]=g;H[f+80>>2]=i;H[f+76>>2]=h;H[f+72>>2]=k;H[f+68>>2]=l;H[f+64>>2]=h;Ca(d,2,14778,f- -64|0)}g=f+152|0;Ea(b+1|0,g,1);H[e+52>>2]=H[f+152>>2];Ea(b+2|0,g,1);g=H[f+152>>2];H[e+56>>2]=g;i=H[e+52>>2];if(i-256>>>0<4294967041|g-256>>>0<=4294967040){break b}g=H[e+76>>2];if(g>>>0>=32){break c}b=b+3|0;H[e+88>>2]=0;H[e+92>>2]=H[a+184>>2];e=e+52|0;h=h+1|0;if(h>>>0<K[j+16>>2]){continue}break}}c=0;h=H[a+116>>2];if(!h){break a}g=H[a+120>>2];if(!g){break a}l=0-!h|0;e=l;p=H[a+108>>2];k=H[j+8>>2]-p|0;i=h-1|0;b=k+i|0;e=k>>>0>b>>>0?e+1|0:e;b=Ke(b,e,h,0);H[a+128>>2]=b;n=0-!g|0;e=n;q=H[a+112>>2];o=H[j+12>>2]-q|0;m=o;k=g-1|0;o=o+k|0;e=m>>>0>o>>>0?e+1|0:e;e=Ke(o,e,g,0);H[a+132>>2]=e;i:{if(!(!b|!e)){if(b>>>0<=65535/(e>>>0)>>>0){break i}}H[f+20>>2]=e;H[f+16>>2]=b;Ca(d,1,14120,f+16|0);break a}o=N(b,e);j:{if(I[a+92|0]&2){H[a+28>>2]=(H[a+28>>2]-p>>>0)/(h>>>0);H[a+32>>2]=(H[a+32>>2]-q>>>0)/(g>>>0);e=l;b=H[a+36>>2]-p|0;m=b;b=b+i|0;e=m>>>0>b>>>0?e+1|0:e;v=a,w=Ke(b,e,h,0),H[v+36>>2]=w;e=n;b=H[a+40>>2]-q|0;m=b;b=b+k|0;e=m>>>0>b>>>0?e+1|0:e;v=a,w=Ke(b,e,g,0),H[v+40>>2]=w;break j}H[a+40>>2]=e;H[a+36>>2]=b;H[a+28>>2]=0;H[a+32>>2]=0}b=Fa(o,5644);H[a+180>>2]=b;if(!b){Ca(d,1,3935,0);break a}b=Fa(H[j+16>>2],1080);H[H[a+12>>2]+5584>>2]=b;if(!H[H[a+12>>2]+5584>>2]){Ca(d,1,3935,0);break a}b=Fa(10,20);H[H[a+12>>2]+5616>>2]=b;b=H[a+12>>2];if(!H[b+5616>>2]){Ca(d,1,3935,0);break a}H[b+5624>>2]=10;b=Fa(10,20);H[H[a+12>>2]+5628>>2]=b;b=H[a+12>>2];if(!H[b+5628>>2]){Ca(d,1,3935,0);break a}H[b+5636>>2]=10;h=H[j+16>>2];k:{if(!h){break k}g=H[j+24>>2];b=0;if((h|0)!=1){l=h&-2;e=0;while(1){i=g+N(b,52)|0;if(!H[i+32>>2]){H[(H[H[a+12>>2]+5584>>2]+N(b,1080)|0)+1076>>2]=1<<H[i+24>>2]-1}i=b|1;k=g+N(i,52)|0;if(!H[k+32>>2]){H[(H[H[a+12>>2]+5584>>2]+N(i,1080)|0)+1076>>2]=1<<H[k+24>>2]-1}b=b+2|0;e=e+2|0;if((l|0)!=(e|0)){continue}break}}if(!(h&1)){break k}e=g+N(b,52)|0;if(H[e+32>>2]){break k}H[(H[H[a+12>>2]+5584>>2]+N(b,1080)|0)+1076>>2]=1<<H[e+24>>2]-1}if(o){b=H[a+180>>2];e=0;while(1){h=Fa(H[j+16>>2],1080);H[b+5584>>2]=h;if(!h){Ca(d,1,3935,0);break a}b=b+5644|0;e=e+1|0;if(o>>>0>e>>>0){continue}break}}b=N(H[a+132>>2],H[a+128>>2]);H[H[a+224>>2]+36>>2]=b;b=Fa(b,40);d=H[a+224>>2];H[d+40>>2]=b;e=0;l:{if(!b){break l}e=1;if(!H[d+36>>2]){break l}d=0;while(1){m:{e=0;g=N(d,40);b=g+b|0;H[b+20>>2]=0;H[b+28>>2]=100;h=Fa(100,24);l=H[a+224>>2];b=H[l+40>>2];H[(g+b|0)+24>>2]=h;if(!h){break m}e=1;d=d+1|0;if(d>>>0<K[l+36>>2]){continue}}break}}if(!e){break a}H[a+8>>2]=4;r=H[j+16>>2];if(r){b=H[a+112>>2];d=H[a+120>>2];c=b+N(d,H[a+132>>2]-1|0)|0;d=c+d|0;c=c>>>0>d>>>0?-1:d;d=H[j+12>>2];c=c>>>0<d>>>0?c:d;l=c-1|0;k=0-!c|0;c=H[a+108>>2];d=H[a+116>>2];a=c+N(d,H[a+128>>2]-1|0)|0;d=a+d|0;a=a>>>0>d>>>0?-1:d;d=H[j+8>>2];a=a>>>0<d>>>0?a:d;i=a-1|0;n=0-!a|0;a=H[j+4>>2];b=a>>>0<b>>>0?b:a;o=b-1|0;p=0-!b|0;a=H[j>>2];b=a>>>0<c>>>0?c:a;q=b-1|0;u=0-!b|0;a=H[j+24>>2];b=0;while(1){e=p;d=H[a+4>>2];c=d+o|0;j=Ke(c,c>>>0<d>>>0?e+1|0:e,d,0);H[a+20>>2]=j;e=u;h=H[a>>2];c=h+q|0;s=Ke(c,c>>>0<h>>>0?e+1|0:e,h,0);H[a+16>>2]=s;c=H[a+40>>2];g=c&31;if((c&63)>>>0>=32){e=-1<<g;m=0}else{m=-1<<g;e=m|(1<<g)-1&-1>>>32-g}g=m^-1;e=e^-1;m=e;e=k;t=d+l|0;e=t>>>0<l>>>0?e+1|0:e;e=Ke(t,e,d,0)-j|0;d=m;j=e;e=e+g|0;d=j>>>0>e>>>0?d+1|0:d;j=e;e=c&31;if((c&63)>>>0>=32){d=d>>>e|0}else{d=((1<<e)-1&d)<<32-e|j>>>e}H[a+12>>2]=d;e=n;d=h+i|0;e=d>>>0<i>>>0?e+1|0:e;d=Ke(d,e,h,0)-s|0;e=m;d=d+g|0;e=d>>>0<g>>>0?e+1|0:e;h=d;d=c&31;if((c&63)>>>0>=32){c=e>>>d|0}else{c=((1<<d)-1&e)<<32-d|h>>>d}H[a+8>>2]=c;a=a+52|0;b=b+1|0;if((r|0)!=(b|0)){continue}break}}c=1;break a}H[f+144>>2]=c;Ca(d,1,7932,f+144|0);c=0;break a}c=0;H[j+16>>2]=0;Ca(d,1,3935,0);break a}H[f+52>>2]=g;H[f+48>>2]=h;Ca(d,1,15402,f+48|0);break a}H[f+40>>2]=g;H[f+36>>2]=i;H[f+32>>2]=h;Ca(d,1,14340,f+32|0)}oa=f+160|0;return c|0}function Fc(a,b,c,d,e,f,g){var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0;j=oa+-64|0;oa=j;H[j+60>>2]=b;z=j+39|0;t=j+40|0;a:{b:{c:{d:{e:while(1){h=0;f:while(1){k=b;if((o^2147483647)<(h|0)){break d}o=h+o|0;g:{h:{i:{j:{h=b;i=I[h|0];if(i){while(1){k:{b=i&255;l:{if(!b){b=h;break l}if((b|0)!=37){break k}i=h;while(1){if(I[i+1|0]!=37){b=i;break l}h=h+1|0;n=I[i+2|0];b=i+2|0;i=b;if((n|0)==37){continue}break}}h=h-k|0;y=o^2147483647;if((h|0)>(y|0)){break d}if(a){Ma(a,k,h)}if(h){continue f}H[j+60>>2]=b;h=b+1|0;q=-1;i=F[b+1|0]-48|0;if(!(I[b+2|0]!=36|i>>>0>9)){x=1;q=i;h=b+3|0}H[j+60>>2]=h;l=0;i=F[h|0];b=i-32|0;m:{if(b>>>0>31){n=h;break m}n=h;b=1<<b;if(!(b&75913)){break m}while(1){n=h+1|0;H[j+60>>2]=n;l=b|l;i=F[h+1|0];b=i-32|0;if(b>>>0>=32){break m}h=n;b=1<<b;if(b&75913){continue}break}}n:{if((i|0)==42){b=F[n+1|0]-48|0;o:{if(!(I[n+2|0]!=36|b>>>0>9)){p:{if(!a){H[(b<<2)+e>>2]=10;b=0;break p}b=H[(b<<3)+d>>2]}p=b;b=n+3|0;i=1;break o}if(x){break j}b=n+1|0;if(!a){H[j+60>>2]=b;x=0;p=0;break n}h=H[c>>2];H[c>>2]=h+4;p=H[h>>2];i=0}x=i;H[j+60>>2]=b;if((p|0)>=0){break n}p=0-p|0;l=l|8192;break n}p=Ec(j+60|0);if((p|0)<0){break d}b=H[j+60>>2]}h=0;m=-1;u=0;q:{if(I[b|0]!=46){break q}if(I[b+1|0]==42){i=F[b+2|0]-48|0;r:{if(!(I[b+3|0]!=36|i>>>0>9)){b=b+4|0;s:{if(!a){H[(i<<2)+e>>2]=10;m=0;break s}m=H[(i<<3)+d>>2]}break r}if(x){break j}b=b+2|0;m=0;if(!a){break r}i=H[c>>2];H[c>>2]=i+4;m=H[i>>2]}H[j+60>>2]=b;u=(m|0)>=0;break q}H[j+60>>2]=b+1;m=Ec(j+60|0);b=H[j+60>>2];u=1}while(1){v=h;n=28;r=b;i=F[b|0];if(i-123>>>0<4294967238){break c}b=b+1|0;h=I[(i+N(h,58)|0)+25263|0];if((h-1&255)>>>0<8){continue}break}H[j+60>>2]=b;t:{if((h|0)!=27){if(!h){break c}if((q|0)>=0){if(!a){H[(q<<2)+e>>2]=h;continue e}h=(q<<3)+d|0;i=H[h+4>>2];H[j+48>>2]=H[h>>2];H[j+52>>2]=i;break t}if(!a){break g}Dc(j+48|0,h,c,g);break t}if((q|0)>=0){break c}h=0;if(!a){continue f}}if(I[a|0]&32){break b}i=l&-65537;l=l&8192?i:l;q=0;w=1072;n=t;u:{v:{w:{x:{y:{z:{A:{B:{C:{D:{E:{F:{G:{H:{I:{J:{K:{r=I[r|0];h=r<<24>>24;h=v?(r&15)==3?h&-45:h:h;switch(h-88|0){case 0:case 32:break G;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 10:case 16:case 18:case 19:case 20:case 21:case 25:case 26:case 28:case 30:case 31:break h;case 9:case 13:case 14:case 15:break u;case 11:break B;case 12:case 17:break E;case 22:break I;case 23:break F;case 24:break H;case 27:break A;case 29:break J;default:break K}}L:{switch(h-65|0){case 1:case 3:break h;case 0:case 4:case 5:case 6:break u;case 2:break z;default:break L}}if((h|0)==83){break y}break h}i=H[j+48>>2];r=H[j+52>>2];w=1072;break D}h=0;M:{switch(v|0){case 0:H[H[j+48>>2]>>2]=o;continue f;case 1:H[H[j+48>>2]>>2]=o;continue f;case 2:k=H[j+48>>2];H[k>>2]=o;H[k+4>>2]=o>>31;continue f;case 3:G[H[j+48>>2]>>1]=o;continue f;case 4:F[H[j+48>>2]]=o;continue f;case 6:H[H[j+48>>2]>>2]=o;continue f;case 7:break M;default:continue f}}k=H[j+48>>2];H[k>>2]=o;H[k+4>>2]=o>>31;continue f}m=m>>>0<=8?8:m;l=l|8;h=120}b=t;k=H[j+52>>2];r=k;i=H[j+48>>2];s=i;if(i|k){A=h&32;while(1){b=b-1|0;F[b|0]=A|I[(s&15)+25792|0];v=!k&s>>>0>15|(k|0)!=0;s=(k&15)<<28|s>>>4;k=k>>>4|0;if(v){continue}break}}k=b;if(!(l&8)|!(i|r)){break C}w=(h>>>4|0)+1072|0;q=2;break C}b=t;k=H[j+52>>2];r=k;i=H[j+48>>2];s=i;if(i|k){while(1){b=b-1|0;F[b|0]=s&7|48;v=!k&s>>>0>7|(k|0)!=0;s=(k&7)<<29|s>>>3;k=k>>>3|0;if(v){continue}break}}k=b;if(!(l&8)){break C}b=t-b|0;m=(b|0)<(m|0)?m:b+1|0;break C}i=H[j+48>>2];b=H[j+52>>2];r=b;if((b|0)<0){h=0-(b+((i|0)!=0)|0)|0;r=h;i=0-i|0;H[j+48>>2]=i;H[j+52>>2]=h;q=1;w=1072;break D}if(l&2048){q=1;w=1073;break D}q=l&1;w=q?1074:1072}k=cb(i,r,t)}if((m|0)<0&u){break d}l=u?l&-65537:l;if(!((i|r)!=0|m)){k=t;m=0;break h}b=!(i|r)+(t-k|0)|0;m=(b|0)<(m|0)?m:b;break h}h=I[j+48|0];break i}h=m>>>0>=2147483647?2147483647:m;l=h;n=(h|0)!=0;b=H[j+48>>2];k=b?b:1686;b=k;N:{O:{P:{Q:{if(!(b&3)|!h){break Q}while(1){if(!I[b|0]){break P}l=l-1|0;n=(l|0)!=0;b=b+1|0;if(!(b&3)){break Q}if(l){continue}break}}if(!n){break O}if(!(!I[b|0]|l>>>0<4)){while(1){n=H[b>>2];if(((16843008-n|n)&-2139062144)!=-2139062144){break P}b=b+4|0;l=l-4|0;if(l>>>0>3){continue}break}}if(!l){break O}}while(1){if(!I[b|0]){break N}b=b+1|0;l=l-1|0;if(l){continue}break}}b=0}b=b?b-k|0:h;n=b+k|0;if((m|0)>=0){l=i;m=b;break h}l=i;m=b;if(I[n|0]){break d}break h}h=H[j+48>>2];if(h|H[j+52>>2]){break x}h=0;break i}if(m){i=H[j+48>>2];break w}h=0;Oa(a,32,p,0,l);break v}H[j+12>>2]=0;H[j+8>>2]=h;i=j+8|0;H[j+48>>2]=i;m=-1}h=0;while(1){R:{k=H[i>>2];if(!k){break R}k=Cc(j+4|0,k);if((k|0)<0){break b}if(k>>>0>m-h>>>0){break R}i=i+4|0;h=h+k|0;if(m>>>0>h>>>0){continue}}break}n=61;if((h|0)<0){break c}Oa(a,32,p,h,l);if(!h){h=0;break v}n=0;i=H[j+48>>2];while(1){k=H[i>>2];if(!k){break v}m=j+4|0;k=Cc(m,k);n=k+n|0;if(n>>>0>h>>>0){break v}Ma(a,m,k);i=i+4|0;if(h>>>0>n>>>0){continue}break}}Oa(a,32,p,h,l^8192);h=(h|0)<(p|0)?p:h;continue f}if((m|0)<0&u){break d}n=61;h=sa[f|0](a,M[j+48>>3],p,m,l,h)|0;if((h|0)>=0){continue f}break c}i=I[h+1|0];h=h+1|0;continue}}if(a){break a}if(!x){break g}h=1;while(1){a=H[(h<<2)+e>>2];if(a){Dc((h<<3)+d|0,a,c,g);o=1;h=h+1|0;if((h|0)!=10){continue}break a}break}if(h>>>0>=10){o=1;break a}while(1){if(H[(h<<2)+e>>2]){break j}o=1;h=h+1|0;if((h|0)!=10){continue}break}break a}n=28;break c}F[j+39|0]=h;m=1;k=z;l=i}i=n-k|0;m=(i|0)<(m|0)?m:i;if((m|0)>(q^2147483647)){break d}n=61;b=m+q|0;h=(b|0)<(p|0)?p:b;if((y|0)<(h|0)){break c}Oa(a,32,h,b,l);Ma(a,w,q);Oa(a,48,h,b,l^65536);Oa(a,48,m,i,0);Ma(a,k,i);Oa(a,32,h,b,l^8192);b=H[j+60>>2];continue}break}break}o=0;break a}n=61}H[6597]=n}o=-1}oa=j- -64|0;return o}function qd(a,b,c,d,e,f){a=a|0;b=+b;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,t=0,u=0,v=0,w=0,y=0,z=0,A=0;m=oa-560|0;oa=m;H[m+44>>2]=0;x(+b);h=s(1)|0;s(0)|0;a:{if((h|0)<0){u=1;z=1082;b=-b;x(+b);h=s(1)|0;s(0)|0;break a}if(e&2048){u=1;z=1085;break a}u=e&1;z=u?1088:1083;A=!u}b:{if((h&2146435072)==2146435072){h=u+3|0;Oa(a,32,c,h,e&-65537);Ma(a,z,u);d=f&32;Ma(a,b!=b?d?1207:1435:d?1312:1476,3);Oa(a,32,c,h,e^8192);n=(c|0)>(h|0)?c:h;break b}w=m+16|0;c:{d:{e:{b=Bc(b,m+44|0);b=b+b;if(b!=0){h=H[m+44>>2];H[m+44>>2]=h-1;y=f|32;if((y|0)!=97){break e}break c}y=f|32;if((y|0)==97){break c}l=H[m+44>>2];break d}l=h-29|0;H[m+44>>2]=l;b=b*268435456}k=(d|0)<0?6:d;r=(m+48|0)+((l|0)>=0?288:0)|0;h=r;while(1){d=b<4294967295&b>=0?~~b>>>0:0;H[h>>2]=d;h=h+4|0;b=(b-+(d>>>0))*1e9;if(b!=0){continue}break}f:{if((l|0)<=0){i=l;g=h;j=r;break f}j=r;i=l;while(1){o=i>>>0>=29?29:i;g=h-4|0;g:{if(j>>>0>g>>>0){break g}p=0;while(1){q=0;d=H[g>>2];i=o&31;n=p;if((o&63)>>>0>=32){p=d<<i;d=0}else{p=(1<<i)-1&d>>>32-i;d=d<<i}i=n+d|0;q=p+q|0;q=d>>>0>i>>>0?q+1|0:q;p=Ke(i,q,1e9,0);n=Ie(p,ra,-1e9);d=q;q=i+n|0;H[g>>2]=q;g=g-4|0;if(j>>>0<=g>>>0){continue}break}if(!d&i>>>0<1e9){break g}j=j-4|0;H[j>>2]=p}while(1){g=h;if(j>>>0<g>>>0){h=g-4|0;if(!H[h>>2]){continue}}break}i=H[m+44>>2]-o|0;H[m+44>>2]=i;h=g;if((i|0)>0){continue}break}}if((i|0)<0){v=((k+25>>>0)/9|0)+1|0;p=(y|0)==102;while(1){d=0-i|0;n=d>>>0>=9?9:d;h:{if(g>>>0<=j>>>0){h=H[j>>2]?0:4;break h}q=1e9>>>n|0;o=-1<<n^-1;i=0;h=j;while(1){d=H[h>>2];H[h>>2]=(d>>>n|0)+i;i=N(q,d&o);h=h+4|0;if(h>>>0<g>>>0){continue}break}h=H[j>>2]?0:4;if(!i){break h}H[g>>2]=i;g=g+4|0}i=n+H[m+44>>2]|0;H[m+44>>2]=i;j=h+j|0;d=p?r:j;g=g-d>>2>(v|0)?d+(v<<2)|0:g;if((i|0)<0){continue}break}}i=0;i:{if(g>>>0<=j>>>0){break i}i=N(r-j>>2,9);h=10;d=H[j>>2];if(d>>>0<10){break i}while(1){i=i+1|0;h=N(h,10);if(d>>>0>=h>>>0){continue}break}}d=(k-((y|0)!=102?i:0)|0)-((y|0)==103&(k|0)!=0)|0;if((d|0)<(N(g-r>>2,9)-9|0)){h=(m+48|0)+((l|0)<0?-4092:-3804)|0;l=d+9216|0;d=(l|0)/9|0;n=h+(d<<2)|0;h=10;d=l+N(d,-9)|0;if((d|0)<=7){while(1){h=N(h,10);d=d+1|0;if((d|0)!=8){continue}break}}l=H[n>>2];v=(l>>>0)/(h>>>0)|0;o=N(v,h);d=n+4|0;j:{if((l|0)==(o|0)&(d|0)==(g|0)){break j}l=l-o|0;k:{if(!(v&1)){b=9007199254740992;if(!(F[n-4|0]&1)|((h|0)!=1e9|j>>>0>=n>>>0)){break k}}b=9007199254740994}t=(d|0)==(g|0)?1:1.5;d=h>>>1|0;t=d>>>0>l>>>0?.5:(d|0)==(l|0)?t:1.5;if(!(I[z|0]!=45|A)){t=-t;b=-b}H[n>>2]=o;if(b+t==b){break j}d=h+o|0;H[n>>2]=d;if(d>>>0>=1e9){while(1){H[n>>2]=0;n=n-4|0;if(n>>>0<j>>>0){j=j-4|0;H[j>>2]=0}d=H[n>>2]+1|0;H[n>>2]=d;if(d>>>0>999999999){continue}break}}i=N(r-j>>2,9);h=10;d=H[j>>2];if(d>>>0<10){break j}while(1){i=i+1|0;h=N(h,10);if(d>>>0>=h>>>0){continue}break}}d=n+4|0;g=d>>>0<g>>>0?d:g}while(1){l=g;o=g>>>0<=j>>>0;if(!o){g=g-4|0;if(!H[g>>2]){continue}}break}l:{if((y|0)!=103){p=e&8;break l}h=k?k:1;d=(h|0)>(i|0)&(i|0)>-5;k=(d?i^-1:-1)+h|0;f=(d?-1:-2)+f|0;p=e&8;if(p){break l}g=-9;m:{if(o){break m}o=H[l-4>>2];if(!o){break m}d=10;g=0;if((o>>>0)%10|0){break m}while(1){h=g;g=g+1|0;d=N(d,10);if(!((o>>>0)%(d>>>0)|0)){continue}break}g=h^-1}d=N(l-r>>2,9);if((f&-33)==70){p=0;d=(d+g|0)-9|0;d=(d|0)>0?d:0;k=(d|0)>(k|0)?k:d;break l}p=0;d=((d+i|0)+g|0)-9|0;d=(d|0)>0?d:0;k=(d|0)>(k|0)?k:d}n=-1;o=k|p;if(((o?2147483645:2147483646)|0)<(k|0)){break b}q=(((o|0)!=0)+k|0)+1|0;h=f&-33;n:{if((h|0)==70){if((q^2147483647)<(i|0)){break b}g=(i|0)>0?i:0;break n}d=i>>31;g=cb((d^i)-d|0,0,w);if((w-g|0)<=1){while(1){g=g-1|0;F[g|0]=48;if((w-g|0)<2){continue}break}}v=g-2|0;F[v|0]=f;F[g-1|0]=(i|0)<0?45:43;g=w-v|0;if((g|0)>(q^2147483647)){break b}}d=g+q|0;if((d|0)>(u^2147483647)){break b}i=d+u|0;Oa(a,32,c,i,e);Ma(a,z,u);Oa(a,48,c,i,e^65536);o:{p:{q:{if((h|0)==70){h=m+16|9;f=j>>>0>r>>>0?r:j;j=f;while(1){g=cb(H[j>>2],0,h);r:{if((f|0)!=(j|0)){if(m+16>>>0>=g>>>0){break r}while(1){g=g-1|0;F[g|0]=48;if(m+16>>>0<g>>>0){continue}break}break r}if((g|0)!=(h|0)){break r}g=g-1|0;F[g|0]=48}Ma(a,g,h-g|0);j=j+4|0;if(r>>>0>=j>>>0){continue}break}if(o){Ma(a,1684,1)}if((k|0)<=0|j>>>0>=l>>>0){break q}while(1){g=cb(H[j>>2],0,h);if(g>>>0>m+16>>>0){while(1){g=g-1|0;F[g|0]=48;if(m+16>>>0<g>>>0){continue}break}}Ma(a,g,(k|0)>=9?9:k);g=k-9|0;j=j+4|0;if(l>>>0<=j>>>0){break p}d=(k|0)>9;k=g;if(d){continue}break}break p}s:{if((k|0)<0){break s}f=j>>>0<l>>>0?l:j+4|0;l=m+16|9;h=j;while(1){g=cb(H[h>>2],0,l);if((l|0)==(g|0)){g=g-1|0;F[g|0]=48}t:{if((h|0)!=(j|0)){if(m+16>>>0>=g>>>0){break t}while(1){g=g-1|0;F[g|0]=48;if(m+16>>>0<g>>>0){continue}break}break t}Ma(a,g,1);g=g+1|0;if(!(k|p)){break t}Ma(a,1684,1)}d=l-g|0;Ma(a,g,(d|0)<(k|0)?d:k);k=k-d|0;h=h+4|0;if(f>>>0<=h>>>0){break s}if((k|0)>=0){continue}break}}Oa(a,48,k+18|0,18,0);Ma(a,v,w-v|0);break o}g=k}Oa(a,48,g+9|0,9,0)}Oa(a,32,c,i,e^8192);n=(c|0)>(i|0)?c:i;break b}k=(f<<26>>31&9)+z|0;u:{if(d>>>0>11){break u}g=12-d|0;t=16;while(1){t=t*16;g=g-1|0;if(g){continue}break}if(I[k|0]==45){b=-(t+(-b-t));break u}b=b+t-t}h=H[m+44>>2];g=h>>31;g=cb((g^h)-g|0,0,w);if((w|0)==(g|0)){g=g-1|0;F[g|0]=48;h=H[m+44>>2]}r=u|2;j=f&32;l=g-2|0;F[l|0]=f+15;F[g-1|0]=(h|0)<0?45:43;g=!(e&8)&(d|0)<=0;h=m+16|0;while(1){f=h;i=P(b)<2147483647?~~b:-2147483648;F[h|0]=j|I[i+25792|0];b=(b-+(i|0))*16;h=h+1|0;if(!(g&b==0|(h-(m+16|0)|0)!=1)){F[f+1|0]=46;h=f+2|0}if(b!=0){continue}break}n=-1;g=w-l|0;f=g+r|0;if((2147483645-f|0)<(d|0)){break b}i=f;f=m+16|0;j=h-f|0;d=d?(j-2|0)<(d|0)?d+2|0:j:j;h=i+d|0;Oa(a,32,c,h,e);Ma(a,k,r);Oa(a,48,c,h,e^65536);Ma(a,f,j);Oa(a,48,d-j|0,0,0);Ma(a,l,g);Oa(a,32,c,h,e^8192);n=(c|0)>(h|0)?c:h}oa=m+560|0;return n|0}function Zc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;j=oa-80|0;oa=j;H[j+76>>2]=1;a:{b:{if(H[a+128>>2]!=1|H[a+132>>2]!=1|(H[a+108>>2]|H[a+112>>2])){break b}k=H[a+100>>2];if(H[k>>2]|H[k+4>>2]|(H[k+8>>2]!=H[a+116>>2]|H[k+12>>2]!=H[a+120>>2])){break b}if(!Za(a,j+72|0,0,j+68|0,j- -64|0,j+60|0,j+56|0,j+52|0,j+76|0,b,c)){break a}c:{d:{if(!H[j+76>>2]){break d}if(!gb(a,H[j+72>>2],0,0,b,c)){break d}b=H[a+100>>2];if(H[b+16>>2]){break c}d=1;break a}Ca(c,1,8776,0);break a}e=H[b+24>>2];while(1){b=N(g,52);Da(H[(b+e|0)+44>>2]);c=H[a+100>>2];e=H[c+24>>2];k=b+e|0;d=H[a+232>>2];n=H[H[H[d+20>>2]>>2]+20>>2]+N(g,76)|0;H[k+44>>2]=H[n+36>>2];H[k+36>>2]=H[(b+H[H[d+24>>2]+24>>2]|0)+36>>2];H[n+36>>2]=0;d=1;g=g+1|0;if(g>>>0<K[c+16>>2]){continue}break}break a}H[a+80>>2]=0;H[a+84>>2]=0;Da(H[a+88>>2]);H[a+88>>2]=0;e:{if(!(H[a+28>>2]|H[a+32>>2]|H[a+36>>2]!=H[a+128>>2])){k=2;if(H[a+40>>2]==H[a+132>>2]){break e}}k=2;if(H[a+76>>2]){break e}if(!Fb(b)){break e}r=H[a+128>>2];k=N(r,H[a+132>>2]);if(k){i=k&1;h=H[H[a+224>>2]+40>>2];f:{if((k|0)==1){k=0;break f}l=k&-2;k=0;while(1){d=h+N(g,40)|0;f=H[d+4>>2];if(f){m=(H[d+16>>2]+N(f,24)|0)-8|0;f=H[m>>2];o=f;p=f>>>0>k>>>0;f=H[m+4>>2];m=p&(f|0)>=(n|0)|(f|0)>(n|0);k=m?o:k;n=m?f:n}f=H[d+44>>2];if(f){f=(H[d+56>>2]+N(f,24)|0)-8|0;d=H[f>>2];o=d;m=d>>>0>k>>>0;d=H[f+4>>2];f=m&(d|0)>=(n|0)|(d|0)>(n|0);k=f?o:k;n=f?d:n}g=g+2|0;e=e+2|0;if((l|0)!=(e|0)){continue}break}}g:{if(!i){break g}d=h+N(g,40)|0;h=H[d+4>>2];if(!h){break g}h=(H[d+16>>2]+N(h,24)|0)-8|0;d=H[h>>2];f=d;g=d>>>0>k>>>0;d=H[h+4>>2];h=g&(d|0)>=(n|0)|(d|0)>(n|0);k=h?f:k;n=h?d:n}k=k+2|0;n=k>>>0<2?n+1|0:n}else{k=2;n=0}g=0;f=H[a+32>>2];q=H[a+40>>2];h:{if(f>>>0>=q>>>0){break h}h=H[a+28>>2];i=H[a+36>>2];if(h>>>0>=i>>>0){break h}l=i-h&3;t=H[H[a+224>>2]+40>>2];u=h-i>>>0>4294967292;while(1){m=t+N(N(f,r),40)|0;d=h;e=0;if(l){while(1){g=H[(m+N(d,40)|0)+4>>2]+g|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}if(!u){while(1){e=m+N(d,40)|0;g=H[e+124>>2]+(H[e+84>>2]+(H[e+44>>2]+(H[e+4>>2]+g|0)|0)|0)|0;d=d+4|0;if((i|0)!=(d|0)){continue}break}}f=f+1|0;if((q|0)!=(f|0)){continue}break}}f=Ga(g<<3);H[a+88>>2]=f;if(!g|!f){break e}g=0;d=H[a+40>>2];i=H[a+32>>2];i:{if(d>>>0<=i>>>0){break i}e=H[a+36>>2];if(e>>>0<=K[a+28>>2]){break i}while(1){l=H[a+28>>2];if(l>>>0<e>>>0){t=H[H[a+224>>2]+40>>2]+N(N(H[a+128>>2],i),40)|0;while(1){h=t+N(l,40)|0;d=H[h+4>>2];if(d){r=d&3;h=H[h+16>>2];q=0;j:{if(d>>>0<4){d=0;break j}u=h+72|0;v=h+48|0;w=h+24|0;x=d&-4;d=0;f=0;while(1){m=N(d,24);p=m+h|0;s=H[p+4>>2];e=g<<3;o=e+H[a+88>>2]|0;H[o>>2]=H[p>>2];H[o+4>>2]=s;p=m+w|0;s=H[p+4>>2];o=e+H[a+88>>2]|0;H[o+8>>2]=H[p>>2];H[o+12>>2]=s;p=m+v|0;s=H[p+4>>2];o=e+H[a+88>>2]|0;H[o+16>>2]=H[p>>2];H[o+20>>2]=s;m=m+u|0;o=H[m+4>>2];e=e+H[a+88>>2]|0;H[e+24>>2]=H[m>>2];H[e+28>>2]=o;d=d+4|0;g=g+4|0;f=f+4|0;if((x|0)!=(f|0)){continue}break}}if(r){while(1){f=h+N(d,24)|0;m=H[f+4>>2];e=H[a+88>>2]+(g<<3)|0;H[e>>2]=H[f>>2];H[e+4>>2]=m;d=d+1|0;g=g+1|0;q=q+1|0;if((r|0)!=(q|0)){continue}break}}e=H[a+36>>2]}l=l+1|0;if(l>>>0<e>>>0){continue}break}d=H[a+40>>2]}i=i+1|0;if(i>>>0<d>>>0){continue}break}f=H[a+88>>2]}H[a+84>>2]=g;e=oa-208|0;oa=e;H[e+8>>2]=1;H[e+12>>2]=0;l=g<<3;k:{if(!l){break k}H[e+16>>2]=8;H[e+20>>2]=8;d=8;g=8;i=2;while(1){h=d;d=(g+8|0)+d|0;H[(e+16|0)+(i<<2)>>2]=d;i=i+1|0;g=h;if(d>>>0<l>>>0){continue}break}h=(f+l|0)-8|0;l:{if(h>>>0<=f>>>0){g=0;i=1;d=1;h=0;break l}i=1;d=1;while(1){m:{if((i&3)==3){Gb(f,d,e+16|0);xb(e+8|0,2);d=d+2|0;break m}l=e+16|0;g=d-1|0;n:{if(K[l+(g<<2)>>2]>=h-f>>>0){wb(f,i,H[e+12>>2],d,0,l);break n}Gb(f,d,e+16|0)}if((d|0)==1){vb(e+8|0,1);d=0;break m}vb(e+8|0,g);d=1}i=H[e+8>>2]|1;H[e+8>>2]=i;f=f+8|0;if(h>>>0>f>>>0){continue}break}g=H[e+12>>2];h=(g|0)!=0}wb(f,i,g,d,0,e+16|0);i=H[e+8>>2];if(!(h|((d|0)!=1|(i|0)!=1))){break k}while(1){o:{if((d|0)<=1){h=Jc(i,g);xb(e+8|0,h);d=d+h|0;break o}g=e+8|0;vb(g,2);H[e+8>>2]=H[e+8>>2]^7;xb(g,1);l=f-8|0;i=e+16|0;h=d-2|0;wb(l-H[i+(h<<2)>>2]|0,H[e+8>>2],H[e+12>>2],d-1|0,1,i);vb(g,1);d=H[e+8>>2]|1;H[e+8>>2]=d;wb(l,d,H[e+12>>2],h,1,i);d=h}f=f-8|0;g=H[e+12>>2];i=H[e+8>>2];if(g|((d|0)!=1|(i|0)!=1)){continue}break}}oa=e+208|0}d=H[a+128>>2];e=0;p:{while(1){q:{if(!(!H[H[a+180>>2]+5596>>2]|((d|0)!=1|H[a+132>>2]!=1))){H[j+72>>2]=0;H[a+228>>2]=0;H[a+8>>2]=H[a+8>>2]|128;d=0;break q}d=0;if(!Za(a,j+72|0,0,j+68|0,j- -64|0,j+60|0,j+56|0,j+52|0,j+76|0,b,c)){break a}if(!H[j+76>>2]){break p}d=H[j+72>>2]}h=d+1|0;f=gb(a,d,0,0,b,c);g=N(H[a+128>>2],H[a+132>>2]);if(!f){H[j+4>>2]=g;H[j>>2]=h;Ca(c,1,7537,j);d=0;break a}H[j+36>>2]=g;H[j+32>>2]=h;Ca(c,4,11795,j+32|0);if(!Sc(H[a+232>>2],H[H[a+100>>2]+24>>2])){d=0;break a}r:{if(!(H[a+128>>2]!=1|H[a+132>>2]!=1)){g=H[a+100>>2];f=H[a+96>>2];if(H[g>>2]!=H[f>>2]|H[g+4>>2]!=H[f+4>>2]|(H[g+8>>2]!=H[f+8>>2]|H[g+12>>2]!=H[f+12>>2])){break r}}d=H[a+180>>2]+N(d,5644)|0;g=H[d+5596>>2];if(!g){break r}Da(g);H[d+5596>>2]=0;H[d+5600>>2]=0}H[j+16>>2]=h;Ca(c,4,16601,j+16|0);if(!(Sa(b)|ra)&H[a+8>>2]==64){break p}e=e+1|0;d=H[a+128>>2];if((e|0)==(N(d,H[a+132>>2])|0)){break p}h=H[a+84>>2];if(!h|(h|0)!=H[a+80>>2]){continue}break}zc(b,k,n,c)}d=Rc(a,c)}oa=j+80|0;return d|0}function $a(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;d=H[a+32>>2];a:{if(d){break a}b:{h=H[a+16>>2];if((h|0)>=6){b=H[a+8>>2];f=H[a+12>>2];d=h;break b}b=H[a+20>>2];c:{d:{if((b|0)>=5){c=H[a>>2];d=H[c>>2];H[a>>2]=c+4;g=b-4|0;break d}if((b|0)<=0){d=-1;break c}c=H[a>>2];e:{if((b|0)==1){e=-1;b=0;break e}e=-1;f=b-1|0;k=f&1;f:{if((b|0)==2){d=0;i=b;break f}j=f&-2;d=0;f=c;i=b;while(1){H[a>>2]=f+1;l=I[f|0];c=f+2|0;H[a>>2]=c;H[a+20>>2]=i-1;f=I[f+1|0];i=i-2|0;H[a+20>>2]=i;e=((255<<d^-1)&e|l<<d)&(65280<<d^-1)|f<<(d|8);d=d+16|0;f=c;m=m+2|0;if((j|0)!=(m|0)){continue}break}}if(k){f=c+1|0;H[a>>2]=f;c=I[c|0];H[a+20>>2]=i-1;e=(255<<d^-1)&e|c<<d;c=f}b=(b<<3)-8|0}H[a>>2]=c+1;d=(255<<b^-1)&e|(I[c|0]|15)<<b}H[a+20>>2]=g}b=H[a+24>>2];c=d>>>24|0;H[a+24>>2]=(c|0)==255;g=d>>>16&255;k=(g|0)==255;f=d&255;e=(f|0)==255;j=b+e|0;b=d>>>8&255;i=(b|0)==255;j=k+(j+i|0)|0;d=(h-j|0)+32|0;H[a+16>>2]=d;l=H[a+12>>2];b=c|(g|(b|f<<(e?7:8))<<(i?7:8))<<(k?7:8);f=(j-h|0)+32|0;c=f&31;if((f&63)>>>0>=32){i=b<<c;g=0}else{i=(1<<c)-1&b>>>32-c;g=b<<c}b=g|H[a+8>>2];c=i|l;f=c;H[a+8>>2]=b;H[a+12>>2]=c;if((d|0)>=6){break b}d=0;break a}e=H[a+28>>2];i=H[(e<<2)+20752>>2];g:{if((f|0)<0){d=d-1|0;c=(-1<<i^-1)<<1;i=1;e=((e|0)>=11?11:e)+1|0;break g}g=b;h=63-i|0;c=h&31;if((h&63)>>>0>=32){g=f>>>c|0}else{g=((1<<c)-1&f)<<32-c|g>>>c}c=(g&(-1<<i^-1))<<1|1;i=i+1|0;d=d-i|0;e=((e|0)<=1?1:e)-1|0}H[a+16>>2]=d;H[a+28>>2]=e;g=b;h=i&31;if((i&63)>>>0>=32){b=b<<h;g=0}else{b=(1<<h)-1&g>>>32-h|f<<h;g=g<<h}f=b;H[a+8>>2]=g;H[a+12>>2]=b;i=H[a+44>>2]|c>>31;j=H[a+40>>2]&-64|c;H[a+40>>2]=j;H[a+44>>2]=i;if((d|0)<6){d=1;break a}b=H[(e<<2)+20752>>2];h:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((e|0)>=11?11:e)+1|0;break h}k=g;h=63-b|0;c=h&31;if((h&63)>>>0>=32){k=f>>>c|0}else{k=((1<<c)-1&f)<<32-c|k>>>c}c=(k&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((e|0)<=1?1:e)-1|0}H[a+16>>2]=d;H[a+28>>2]=e;k=g;h=b&31;if((b&63)>>>0>=32){b=g<<h;k=0}else{b=(1<<h)-1&k>>>32-h|f<<h;k=k<<h}f=b;H[a+8>>2]=k;H[a+12>>2]=b;b=c>>31<<7|c>>>25|i;h=b;j=j&-8065|c<<7;H[a+40>>2]=j;H[a+44>>2]=b;if((d|0)<6){d=2;break a}b=H[(e<<2)+20752>>2];i:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((e|0)>=11?11:e)+1|0;break i}g=k;i=63-b|0;c=i&31;if((i&63)>>>0>=32){g=f>>>c|0}else{g=((1<<c)-1&f)<<32-c|g>>>c}c=(g&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((e|0)<=1?1:e)-1|0}H[a+16>>2]=d;H[a+28>>2]=e;l=k;g=b&31;if((b&63)>>>0>=32){i=k<<g;g=0}else{i=(1<<g)-1&l>>>32-g|f<<g;g=l<<g}H[a+8>>2]=g;f=i;H[a+12>>2]=f;b=c>>31<<14|c>>>18|h;i=b;k=j&-1032193|c<<14;H[a+40>>2]=k;H[a+44>>2]=b;if((d|0)<6){d=3;break a}b=H[(e<<2)+20752>>2];j:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((e|0)>=11?11:e)+1|0;break j}j=g;h=63-b|0;c=h&31;if((h&63)>>>0>=32){j=f>>>c|0}else{j=((1<<c)-1&f)<<32-c|j>>>c}c=(j&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((e|0)<=1?1:e)-1|0}H[a+16>>2]=d;H[a+28>>2]=e;j=g;h=b&31;if((b&63)>>>0>=32){b=g<<h;g=0}else{b=(1<<h)-1&j>>>32-h|f<<h;g=j<<h}f=b;H[a+8>>2]=g;H[a+12>>2]=b;b=c>>31<<21|c>>>11|i;j=b;k=k&-132120577|c<<21;H[a+40>>2]=k;H[a+44>>2]=b;if((d|0)<6){d=4;break a}b=H[(e<<2)+20752>>2];k:{if((f|0)<0){c=(-1<<b^-1)<<1;b=1;h=((e|0)>=11?11:e)+1|0;d=d-1|0;break k}h=g;i=63-b|0;c=i&31;if((i&63)>>>0>=32){i=f>>>c|0}else{i=((1<<c)-1&f)<<32-c|h>>>c}c=(i&(-1<<b^-1))<<1|1;h=((e|0)<=1?1:e)-1|0;b=b+1|0;d=d-b|0}H[a+16>>2]=d;H[a+28>>2]=h;i=g;e=b&31;if((b&63)>>>0>=32){b=g<<e;g=0}else{b=(1<<e)-1&i>>>32-e|f<<e;g=i<<e}H[a+8>>2]=g;f=b;H[a+12>>2]=b;b=j&-4|(c>>31<<28|c>>>4);j=b;k=k&268435455|c<<28;H[a+40>>2]=k;H[a+44>>2]=b;if((d|0)<6){d=5;break a}b=H[(h<<2)+20752>>2];l:{if((f|0)<0){e=(-1<<b^-1)<<1;b=1;h=((h|0)>=11?11:h)+1|0;i=d-1|0;break l}i=g;e=63-b|0;c=e&31;if((e&63)>>>0>=32){i=f>>>c|0}else{i=((1<<c)-1&f)<<32-c|i>>>c}e=(i&(-1<<b^-1))<<1|1;h=((h|0)<=1?1:h)-1|0;b=b+1|0;i=d-b|0}H[a+16>>2]=i;H[a+28>>2]=h;d=g;c=b&31;if((b&63)>>>0>=32){b=d<<c;g=0}else{b=(1<<c)-1&d>>>32-c|f<<c;g=d<<c}c=b;H[a+8>>2]=g;H[a+12>>2]=b;b=j&-505|e<<3;l=b;H[a+40>>2]=k;H[a+44>>2]=b;d=6;if((i|0)<6){break a}b=H[(h<<2)+20752>>2];m:{if((c|0)<0){e=(-1<<b^-1)<<1;b=1;h=((h|0)>=11?11:h)+1|0;d=i-1|0;break m}d=g;e=63-b|0;f=e&31;if((e&63)>>>0>=32){f=c>>>f|0}else{f=((1<<f)-1&c)<<32-f|d>>>f}e=(f&(-1<<b^-1))<<1|1;h=((h|0)<=1?1:h)-1|0;b=b+1|0;d=i-b|0}H[a+16>>2]=d;H[a+28>>2]=h;j=g;f=b&31;if((b&63)>>>0>=32){i=g<<f;g=0}else{i=(1<<f)-1&j>>>32-f|c<<f;g=j<<f}H[a+8>>2]=g;f=i;H[a+12>>2]=f;i=k;b=l&-64513|e<<10;k=b;H[a+40>>2]=i;H[a+44>>2]=b;if((d|0)<6){d=7;break a}b=H[(h<<2)+20752>>2];n:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((h|0)>=11?11:h)+1|0;break n}j=g;e=63-b|0;c=e&31;if((e&63)>>>0>=32){j=f>>>c|0}else{j=((1<<c)-1&f)<<32-c|j>>>c}c=(j&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((h|0)<=1?1:h)-1|0}H[a+16>>2]=d;H[a+28>>2]=e;d=g;e=b&31;if((b&63)>>>0>=32){b=d<<e;g=0}else{b=(1<<e)-1&d>>>32-e|f<<e;g=d<<e}H[a+8>>2]=g;H[a+12>>2]=b;H[a+40>>2]=i;H[a+44>>2]=k&-8257537|c<<17;d=8}H[a+32>>2]=d-1;f=H[a+44>>2];b=f>>>7|0;c=H[a+40>>2];H[a+40>>2]=(f&127)<<25|c>>>7;H[a+44>>2]=b;return c&127}function ec(a,b,c,d,e,f,g,h,i){var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,z=0,A=0;p=oa-32|0;oa=p;H[p+24>>2]=f;r=H[(N(H[d+28>>2],76)+b|0)+28>>2]+N(H[d+32>>2],152)|0;a:{if(!(H[d+40>>2]|!H[r+24>>2])){k=r+28|0;while(1){b:{if(ac(k)){break b}b=H[d+36>>2];if(b>>>0>=K[k+24>>2]/40>>>0){Ca(i,1,2836,0);break a}b=H[k+20>>2]+N(b,40)|0;rc(H[b+32>>2]);rc(H[b+36>>2]);o=N(H[b+20>>2],H[b+16>>2]);if(!o){break b}b=H[b+24>>2];if(o>>>0>=8){q=o&-8;j=0;while(1){H[b+516>>2]=0;H[b+520>>2]=0;H[b+448>>2]=0;H[b+452>>2]=0;H[b+380>>2]=0;H[b+384>>2]=0;H[b+312>>2]=0;H[b+316>>2]=0;H[b+244>>2]=0;H[b+248>>2]=0;H[b+176>>2]=0;H[b+180>>2]=0;H[b+108>>2]=0;H[b+112>>2]=0;H[b+40>>2]=0;H[b+44>>2]=0;b=b+544|0;j=j+8|0;if((q|0)!=(j|0)){continue}break}}j=0;o=o&7;if(!o){break b}while(1){H[b+40>>2]=0;H[b+44>>2]=0;b=b+68|0;j=j+1|0;if((o|0)!=(j|0)){continue}break}}k=k+36|0;n=n+1|0;if(n>>>0<K[r+24>>2]){continue}break}}q=f;c:{if(!(I[c|0]&2)){break c}if(h>>>0<=5){Ca(i,2,4196,0);break c}if(!(I[f|0]==255&I[f+1|0]==145)){Ca(i,2,4238,0);break c}q=f+6|0;H[p+24>>2]=q}l=Ga(20);if(!l){break a}d:{if(F[a+108|0]&1){q=H[a+40>>2];o=a+44|0;h=a+40|0;break d}if(I[c+5640|0]&2){q=H[c+5168>>2];o=c+5180|0;h=c+5168|0;break d}H[p+28>>2]=(f+h|0)-q;o=p+28|0;h=p+24|0}a=H[o>>2];H[l+12>>2]=0;H[l+16>>2]=0;H[l+8>>2]=q;H[l>>2]=q;H[l+4>>2]=a+q;if(!Ta(l,1)){tc(l);a=uc(l);hb(l);a=a+q|0;b=H[h>>2];d=H[o>>2];if(I[c|0]&4){if(b+(d-a|0)>>>0<=1){Ca(i,1,4422,0);break a}if(!(I[a|0]==255&I[a+1|0]==146)){Ca(i,1,4401,0);break a}a=a+2|0}a=a-b|0;H[o>>2]=d-a;H[h>>2]=a+b;H[e>>2]=0;H[g>>2]=H[p+24>>2]-f;x=1;break a}if(H[r+24>>2]){t=r+28|0;while(1){a=H[d+36>>2];b=H[t+20>>2];e:{if(ac(t)){break e}u=b+N(a,40)|0;z=N(H[u+20>>2],H[u+16>>2]);if(!z){break e}k=H[u+24>>2];v=0;while(1){f:{g:{if(!H[k+40>>2]){a=pc(l,H[u+32>>2],v,H[d+40>>2]+1|0);break g}a=Ta(l,1)}if(!a){H[k+36>>2]=0;break f}if(!H[k+40>>2]){b=0;while(1){a=b;b=b+1|0;if(!pc(l,H[u+36>>2],v,a)){continue}break}b=H[t+28>>2];H[k+32>>2]=3;H[k+24>>2]=b;H[k+28>>2]=(b-a|0)+1}a=1;h:{if(!Ta(l,1)){break h}a=2;if(!Ta(l,1)){break h}a=Ta(l,2);if((a|0)!=3){a=a+3|0;break h}a=Ta(l,5);if((a|0)!=31){a=a+6|0;break h}a=Ta(l,7)+37|0}H[k+36>>2]=a;b=0;while(1){a=b;b=b+1|0;if(Ta(l,1)){continue}break}H[k+32>>2]=a+H[k+32>>2];i:{a=H[k+40>>2];j:{k:{if(!a){a=H[(H[c+5584>>2]+N(H[d+28>>2],1080)|0)+16>>2];if(!H[k+48>>2]){b=Ia(H[k>>2],240);if(!b){break i}H[k>>2]=b;y(b+N(H[k+48>>2],24)|0,0,240);H[k+48>>2]=10}j=H[k>>2];nb(j);b=a&4?1:a&1?10:109;a=0;break k}b=H[k>>2];n=a-1|0;j=b+N(n,24)|0;if(H[j+4>>2]!=H[j+12>>2]){break j}n=H[(H[c+5584>>2]+N(H[d+28>>2],1080)|0)+16>>2];j=H[k+48>>2];if(j>>>0<a+1>>>0){j=j+10|0;b=Ia(b,N(j,24));if(!b){break i}H[k>>2]=b;y(b+N(H[k+48>>2],24)|0,0,240);H[k+48>>2]=j;b=H[k>>2]}j=N(a,24)+b|0;nb(j);b=1;l:{if(n&4){break l}b=109;if(!(n&1)){break l}b=H[j-12>>2];b=(b|0)==1?2:(b|0)==10?2:1}}n=a;H[j+12>>2]=b}a=H[k+36>>2];if(I[(H[c+5584>>2]+N(H[d+28>>2],1080)|0)+16|0]&64){while(1){m=N(n,24);s=n?a:1;H[(m+H[k>>2]|0)+16>>2]=s;w=H[k+32>>2];j=0;b=a;if(s>>>0>=2){while(1){j=j+1|0;s=b>>>0>3;b=b>>>1|0;if(s){continue}break}}b=j+w|0;if(b>>>0>=33){H[p+16>>2]=b;Ca(i,1,15535,p+16|0);break i}j=Ta(l,b);b=H[k>>2];m=m+b|0;H[m+20>>2]=j;a=a-H[m+16>>2]|0;if((a|0)<=0){break f}j=H[(H[c+5584>>2]+N(H[d+28>>2],1080)|0)+16>>2];m=H[k+48>>2];if(m>>>0<n+2>>>0){m=m+10|0;b=Ia(b,N(m,24));if(!b){break i}H[k>>2]=b;y(b+N(H[k+48>>2],24)|0,0,240);H[k+48>>2]=m;b=H[k>>2]}n=n+1|0;b=b+N(n,24)|0;nb(b);if(j&4){H[b+12>>2]=1;continue}if(j&1){j=b;b=H[b-12>>2];H[j+12>>2]=(b|0)==1?2:(b|0)==10?2:1}else{H[b+12>>2]=109}continue}}while(1){m=N(n,24);j=m+H[k>>2]|0;b=H[j+12>>2]-H[j+4>>2]|0;b=(a|0)>(b|0)?b:a;H[j+16>>2]=b;s=H[k+32>>2];j=0;if(b>>>0>=2){while(1){j=j+1|0;w=b>>>0>3;b=b>>>1|0;if(w){continue}break}}b=j+s|0;if(b>>>0>=33){H[p>>2]=b;Ca(i,1,15535,p);break i}j=Ta(l,b);b=H[k>>2];m=m+b|0;H[m+20>>2]=j;a=a-H[m+16>>2]|0;if((a|0)<=0){break f}j=H[(H[c+5584>>2]+N(H[d+28>>2],1080)|0)+16>>2];m=H[k+48>>2];if(m>>>0<n+2>>>0){m=m+10|0;b=Ia(b,N(m,24));if(!b){break i}H[k>>2]=b;y(b+N(H[k+48>>2],24)|0,0,240);H[k+48>>2]=m;b=H[k>>2]}n=n+1|0;b=b+N(n,24)|0;nb(b);if(j&4){H[b+12>>2]=1;continue}if(j&1){j=b;b=H[b-12>>2];H[j+12>>2]=(b|0)==1?2:(b|0)==10?2:1}else{H[b+12>>2]=109}continue}}hb(l);break a}k=k+68|0;v=v+1|0;if((z|0)!=(v|0)){continue}break}}t=t+36|0;A=A+1|0;if(A>>>0<K[r+24>>2]){continue}break}}if(!tc(l)){hb(l);break a}a=uc(l);hb(l);b=a+q|0;a=H[h>>2];if(I[c|0]&4){if(a+(H[o>>2]-b|0)>>>0<=1){Ca(i,1,4422,0);break a}if(!(I[b|0]==255&I[b+1|0]==146)){Ca(i,1,4401,0);break a}b=b+2|0}if((a|0)==(b|0)){break a}H[o>>2]=H[o>>2]+(a-b|0);H[h>>2]=b;x=1;H[e>>2]=1;H[g>>2]=H[p+24>>2]-f}oa=p+32|0;return x}function Eb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;o=N(c,5);j=(c<<2)+b|0;e=H[a>>2];f=H[a+12>>2]<<5;h=e+f|0;l=e-f|0;e=H[a+16>>2];k=H[a+28>>2];i=H[a+20>>2];q=H[a+8>>2];a:{b:{if(h&15|(b&15|d>>>0<8)){if(e>>>0>=i>>>0){break a}c:{switch(d-1|0){case 1:f=e+1|0;if(i-e&1){g=h+(e<<6)|0;e=(e<<2)+b|0;L[g>>2]=L[e>>2];L[g+4>>2]=L[e+(c<<2)>>2];e=f}if((f|0)==(i|0)){break a}while(1){f=h+(e<<6)|0;g=(e<<2)+b|0;L[f>>2]=L[g>>2];r=f;f=c<<2;L[r+4>>2]=L[f+g>>2];g=e+1|0;j=h+(g<<6)|0;g=(g<<2)+b|0;L[j>>2]=L[g>>2];L[j+4>>2]=L[f+g>>2];e=e+2|0;if((i|0)!=(e|0)){continue}break};break a;case 0:break c;default:break b}}f=e;j=i-e&3;if(j){while(1){L[h+(f<<6)>>2]=L[(f<<2)+b>>2];f=f+1|0;g=g+1|0;if((j|0)!=(g|0)){continue}break}}if(e-i>>>0>4294967292){break a}while(1){L[h+(f<<6)>>2]=L[(f<<2)+b>>2];e=f+1|0;L[h+(e<<6)>>2]=L[(e<<2)+b>>2];e=f+2|0;L[h+(e<<6)>>2]=L[(e<<2)+b>>2];e=f+3|0;L[h+(e<<6)>>2]=L[(e<<2)+b>>2];f=f+4|0;if((i|0)!=(f|0)){continue}break}break a}if(e>>>0>=i>>>0){break a}n=c<<4;m=N(c,12);s=c<<3;while(1){f=h+(e<<6)|0;g=(e<<2)+b|0;L[f>>2]=L[g>>2];p=c<<2;L[f+4>>2]=L[p+g>>2];L[f+8>>2]=L[g+s>>2];L[f+12>>2]=L[g+m>>2];L[f+16>>2]=L[g+n>>2];g=e+o<<2;L[f+20>>2]=L[g+b>>2];g=g+j|0;L[f+24>>2]=L[g>>2];L[f+28>>2]=L[g+p>>2];e=e+1|0;if((i|0)!=(e|0)){continue}break}break a}n=c<<4;m=N(c,12);s=c<<3;p=(d|0)==5;r=(d|0)==7;while(1){f=h+(e<<6)|0;g=(e<<2)+b|0;L[f>>2]=L[g>>2];t=c<<2;L[f+4>>2]=L[g+t>>2];L[f+8>>2]=L[g+s>>2];d:{if((d|0)==3){break d}L[f+12>>2]=L[g+m>>2];if((d|0)==4){break d}L[f+16>>2]=L[g+n>>2];if(p){break d}g=e+o<<2;L[f+20>>2]=L[g+b>>2];if((d|0)==6){break d}g=g+j|0;L[f+24>>2]=L[g>>2];if(r){break d}L[f+28>>2]=L[g+t>>2]}e=e+1|0;if((i|0)!=(e|0)){continue}break}}b=(q<<2)+b|0;i=b+(c<<2)|0;e=H[a+24>>2];h=l+32|0;e:{if(h&15|(b&15|d>>>0<8)){if(e>>>0>=k>>>0){break e}f:{switch(d-1|0){case 1:a=e+1|0;if(k-e&1){d=h+(e<<6)|0;e=b+(e<<2)|0;L[d>>2]=L[e>>2];L[d+4>>2]=L[e+(c<<2)>>2];e=a}if((a|0)==(k|0)){break e}while(1){a=h+(e<<6)|0;d=b+(e<<2)|0;L[a>>2]=L[d>>2];f=a;a=c<<2;L[f+4>>2]=L[a+d>>2];d=e+1|0;f=h+(d<<6)|0;d=b+(d<<2)|0;L[f>>2]=L[d>>2];L[f+4>>2]=L[a+d>>2];e=e+2|0;if((k|0)!=(e|0)){continue}break};break e;case 0:f=e;a=k-e&3;if(a){g=0;while(1){L[h+(f<<6)>>2]=L[b+(f<<2)>>2];f=f+1|0;g=g+1|0;if((a|0)!=(g|0)){continue}break}}if(e-k>>>0>4294967292){break e}while(1){L[h+(f<<6)>>2]=L[b+(f<<2)>>2];a=f+1|0;L[h+(a<<6)>>2]=L[b+(a<<2)>>2];a=f+2|0;L[h+(a<<6)>>2]=L[b+(a<<2)>>2];a=f+3|0;L[h+(a<<6)>>2]=L[b+(a<<2)>>2];f=f+4|0;if((k|0)!=(f|0)){continue}break};break e;default:break f}}g=c<<4;j=N(c,12);l=c<<3;q=(d|0)==5;n=(d|0)==7;while(1){a=h+(e<<6)|0;f=b+(e<<2)|0;L[a>>2]=L[f>>2];m=c<<2;L[a+4>>2]=L[m+f>>2];L[a+8>>2]=L[f+l>>2];g:{if((d|0)==3){break g}L[a+12>>2]=L[f+j>>2];if((d|0)==4){break g}L[a+16>>2]=L[f+g>>2];if(q){break g}f=e+o<<2;L[a+20>>2]=L[f+b>>2];if((d|0)==6){break g}f=f+i|0;L[a+24>>2]=L[f>>2];if(n){break g}L[a+28>>2]=L[f+m>>2]}e=e+1|0;if((k|0)!=(e|0)){continue}break}break e}if(e>>>0>=k>>>0){break e}f=c<<4;g=N(c,12);j=c<<3;while(1){a=h+(e<<6)|0;d=b+(e<<2)|0;L[a>>2]=L[d>>2];l=c<<2;L[a+4>>2]=L[l+d>>2];L[a+8>>2]=L[d+j>>2];L[a+12>>2]=L[d+g>>2];L[a+16>>2]=L[d+f>>2];d=e+o<<2;L[a+20>>2]=L[d+b>>2];d=d+i|0;L[a+24>>2]=L[d>>2];L[a+28>>2]=L[d+l>>2];e=e+1|0;if((k|0)!=(e|0)){continue}break}}}function Tb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;d=oa-176|0;oa=d;a:{if(b&384){Pa(5943,11,c);break a}b:{if(!(b&1)){break b}e=H[a+96>>2];if(!e){break b}f=oa-80|0;oa=f;Pa(1792,13,c);F[f+79|0]=0;F[f+78|0]=9;g=H[e+4>>2];H[f+68>>2]=H[e>>2];H[f+72>>2]=g;j=f+78|0;H[f+64>>2]=j;Ha(c,7520,f- -64|0);g=H[e+12>>2];H[f+52>>2]=H[e+8>>2];H[f+56>>2]=g;H[f+48>>2]=j;Ha(c,7503,f+48|0);H[f+36>>2]=H[e+16>>2];H[f+32>>2]=j;Ha(c,7277,f+32|0);if(!(!H[e+24>>2]|!H[e+16>>2])){while(1){l=f+78|0;H[f+16>>2]=l;H[f+20>>2]=m;Ha(c,1824,f+16|0);j=H[e+24>>2];g=oa-48|0;oa=g;F[g+46|0]=9;F[g+47|0]=0;F[g+45|0]=9;n=N(m,52)+j|0;j=H[n+4>>2];H[g+36>>2]=H[n>>2];H[g+40>>2]=j;j=g+45|0;H[g+32>>2]=j;Ha(c,7209,g+32|0);H[g+20>>2]=H[n+24>>2];H[g+16>>2]=j;Ha(c,7455,g+16|0);H[g+4>>2]=H[n+32>>2];H[g>>2]=j;Ha(c,7428,g);oa=g+48|0;H[f>>2]=l;Ha(c,1702,f);m=m+1|0;if(m>>>0<K[e+16>>2]){continue}break}}Pa(1710,2,c);oa=f+80|0}if(!(!(b&2)|!H[a+96>>2])){Pa(1931,36,c);e=H[a+112>>2];H[d+160>>2]=H[a+108>>2];H[d+164>>2]=e;Ha(c,2425,d+160|0);e=H[a+120>>2];H[d+144>>2]=H[a+116>>2];H[d+148>>2]=e;Ha(c,2391,d+144|0);e=H[a+132>>2];H[d+128>>2]=H[a+128>>2];H[d+132>>2]=e;Ha(c,2409,d+128|0);Sb(H[a+12>>2],H[H[a+96>>2]+16>>2],c);Pa(1710,2,c)}c:{if(!(b&8)|!H[a+96>>2]){break c}e=N(H[a+128>>2],H[a+132>>2]);if(!e){break c}h=H[a+180>>2];while(1){Sb(h,H[H[a+96>>2]+16>>2],c);h=h+5644|0;k=k+1|0;if((e|0)!=(k|0)){continue}break}}if(!(b&16)){break a}i=H[a+224>>2];Pa(1893,37,c);e=H[i>>2];b=H[i+4>>2];a=H[i+12>>2];H[d+120>>2]=H[i+8>>2];H[d+124>>2]=a;H[d+112>>2]=e;H[d+116>>2]=b;Ha(c,5730,d+112|0);Pa(1875,17,c);if(!(!H[i+28>>2]|!H[i+24>>2])){h=0;while(1){a=H[i+28>>2]+N(h,24)|0;g=J[a>>1];e=H[a+8>>2];b=H[a+12>>2];H[d+96>>2]=H[a+16>>2];H[d+88>>2]=e;H[d+92>>2]=b;H[d+80>>2]=g;Ha(c,7397,d+80|0);h=h+1|0;if(h>>>0<K[i+24>>2]){continue}break}}Pa(1708,4,c);j=H[i+40>>2];d:{if(!j){break d}g=H[i+36>>2];if(!g){break d}k=0;h=0;while(1){a=j+N(h,40)|0;e=H[a+4>>2];e:{if(!e){break e}l=H[a+16>>2];if(!l){break e}b=H[l>>2];a=H[l+4>>2];if((a|0)<0){a=1}else{a=!b&(a|0)<=0}if(a|(H[l+8>>2]|H[l+12>>2])){break e}if(Kc(1439)){break d}}k=e+k|0;h=h+1|0;if((g|0)!=(h|0)){continue}break}if(!k){break d}Pa(1858,16,c);if(H[i+36>>2]){k=H[i+40>>2];n=0;while(1){f=N(n,40);l=H[(f+k|0)+4>>2];H[d+68>>2]=l;H[d+64>>2]=n;Ha(c,7467,d- -64|0);k=H[i+40>>2];f:{if(!l){break f}h=0;if(!H[(f+k|0)+16>>2]){break f}while(1){m=H[(f+H[i+40>>2]|0)+16>>2]+N(h,24)|0;j=H[m>>2];g=H[m+4>>2];e=H[m+8>>2];b=H[m+12>>2];a=H[m+20>>2];H[d+56>>2]=H[m+16>>2];H[d+60>>2]=a;H[d+48>>2]=e;H[d+52>>2]=b;H[d+40>>2]=j;H[d+44>>2]=g;H[d+32>>2]=h;Ha(c,10938,d+32|0);h=h+1|0;if((l|0)!=(h|0)){continue}break}k=H[i+40>>2]}a=f+k|0;g:{if(!H[a+24>>2]){break g}h=0;if(!H[a+20>>2]){break g}while(1){a=H[(f+k|0)+24>>2]+N(h,24)|0;g=J[a>>1];e=H[a+8>>2];b=H[a+12>>2];H[d+16>>2]=H[a+16>>2];H[d+8>>2]=e;H[d+12>>2]=b;H[d>>2]=g;Ha(c,7397,d);h=h+1|0;k=H[i+40>>2];if(h>>>0<K[(f+k|0)+20>>2]){continue}break}}n=n+1|0;if(n>>>0<K[i+36>>2]){continue}break}}Pa(1708,4,c)}Pa(1710,2,c)}oa=d+176|0}function De(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=oa-128|0;oa=e;H[e+120>>2]=0;a:{if((c|0)!=8){Ca(d,1,4047,0);Ca(d,1,4047,0);break a}Ea(b,a+228|0,2);Ea(b+2|0,e+124|0,4);Ea(b+6|0,e+116|0,1);Ea(b+7|0,e+120|0,1);c=H[a+228>>2];i=H[a+128>>2];if(c>>>0>=N(i,H[a+132>>2])>>>0){H[e+112>>2]=c;Ca(d,1,7843,e+112|0);break a}h=H[a+180>>2]+N(c,5644)|0;j=(c>>>0)/(i>>>0)|0;b=H[e+116>>2];b:{f=H[a+44>>2];if((f|0)>=0&(c|0)!=(f|0)){break b}f=H[h+5588>>2]+1|0;if((f|0)==(b|0)){break b}H[e+104>>2]=f;H[e+100>>2]=b;H[e+96>>2]=c;Ca(d,1,7867,e+96|0);f=0;break a}H[h+5588>>2]=b;c:{b=H[e+124>>2];if(b-1>>>0<=12){if((b|0)!=12){break c}H[e+64>>2]=12;Ca(d,2,11864,e- -64|0);b=H[e+124>>2]}if(!b){Ca(d,4,10695,0);H[a+56>>2]=1}d:{e:{f:{g:{g=H[h+5592>>2];if(g){b=H[e+116>>2];if(b>>>0<g>>>0){break g}H[e+52>>2]=g;H[e+48>>2]=b;Ca(d,1,5150,e+48|0);H[a+56>>2]=1;f=0;break a}f=H[e+120>>2];if(f){break f}break d}f=H[e+120>>2];if(!f){break e}}g=(I[a+92|0]>>>4&1)+f|0;H[e+120>>2]=g;b=H[e+116>>2];f=H[h+5592>>2];if(b>>>0>f-1>>>0){H[e+20>>2]=f;H[e+16>>2]=b;Ca(d,1,5051,e+16|0);H[a+56>>2]=1;f=0;break a}if(b>>>0>=g>>>0){H[e+36>>2]=g;H[e+32>>2]=b;Ca(d,1,5250,e+32|0);H[a+56>>2]=1;f=0;break a}H[h+5592>>2]=g}if((H[e+116>>2]+1|0)!=(g|0)){break d}F[a+92|0]=I[a+92|0]|1}b=H[e+124>>2];H[a+8>>2]=16;H[a+24>>2]=H[a+56>>2]?0:b-12|0;f=H[a+44>>2];h:{if((f|0)==-1){f=4;b=c-N(j,i)|0;if(!(b>>>0<K[a+28>>2]|b>>>0>=K[a+36>>2]|j>>>0<K[a+32>>2])){f=j>>>0>=K[a+40>>2]?4:0}F[a+92|0]=I[a+92|0]&251|f;b=H[a+228>>2];break h}b=H[a+228>>2];F[a+92|0]=I[a+92|0]&251|((f|0)!=(b|0)?4:0)}c=H[H[a+224>>2]+40>>2]+N(b,40)|0;H[c>>2]=b;H[c+12>>2]=H[e+116>>2];f=H[e+120>>2];if(!H[a+76>>2]){if(K[c+4>>2]>=f>>>0){f=1;break a}H[e>>2]=b;Ca(d,2,1612,e);H[a+76>>2]=1;f=H[e+120>>2]}b=H[a+228>>2];c=H[H[a+224>>2]+40>>2];if(f){b=N(b,40)+c|0;H[b+4>>2]=f;c=H[e+120>>2];H[b+8>>2]=c;b=H[b+16>>2];if(!b){b=Fa(c,24);H[(H[H[a+224>>2]+40>>2]+N(H[a+228>>2],40)|0)+16>>2]=b;if(b){f=1;break a}f=0;Ca(d,1,6947,0);break a}b=Ia(b,N(c,24));c=H[H[a+224>>2]+40>>2]+N(H[a+228>>2],40)|0;if(!b){Da(H[c+16>>2]);f=0;H[(H[H[a+224>>2]+40>>2]+N(H[a+228>>2],40)|0)+16>>2]=0;Ca(d,1,6947,0);break a}H[c+16>>2]=b;f=1;break a}i:{f=N(b,40)+c|0;g=H[f+16>>2];if(g){break i}H[f+8>>2]=10;g=Fa(10,24);c=H[H[a+224>>2]+40>>2];b=H[a+228>>2];H[(c+N(b,40)|0)+16>>2]=g;if(g){break i}f=0;H[(N(b,40)+c|0)+8>>2]=0;Ca(d,1,6947,0);break a}b=N(b,40)+c|0;c=H[e+116>>2];if(K[b+8>>2]>c>>>0){f=1;break a}f=1;h=b;b=c+1|0;H[h+8>>2]=b;b=Ia(g,N(b,24));c=H[H[a+224>>2]+40>>2]+N(H[a+228>>2],40)|0;if(!b){Da(H[c+16>>2]);f=0;a=H[H[a+224>>2]+40>>2]+N(H[a+228>>2],40)|0;H[a+8>>2]=0;H[a+16>>2]=0;Ca(d,1,6947,0);break a}H[c+16>>2]=b;break a}H[e+80>>2]=b;Ca(d,1,12133,e+80|0);f=0}oa=e+128|0;return f|0}function qb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;g=H[a+8>>2];e=g+H[a+4>>2]|0;a:{if(!H[a+12>>2]){if((e|0)<2|(d|0)<=0){break a}q=e&2147483644;m=e&3;r=e&1;s=g+1|0;h=H[a>>2];o=h+(e<<2)|0;t=e-4>>>1|0;a=e-1|0;u=h+(a<<2)|0;v=N(c,g)<<2;l=e>>>0<4;w=N(a>>>1|0,c)<<2;while(1){g=H[b+v>>2];e=H[b>>2]-(g+1>>1)|0;i=0;a=0;if(!l){while(1){j=a+1|0;x=H[(N(j,c)<<2)+b>>2];f=H[(N(a+s|0,c)<<2)+b>>2];p=h+(i<<2)|0;H[p>>2]=e;k=e;e=x-((g+f|0)+2>>2)|0;H[p+4>>2]=(k+e>>1)+g;i=i+2|0;k=(a|0)!=(t|0);g=f;a=j;if(k){continue}break}}H[h+(i<<2)>>2]=e;if(r){a=H[b+w>>2]-(g+1>>1)|0;H[u>>2]=a;e=a+e>>1;a=-8}else{a=-4}H[a+o>>2]=e+g;e=0;a=0;g=0;if(!l){while(1){H[(N(a,c)<<2)+b>>2]=H[h+(a<<2)>>2];f=a|1;H[(N(f,c)<<2)+b>>2]=H[h+(f<<2)>>2];f=a|2;H[(N(f,c)<<2)+b>>2]=H[h+(f<<2)>>2];f=a|3;H[(N(f,c)<<2)+b>>2]=H[h+(f<<2)>>2];a=a+4|0;g=g+4|0;if((q|0)!=(g|0)){continue}break}}if(m){while(1){H[(N(a,c)<<2)+b>>2]=H[h+(a<<2)>>2];a=a+1|0;e=e+1|0;if((m|0)!=(e|0)){continue}break}}b=b+4|0;n=n+1|0;if((n|0)!=(d|0)){continue}break}break a}b:{switch(e-1|0){case 0:if((d|0)<=0){break a}if(d>>>0>=4){c=d&2147483644;a=0;while(1){H[b>>2]=H[b>>2]/2;H[b+4>>2]=H[b+4>>2]/2;H[b+8>>2]=H[b+8>>2]/2;H[b+12>>2]=H[b+12>>2]/2;b=b+16|0;a=a+4|0;if((c|0)!=(a|0)){continue}break}}c=d&3;if(!c){break a}a=0;while(1){H[b>>2]=H[b>>2]/2;b=b+4|0;a=a+1|0;if((c|0)!=(a|0)){continue}break};break a;case 1:if((d|0)<=0){break a}a=H[a>>2];e=0;g=N(c,g)<<2;while(1){f=b+g|0;j=H[b>>2]-(H[f>>2]+1>>1)|0;H[a+4>>2]=j;f=j+H[f>>2]|0;H[a>>2]=f;H[b>>2]=f;H[(c<<2)+b>>2]=H[a+4>>2];b=b+4|0;e=e+1|0;if((e|0)!=(d|0)){continue}break};break a;default:break b}}if((e|0)<3|(d|0)<=0){break a}q=e&2147483644;m=e&3;h=H[a>>2];r=(h+(e<<2)|0)-4|0;a=e-2|0;s=h+(a<<2)|0;o=e&1;f=!o;t=((e-f|0)-4>>>1|0)+1|0;u=N(c,g)<<2;v=a-f>>>0<2;w=N((e>>>1|0)-1|0,c)<<2;x=e-1>>>0<3;while(1){l=b+u|0;g=H[l+(c<<2)>>2];a=H[l>>2];e=H[b>>2]-((g+a|0)+2>>2)|0;H[h>>2]=e+a;i=1;a=1;if(!v){while(1){p=H[(N(a,c)<<2)+b>>2];j=a+1|0;f=H[l+(N(j,c)<<2)>>2];y=h+(i<<2)|0;H[y>>2]=e;k=e;e=p-((g+f|0)+2>>2)|0;H[y+4>>2]=(k+e>>1)+g;i=i+2|0;k=(a|0)!=(t|0);a=j;g=f;if(k){continue}break}}H[h+(i<<2)>>2]=e;c:{if(!o){a=H[b+w>>2]-(g+1>>1)|0;H[s>>2]=(e+a>>1)+g;break c}a=e+g|0}H[r>>2]=a;e=0;a=0;g=0;if(!x){while(1){H[(N(a,c)<<2)+b>>2]=H[h+(a<<2)>>2];f=a|1;H[(N(f,c)<<2)+b>>2]=H[h+(f<<2)>>2];f=a|2;H[(N(f,c)<<2)+b>>2]=H[h+(f<<2)>>2];f=a|3;H[(N(f,c)<<2)+b>>2]=H[h+(f<<2)>>2];a=a+4|0;g=g+4|0;if((q|0)!=(g|0)){continue}break}}if(m){while(1){H[(N(a,c)<<2)+b>>2]=H[h+(a<<2)>>2];a=a+1|0;e=e+1|0;if((m|0)!=(e|0)){continue}break}}b=b+4|0;n=n+1|0;if((n|0)!=(d|0)){continue}break}}}function Nb(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0;r=oa-16|0;oa=r;a:{if(!c){Ca(d,1,11629,0);break a}t=H[c+16>>2];i=H[a+96>>2];if(t>>>0<K[i+16>>2]){Ca(d,1,10570,0);break a}f=H[a+128>>2];g=N(f,H[a+132>>2]);if(g>>>0<=e>>>0){H[r>>2]=e;H[r+4>>2]=g-1;Ca(d,1,16362,r);g=0;break a}j=(e>>>0)/(f>>>0)|0;f=e-N(j,f)|0;h=H[a+108>>2]+N(f,H[a+116>>2])|0;H[c>>2]=h;g=H[i>>2];l=g>>>0<h>>>0?h:g;H[c>>2]=l;f=H[a+108>>2]+N(H[a+116>>2],f+1|0)|0;H[c+8>>2]=f;g=H[H[a+96>>2]+8>>2];f=f>>>0<g>>>0?f:g;H[c+8>>2]=f;i=H[a+112>>2]+N(j,H[a+120>>2])|0;H[c+4>>2]=i;g=H[H[a+96>>2]+4>>2];h=g>>>0<i>>>0?i:g;H[c+4>>2]=h;i=H[a+112>>2]+N(H[a+120>>2],j+1|0)|0;H[c+12>>2]=i;g=H[H[a+96>>2]+12>>2];g=g>>>0>i>>>0?i:g;H[c+12>>2]=g;i=H[a+96>>2];m=H[i+16>>2];if(m){u=g-1|0;v=(g>>31)-!g|0;w=f-1|0;x=(f>>31)-!f|0;y=h-1|0;z=0-!h|0;A=l-1|0;B=0-!l|0;C=H[i+24>>2];g=H[c+24>>2];while(1){i=H[(C+N(q,52)|0)+40>>2];H[g+40>>2]=i;f=B;l=H[g>>2];h=l+A|0;f=l>>>0>h>>>0?f+1|0:f;n=Ke(h,f,l,0);H[g+16>>2]=n;f=z;h=H[g+4>>2];j=h+y|0;f=h>>>0>j>>>0?f+1|0:f;f=Ke(j,f,h,0);H[g+20>>2]=f;j=f;p=i;f=i&31;if((i&63)>>>0>=32){k=-1<<f;f=0}else{o=(1<<f)-1&-1>>>32-f;f=-1<<f;k=o|f}i=f;o=i-j|0;f=k;k=f-((j>>31)+(i>>>0<j>>>0)|0)|0;j=o;o=p&31;if((p&63)>>>0>=32){o=k>>o}else{o=((1<<o)-1&k)<<32-o|j>>>o}k=h>>31;s=k+v|0;j=h+u|0;s=j>>>0<h>>>0?s+1|0:s;j=Je(j,s,h,k);h=i-j|0;j=f-((j>>31)+(i>>>0<j>>>0)|0)|0;k=p&31;if((p&63)>>>0>=32){j=j>>k}else{j=((1<<k)-1&j)<<32-k|h>>>k}H[g+12>>2]=o-j;j=f-((n>>31)+(i>>>0<n>>>0)|0)|0;h=i-n|0;n=p&31;if((p&63)>>>0>=32){n=j>>n}else{n=((1<<n)-1&j)<<32-n|h>>>n}j=l>>31;k=j+x|0;h=l+w|0;k=h>>>0<l>>>0?k+1|0:k;l=Je(h,k,l,j);h=i-l|0;i=f-((l>>31)+(i>>>0<l>>>0)|0)|0;f=h;h=p&31;if((p&63)>>>0>=32){f=i>>h}else{f=((1<<h)-1&i)<<32-h|f>>>h}H[g+8>>2]=n-f;g=g+52|0;q=q+1|0;if((q|0)!=(m|0)){continue}break}}if(m>>>0<t>>>0){g=H[c+24>>2];while(1){f=N(m,52);Da(H[(f+g|0)+44>>2]);g=H[c+24>>2];H[(f+g|0)+44>>2]=0;m=m+1|0;if(m>>>0<K[c+16>>2]){continue}break}H[c+16>>2]=H[H[a+96>>2]+16>>2]}g=H[a+100>>2];if(g){Va(g)}f=zb();H[a+100>>2]=f;g=0;if(!f){break a}Lb(c,f);H[a+44>>2]=e;if(!Ya(H[a+216>>2],24,d)){break a}h=H[a+216>>2];e=H[h>>2];m=H[h+8>>2];b:{if(e){g=1;i=e&1;if((e|0)==1){e=0}else{f=e&-2;q=0;while(1){e=0;c:{if(!g){break c}e=0;if(!(sa[H[m>>2]](a,b,d)|0)){break c}e=(sa[H[m+4>>2]](a,b,d)|0)!=0}g=e;m=m+8|0;q=q+2|0;if((f|0)!=(q|0)){continue}break}e=!g}g=i?0:g;if(!(e|!i)){g=(sa[H[m>>2]](a,b,d)|0)!=0}Qa(h);if(g){break b}Va(H[a+96>>2]);g=0;H[a+96>>2]=0;break a}Qa(h)}g=Ob(a,c)}oa=r+16|0;return g|0}function hc(a,b,c,d,e,f,g){var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a:{n=N(e,3);h=H[b>>2]>>>n|0;if(h&2097168){break a}h=h&495;if(!h){break a}o=a+28|0;l=o+(I[h+H[a+108>>2]|0]<<2)|0;H[a+104>>2]=l;k=H[l>>2];i=H[k>>2];h=H[a+4>>2]-i|0;H[a+4>>2]=h;j=H[a>>2];b:{if(j>>>16>>>0<i>>>0){m=H[k+4>>2];H[a+4>>2]=i;h=h>>>0<i>>>0;H[l>>2]=H[k+(h?8:12)>>2];k=h?m:!m;h=H[a+8>>2];while(1){c:{if(h){break c}h=H[a+16>>2];m=h+1|0;l=I[h+1|0];if(I[h|0]==255){if(l>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;j=j+65280|0;h=8;break c}H[a+16>>2]=m;j=(l<<9)+j|0;h=7;break c}H[a+16>>2]=m;h=8;j=(l<<8)+j|0}h=h-1|0;H[a+8>>2]=h;j=j<<1;H[a>>2]=j;i=i<<1;H[a+4>>2]=i;if(i>>>0<32768){continue}break}h=i;break b}j=j-(i<<16)|0;H[a>>2]=j;if(!(h&32768)){m=H[k+4>>2];i=h>>>0<i>>>0;H[l>>2]=H[k+(i?12:8)>>2];k=i?!m:m;i=H[a+8>>2];while(1){d:{if(i){break d}i=H[a+16>>2];m=i+1|0;l=I[i+1|0];if(I[i|0]==255){if(l>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;j=j+65280|0;i=8;break d}H[a+16>>2]=m;j=(l<<9)+j|0;i=7;break d}H[a+16>>2]=m;i=8;j=(l<<8)+j|0}i=i-1|0;H[a+8>>2]=i;j=j<<1;H[a>>2]=j;h=h<<1;H[a+4>>2]=h;if(h>>>0<32768){continue}break}break b}k=H[k+4>>2]}e:{if(!k){break e}p=b-4|0;i=H[b>>2];k=H[b+4>>2]>>>n+17&4|(H[p>>2]>>>n+19&1|(i>>>n+16&64|i>>>n&170|i>>>(e?n+12|0:14)&16));m=o+(I[k+24384|0]<<2)|0;H[a+104>>2]=m;l=H[m>>2];i=H[l>>2];h=h-i|0;H[a+4>>2]=h;o=I[k+24640|0];f:{if(j>>>16>>>0<i>>>0){k=H[l+4>>2];H[a+4>>2]=i;h=h>>>0<i>>>0;H[m>>2]=H[l+(h?8:12)>>2];l=h?k:!k;h=H[a+8>>2];while(1){g:{if(h){break g}h=H[a+16>>2];m=h+1|0;k=I[h+1|0];if(I[h|0]==255){if(k>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;j=j+65280|0;h=8;break g}H[a+16>>2]=m;j=(k<<9)+j|0;h=7;break g}H[a+16>>2]=m;h=8;j=(k<<8)+j|0}h=h-1|0;H[a+8>>2]=h;j=j<<1;H[a>>2]=j;i=i<<1;H[a+4>>2]=i;if(i>>>0<32768){continue}break}break f}k=j-(i<<16)|0;H[a>>2]=k;if(!(h&32768)){j=H[l+4>>2];i=h>>>0<i>>>0;H[m>>2]=H[l+(i?12:8)>>2];l=i?!j:j;j=H[a+8>>2];while(1){h:{if(j){break h}j=H[a+16>>2];m=j+1|0;i=I[j+1|0];if(I[j|0]==255){if(i>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;k=k+65280|0;j=8;break h}H[a+16>>2]=m;k=(i<<9)+k|0;j=7;break h}H[a+16>>2]=m;j=8;k=(i<<8)+k|0}j=j-1|0;H[a+8>>2]=j;k=k<<1;H[a>>2]=k;h=h<<1;H[a+4>>2]=h;if(h>>>0<32768){continue}break}break f}l=H[l+4>>2]}H[c>>2]=(l|0)==(o|0)?d:0-d|0;H[p>>2]=H[p>>2]|32<<n;c=l^o;H[b>>2]=H[b>>2]|(c<<19|16)<<n;H[b+4>>2]=H[b+4>>2]|8<<n;if(!(e|g)){a=b-(f<<2)|0;H[a+4>>2]=H[a+4>>2]|32768;H[a>>2]=H[a>>2]|c<<31|65536;a=a-4|0;H[a>>2]=H[a>>2]|131072}if((e|0)!=3){break e}a=(f<<2)+b|0;H[a+4>>2]=H[a+4>>2]|1;H[a>>2]=H[a>>2]|c<<18|2;a=a-4|0;H[a>>2]=H[a>>2]|4}H[b>>2]=H[b>>2]|2097152<<n}}function Wb(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=oa-208|0;oa=h;j=H[a+96>>2];a:{if(!(!(H[H[a+180>>2]+5596>>2]?H[a+128>>2]!=1|H[a+132>>2]!=1:1)|H[a+8>>2]==8)){Ca(g,1,10614,0);break a}m=H[b+16>>2];b:{if(!m){break b}k=H[a+184>>2];l=H[b+24>>2];if(m>>>0>=8){p=m&-8;while(1){H[(N(i,52)+l|0)+40>>2]=k;H[(N(i|1,52)+l|0)+40>>2]=k;H[(N(i|2,52)+l|0)+40>>2]=k;H[(N(i|3,52)+l|0)+40>>2]=k;H[(N(i|4,52)+l|0)+40>>2]=k;H[(N(i|5,52)+l|0)+40>>2]=k;H[(N(i|6,52)+l|0)+40>>2]=k;H[(N(i|7,52)+l|0)+40>>2]=k;i=i+8|0;n=n+8|0;if((p|0)!=(n|0)){continue}break}}m=m&7;if(!m){break b}while(1){H[(N(i,52)+l|0)+40>>2]=k;i=i+1|0;o=o+1|0;if((m|0)!=(o|0)){continue}break}}if(!(c|d|e|f)){Ca(g,4,6344,0);H[a+28>>2]=0;H[a+32>>2]=0;c=H[a+132>>2];H[a+36>>2]=H[a+128>>2];H[a+40>>2]=c;H[b>>2]=H[j>>2];H[b+4>>2]=H[j+4>>2];H[b+8>>2]=H[j+8>>2];H[b+12>>2]=H[j+12>>2];i=Ab(b,g);break a}if((c|0)<0){H[h>>2]=c;Ca(g,1,12602,h);i=0;break a}i=H[j+8>>2];if(i>>>0<c>>>0){H[h+20>>2]=i;H[h+16>>2]=c;Ca(g,1,13070,h+16|0);i=0;break a}i=H[j>>2];c:{if(i>>>0>c>>>0){H[h+196>>2]=i;H[h+192>>2]=c;Ca(g,2,13422,h+192|0);H[a+28>>2]=0;c=H[j>>2];break c}H[a+28>>2]=(c-H[a+108>>2]>>>0)/K[a+116>>2]}H[b>>2]=c;if((d|0)<0){H[h+32>>2]=d;Ca(g,1,12538,h+32|0);i=0;break a}c=H[j+12>>2];if(c>>>0<d>>>0){H[h+52>>2]=c;H[h+48>>2]=d;Ca(g,1,12897,h+48|0);i=0;break a}c=H[j+4>>2];d:{if(c>>>0>d>>>0){H[h+180>>2]=c;H[h+176>>2]=d;Ca(g,2,13247,h+176|0);H[a+32>>2]=0;d=H[j+4>>2];break d}H[a+32>>2]=(d-H[a+112>>2]>>>0)/K[a+120>>2]}H[b+4>>2]=d;i=0;if((e|0)<=0){H[h+64>>2]=e;Ca(g,1,12472,h- -64|0);break a}c=H[j>>2];if(c>>>0>e>>>0){H[h+84>>2]=c;H[h+80>>2]=e;Ca(g,1,13333,h+80|0);break a}c=H[j+8>>2];e:{if(c>>>0<e>>>0){H[h+164>>2]=c;H[h+160>>2]=e;Ca(g,2,12982,h+160|0);H[a+36>>2]=H[a+128>>2];e=H[j+8>>2];break e}k=0;d=e-H[a+108>>2]|0;l=d;c=H[a+116>>2];d=d+c|0;k=l>>>0>d>>>0?1:k;q=a,r=Ke(d-1|0,k-!d|0,c,0),H[q+36>>2]=r}H[b+8>>2]=e;if((f|0)<=0){H[h+96>>2]=f;Ca(g,1,12405,h+96|0);break a}c=H[j+4>>2];if(c>>>0>f>>>0){H[h+116>>2]=c;H[h+112>>2]=f;Ca(g,1,13157,h+112|0);break a}c=H[j+12>>2];f:{if(c>>>0<f>>>0){H[h+148>>2]=c;H[h+144>>2]=f;Ca(g,2,12808,h+144|0);H[a+40>>2]=H[a+132>>2];f=H[j+12>>2];break f}e=0;d=f-H[a+112>>2]|0;l=d;c=H[a+120>>2];d=d+c|0;e=l>>>0>d>>>0?1:e;q=a,r=Ke(d-1|0,e-!d|0,c,0),H[q+40>>2]=r}H[b+12>>2]=f;F[a+92|0]=I[a+92|0]|2;if(!Ab(b,g)){break a}a=H[b>>2];c=H[b+4>>2];d=H[b+12>>2];H[h+136>>2]=H[b+8>>2];H[h+140>>2]=d;H[h+128>>2]=a;H[h+132>>2]=c;Ca(g,4,7566,h+128|0);i=1}oa=h+208|0;return i|0}function gc(a,b,c,d,e,f){var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;a:{m=N(e,3);g=H[b>>2]>>>m|0;if(g&2097168){break a}n=a+28|0;k=n+(I[H[a+108>>2]+(g&495)|0]<<2)|0;H[a+104>>2]=k;j=H[k>>2];h=H[j>>2];g=H[a+4>>2]-h|0;H[a+4>>2]=g;i=H[a>>2];b:{if(i>>>16>>>0<h>>>0){l=H[j+4>>2];H[a+4>>2]=h;g=g>>>0<h>>>0;H[k>>2]=H[j+(g?8:12)>>2];j=g?l:!l;g=H[a+8>>2];while(1){c:{if(g){break c}g=H[a+16>>2];l=g+1|0;k=I[g+1|0];if(I[g|0]==255){if(k>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;i=i+65280|0;g=8;break c}H[a+16>>2]=l;i=(k<<9)+i|0;g=7;break c}H[a+16>>2]=l;g=8;i=(k<<8)+i|0}g=g-1|0;H[a+8>>2]=g;i=i<<1;H[a>>2]=i;h=h<<1;H[a+4>>2]=h;if(h>>>0<32768){continue}break}g=h;break b}i=i-(h<<16)|0;H[a>>2]=i;if(!(g&32768)){l=H[j+4>>2];h=g>>>0<h>>>0;H[k>>2]=H[j+(h?12:8)>>2];j=h?!l:l;h=H[a+8>>2];while(1){d:{if(h){break d}h=H[a+16>>2];l=h+1|0;k=I[h+1|0];if(I[h|0]==255){if(k>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;i=i+65280|0;h=8;break d}H[a+16>>2]=l;i=(k<<9)+i|0;h=7;break d}H[a+16>>2]=l;h=8;i=(k<<8)+i|0}h=h-1|0;H[a+8>>2]=h;i=i<<1;H[a>>2]=i;g=g<<1;H[a+4>>2]=g;if(g>>>0<32768){continue}break}break b}j=H[j+4>>2]}if(!j){break a}j=n;n=b-4|0;h=H[b>>2];o=H[b+4>>2]>>>m+17&4|(H[n>>2]>>>m+19&1|(h>>>m+16&64|h>>>m&170|h>>>(e?m+12|0:14)&16));l=j+(I[o+24384|0]<<2)|0;H[a+104>>2]=l;k=H[l>>2];h=H[k>>2];g=g-h|0;H[a+4>>2]=g;e:{if(i>>>16>>>0<h>>>0){j=H[k+4>>2];H[a+4>>2]=h;g=g>>>0<h>>>0;H[l>>2]=H[k+(g?8:12)>>2];k=g?j:!j;g=H[a+8>>2];while(1){f:{if(g){break f}g=H[a+16>>2];l=g+1|0;j=I[g+1|0];if(I[g|0]==255){if(j>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;i=i+65280|0;g=8;break f}H[a+16>>2]=l;i=(j<<9)+i|0;g=7;break f}H[a+16>>2]=l;g=8;i=(j<<8)+i|0}g=g-1|0;H[a+8>>2]=g;i=i<<1;H[a>>2]=i;h=h<<1;H[a+4>>2]=h;if(h>>>0<32768){continue}break}break e}j=i-(h<<16)|0;H[a>>2]=j;if(!(g&32768)){i=H[k+4>>2];h=g>>>0<h>>>0;H[l>>2]=H[k+(h?12:8)>>2];k=h?!i:i;i=H[a+8>>2];while(1){g:{if(i){break g}i=H[a+16>>2];l=i+1|0;h=I[i+1|0];if(I[i|0]==255){if(h>>>0>=144){H[a+12>>2]=H[a+12>>2]+1;j=j+65280|0;i=8;break g}H[a+16>>2]=l;j=(h<<9)+j|0;i=7;break g}H[a+16>>2]=l;i=8;j=(h<<8)+j|0}i=i-1|0;H[a+8>>2]=i;j=j<<1;H[a>>2]=j;g=g<<1;H[a+4>>2]=g;if(g>>>0<32768){continue}break}break e}k=H[k+4>>2]}g=c;c=I[o+24640|0];H[g>>2]=(c|0)==(k|0)?d:0-d|0;H[n>>2]=H[n>>2]|32<<m;d=c^k;H[b>>2]=H[b>>2]|(d<<19|16)<<m;H[b+4>>2]=H[b+4>>2]|8<<m;if(!(e|f)){c=(-2-H[a+124>>2]<<2)+b|0;H[c+4>>2]=H[c+4>>2]|32768;H[c>>2]=H[c>>2]|d<<31|65536;c=c-4|0;H[c>>2]=H[c>>2]|131072}if((e|0)!=3){break a}a=(H[a+124>>2]<<2)+b|0;H[a+4>>2]=H[a+4>>2]|4;H[a+12>>2]=H[a+12>>2]|1;H[a+8>>2]=H[a+8>>2]|d<<18|2}}function Zd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;e=oa-112|0;oa=e;j=1024;a:{b:{h=Fa(1,1024);if(h){l=e+92|0;k=e+108|0;while(1){c:{d:{d=e+104|0;e:{if((Ka(b,d,8,c)|0)!=8){break e}Ea(d,e+88|0,4);Ea(k,l,4);f=8;f:{g:{h:{i:{switch(H[e+88>>2]){case 0:d=Sa(b);g=ra;if((g|0)<0){g=1}else{g=d>>>0<4294967288&(g|0)<=0}if(g){break h}Ca(c,1,8449,0);break e;case 1:break i;default:break f}}d=e+104|0;if((Ka(b,d,8,c)|0)!=8){break e}Ea(d,e+100|0,4);if(!H[e+100>>2]){break g}Ca(c,1,8449,0);break e}H[e+88>>2]=d+8;break f}Ea(k,e+88|0,4);f=16}d=H[e+92>>2];if((d|0)==1785737827){b=H[a+100>>2];if(b&4){H[a+100>>2]=b|8;break e}Ca(c,1,5702,0);Da(h);a=0;break a}i=H[e+88>>2];if(!i){Ca(c,1,3268,0);Da(h);a=0;break a}if(f>>>0>i>>>0){H[e+4>>2]=d;H[e>>2]=i;Ca(c,1,13933,e);break b}j:{k:{l:{m:{n:{o:{p:{q:{r:{s:{if((d|0)<=1668246641){if((d|0)==1651532643){break r}if((d|0)==1667523942){break p}if((d|0)!=1668112752){break s}g=25296;break n}if((d|0)<=1783635999){if((d|0)==1668246642){break o}g=25264;if((d|0)==1768449138){break n}if((d|0)!=1718909296){break s}g=25240;break l}if((d|0)==1885564018){break q}if((d|0)==1783636e3){break m}g=25248;if((d|0)==1785737832){break l}}d=H[a+100>>2];if(d&1){break j}Ca(c,1,2062,0);Da(h);a=0;break a}g=25280;break n}g=25288;break n}g=25304;break n}g=25272}H[e+76>>2]=d&255;H[e+64>>2]=d>>>24;H[e+72>>2]=d>>>8&255;H[e+68>>2]=d>>>16&255;Ca(c,2,2011,e- -64|0);f=i-f|0;if(I[a+100|0]&4){break k}d=H[e+92>>2];H[e+48>>2]=d>>>24;H[e+60>>2]=d&255;H[e+52>>2]=d>>>16&255;H[e+56>>2]=d>>>8&255;Ca(c,2,6771,e+48|0);H[a+100>>2]=H[a+100>>2]|2147483647;d=ub(b,f,c);if(!ra&(d|0)==(f|0)){continue}Ca(c,1,3748,0);Da(h);a=0;break a}g=25232}f=i-f|0}d=f;f=Sa(b);i=ra;if((i|0)<0){f=1}else{f=(i|0)<=0&d>>>0>f>>>0}if(f){f=H[e+88>>2];a=H[e+92>>2];m=e,n=Sa(b),H[m+40>>2]=n;H[e+36>>2]=d;H[e+32>>2]=a&255;H[e+20>>2]=a>>>24;H[e+16>>2]=f;H[e+28>>2]=a>>>8&255;H[e+24>>2]=a>>>16&255;Ca(c,1,15680,e+16|0);break b}if(d>>>0<=j>>>0){f=h;break c}j=d;f=Ia(h,d);if(f){break c}Da(h);Ca(c,1,2193,0);a=0;break a}if(!(d&2)){Ca(c,1,2132,0);Da(h);a=0;break a}H[a+100>>2]=d|2147483647;d=i-f|0;f=ub(b,d,c);if(!ra&(d|0)==(f|0)){continue}if(!(I[a+100|0]&8)){break d}Ca(c,2,3748,0)}Da(h);a=1;break a}Ca(c,1,3748,0);Da(h);a=0;break a}if((Ka(b,f,d,c)|0)!=(d|0)){Ca(c,1,3798,0);Da(f);a=0;break a}h=f;if(sa[H[g+4>>2]](a,f,d,c)|0){continue}break}Da(f);a=0;break a}Ca(c,1,4923,0);a=0;break a}Da(h);a=0}oa=e+112|0;return a|0}function le(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;g=oa-16|0;oa=g;if(H[a+8>>2]==16){h=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{h=H[a+12>>2]}a:{if(c>>>0<=1){Ca(d,1,4721,0);a=0;break a}Ea(b,g+12|0,2);if(H[g+12>>2]){Ca(d,2,5897,0);a=1;break a}if(c>>>0<=6){Ca(d,1,4721,0);a=0;break a}Ea(b+2|0,g+8|0,1);j=H[h+5628>>2];a=j;b:{c:{d:{e=H[h+5632>>2];if(!e){break d}i=H[g+8>>2];while(1){if((i|0)==H[a>>2]){break d}a=a+20|0;f=f+1|0;if((e|0)!=(f|0)){continue}break}break c}if((e|0)!=(f|0)){break b}}if((e|0)==H[h+5636>>2]){a=e+10|0;H[h+5636>>2]=a;a=Ia(j,N(a,20));if(!a){Da(H[h+5628>>2]);H[h+5636>>2]=0;H[h+5628>>2]=0;H[h+5632>>2]=0;Ca(d,1,4747,0);a=0;break a}H[h+5628>>2]=a;e=H[h+5632>>2];f=N(H[h+5636>>2]-e|0,20);if(f){y(a+N(e,20)|0,0,f)}j=H[h+5628>>2];e=H[h+5632>>2]}a=N(e,20)+j|0;n=1}H[a>>2]=H[g+8>>2];Ea(b+3|0,g+12|0,2);if(H[g+12>>2]){Ca(d,2,5897,0);a=1;break a}Ea(b+5|0,g+4|0,2);f=H[g+4>>2];if(f>>>0>=2){Ca(d,2,3130,0);a=1;break a}e=c-7|0;if(f){c=b+7|0;j=0;while(1){if(e>>>0<=2){Ca(d,1,4721,0);a=0;break a}Ea(c,g+12|0,1);if(H[g+12>>2]!=1){Ca(d,2,5579,0);a=1;break a}Ea(c+1|0,g,2);f=H[g>>2];b=f&32767;H[a+4>>2]=b;i=e-3|0;e=(f>>>15|0)+1|0;k=N(e,b)+2|0;if(i>>>0<k>>>0){Ca(d,1,4721,0);a=0;break a}c=c+3|0;f=0;if(b){while(1){Ea(c,g+12|0,e);if(H[g+12>>2]!=(f|0)){Ca(d,2,6259,0);a=1;break a}c=c+e|0;f=f+1|0;if(f>>>0<K[a+4>>2]){continue}break}}Ea(c,g,2);e=H[g>>2];b=e&32767;H[g>>2]=b;if((b|0)!=H[a+4>>2]){Ca(d,2,3306,0);a=1;break a}e=(e>>>15|0)+1|0;l=N(e,b)+3|0;k=i-k|0;if(l>>>0>k>>>0){Ca(d,1,4721,0);a=0;break a}c=c+2|0;f=0;if(b){while(1){Ea(c,g+12|0,e);if(H[g+12>>2]!=(f|0)){Ca(d,2,6259,0);a=1;break a}c=c+e|0;f=f+1|0;if(f>>>0<K[a+4>>2]){continue}break}}Ea(c,g+12|0,3);e=H[g+12>>2];H[a+8>>2]=0;H[a+12>>2]=0;F[a+16|0]=!(e&65536)|I[a+16|0]&254;i=e&255;H[g+8>>2]=i;e:{if(!i){break e}m=H[h+5620>>2];if(m){f=H[h+5616>>2];b=0;while(1){if((i|0)==H[f+8>>2]){H[a+8>>2]=f;break e}f=f+20|0;b=b+1|0;if((m|0)!=(b|0)){continue}break}}Ca(d,1,4721,0);a=0;break a}e=e>>>8&255;H[g+8>>2]=e;f:{if(!e){break f}i=H[h+5620>>2];if(i){f=H[h+5616>>2];b=0;while(1){if((e|0)==H[f+8>>2]){H[a+12>>2]=f;break f}f=f+20|0;b=b+1|0;if((i|0)!=(b|0)){continue}break}}Ca(d,1,4721,0);a=0;break a}e=k-l|0;c=c+3|0;j=j+1|0;if(j>>>0<K[g+4>>2]){continue}break}}if(e){Ca(d,1,4721,0);a=0;break a}a=1;if(!n){break a}H[h+5632>>2]=H[h+5632>>2]+1;a=1}oa=g+16|0;return a|0}function gd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if(K[a+44>>2]>=8){i=H[a+40>>2];l=8;while(1){k=H[a+12>>2]<<5;e=H[a>>2];g=H[a+36>>2];b=H[a+16>>2];h=H[a+20>>2];a:{if(b>>>0>=h>>>0){break a}j=e+k|0;d=b+1|0;if(h-b&1){c=j+(b<<6)|0;b=(N(b,g)<<2)+i|0;f=H[b+4>>2];H[c>>2]=H[b>>2];H[c+4>>2]=f;f=H[b+28>>2];H[c+24>>2]=H[b+24>>2];H[c+28>>2]=f;f=H[b+20>>2];H[c+16>>2]=H[b+16>>2];H[c+20>>2]=f;f=H[b+12>>2];H[c+8>>2]=H[b+8>>2];H[c+12>>2]=f;b=d}if((d|0)==(h|0)){break a}while(1){d=(N(b,g)<<2)+i|0;f=H[d+4>>2];c=j+(b<<6)|0;H[c>>2]=H[d>>2];H[c+4>>2]=f;f=H[d+28>>2];H[c+24>>2]=H[d+24>>2];H[c+28>>2]=f;f=H[d+20>>2];H[c+16>>2]=H[d+16>>2];H[c+20>>2]=f;f=H[d+12>>2];H[c+8>>2]=H[d+8>>2];H[c+12>>2]=f;d=b+1|0;c=j+(d<<6)|0;d=(N(d,g)<<2)+i|0;f=H[d+28>>2];H[c+24>>2]=H[d+24>>2];H[c+28>>2]=f;f=H[d+20>>2];H[c+16>>2]=H[d+16>>2];H[c+20>>2]=f;f=H[d+12>>2];H[c+8>>2]=H[d+8>>2];H[c+12>>2]=f;f=H[d+4>>2];H[c>>2]=H[d>>2];H[c+4>>2]=f;b=b+2|0;if((h|0)!=(b|0)){continue}break}}b=H[a+24>>2];h=H[a+28>>2];b:{if(b>>>0>=h>>>0){break b}j=(e-k|0)+32|0;k=(N(g,H[a+8>>2])<<2)+i|0;d=b+1|0;if(h-b&1){c=j+(b<<6)|0;b=k+(N(b,g)<<2)|0;e=H[b+4>>2];H[c>>2]=H[b>>2];H[c+4>>2]=e;e=H[b+28>>2];H[c+24>>2]=H[b+24>>2];H[c+28>>2]=e;e=H[b+20>>2];H[c+16>>2]=H[b+16>>2];H[c+20>>2]=e;e=H[b+12>>2];H[c+8>>2]=H[b+8>>2];H[c+12>>2]=e;b=d}if((d|0)==(h|0)){break b}while(1){d=k+(N(b,g)<<2)|0;e=H[d+4>>2];c=j+(b<<6)|0;H[c>>2]=H[d>>2];H[c+4>>2]=e;e=H[d+28>>2];H[c+24>>2]=H[d+24>>2];H[c+28>>2]=e;e=H[d+20>>2];H[c+16>>2]=H[d+16>>2];H[c+20>>2]=e;e=H[d+12>>2];H[c+8>>2]=H[d+8>>2];H[c+12>>2]=e;d=b+1|0;c=j+(d<<6)|0;d=k+(N(d,g)<<2)|0;e=H[d+28>>2];H[c+24>>2]=H[d+24>>2];H[c+28>>2]=e;e=H[d+20>>2];H[c+16>>2]=H[d+16>>2];H[c+20>>2]=e;e=H[d+12>>2];H[c+8>>2]=H[d+8>>2];H[c+12>>2]=e;e=H[d+4>>2];H[c>>2]=H[d>>2];H[c+4>>2]=e;b=b+2|0;if((h|0)!=(b|0)){continue}break}}Wa(a);b=0;if(H[a+32>>2]){while(1){d=H[a>>2]+(b<<5)|0;c=H[d+4>>2];g=(N(H[a+36>>2],b)<<2)+i|0;H[g>>2]=H[d>>2];H[g+4>>2]=c;c=H[d+28>>2];H[g+24>>2]=H[d+24>>2];H[g+28>>2]=c;c=H[d+20>>2];H[g+16>>2]=H[d+16>>2];H[g+20>>2]=c;c=H[d+12>>2];H[g+8>>2]=H[d+8>>2];H[g+12>>2]=c;b=b+1|0;if(b>>>0<K[a+32>>2]){continue}break}}i=i+32|0;l=l+8|0;if(l>>>0<=K[a+44>>2]){continue}break}}Da(H[a>>2]);Da(a)}function pd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;c=H[b>>2]+7&-8;H[b>>2]=c+16;q=a;b=H[c>>2];a=H[c+4>>2];d=H[c+8>>2];c=H[c+12>>2];r=c;g=oa-32|0;oa=g;f=c&65535;e=d;d=0;c=c>>>16&32767;o=c;a:{if(c-15361>>>0<=2045){c=f<<4|e>>>28;d=e<<4|a>>>28;f=o-15360|0;a=a&268435455;b:{if((a|0)==134217728&(b|0)!=0|a>>>0>134217728){d=d+1|0;c=d?c:c+1|0;break b}if(b|(a|0)!=134217728){break b}a=d;d=d+(d&1)|0;c=a>>>0>d>>>0?c+1|0:c}a=d;d=c>>>0>1048575;b=d?0:a;a=d?0:c;c=0;e=f;f=d+f|0;e=e>>>0>f>>>0?1:c;break a}if(!(!(b|e|(a|f))|((c|0)!=32767|(d|0)!=0))){b=e;e=f<<4|b>>>28;b=b<<4|a>>>28;a=e|524288;f=2047;e=0;break a}if(o>>>0>17406){b=0;a=0;f=2047;e=0;break a}j=!(c|d);p=j?15360:15361;k=p-o|0;if((k|0)>112){b=0;a=0;f=0;e=0;break a}d=b;c=a;l=e;e=j?f:f|65536;f=e;h=l;m=128-k|0;c:{if(m&64){e=d;c=m+-64|0;d=c&31;if((c&63)>>>0>=32){c=b<<d;h=0}else{c=(1<<d)-1&e>>>32-d|a<<d;h=e<<d}e=c;d=0;c=0;break c}if(!m){break c}n=h;i=m&31;if((m&63)>>>0>=32){j=h<<i;n=0}else{j=(1<<i)-1&n>>>32-i|e<<i;n=n<<i}i=d;e=64-m|0;h=e&31;if((e&63)>>>0>=32){e=0;h=c>>>h|0}else{e=c>>>h|0;h=((1<<h)-1&c)<<32-h|i>>>h}h=n|h;e=e|j;n=d;i=m&31;if((m&63)>>>0>=32){j=d<<i;d=0}else{j=(1<<i)-1&n>>>32-i|c<<i;d=n<<i}c=j}H[g+16>>2]=d;H[g+20>>2]=c;H[g+24>>2]=h;H[g+28>>2]=e;d:{if(k&64){c=l;b=k+-64|0;a=b&31;if((b&63)>>>0>=32){e=0;b=f>>>a|0}else{e=f>>>a|0;b=((1<<a)-1&f)<<32-a|c>>>a}a=e;l=0;f=0;break d}if(!k){break d}e=l;c=64-k|0;d=c&31;if((c&63)>>>0>=32){c=e<<d;l=0}else{c=(1<<d)-1&e>>>32-d|f<<d;l=e<<d}d=b;b=k&31;if((k&63)>>>0>=32){j=0;a=a>>>b|0}else{j=a>>>b|0;a=((1<<b)-1&a)<<32-b|d>>>b}b=l|a;a=c|j;d=k&31;if((k&63)>>>0>=32){c=0;l=f>>>d|0}else{c=f>>>d|0;l=((1<<d)-1&f)<<32-d|e>>>d}f=c}H[g>>2]=b;H[g+4>>2]=a;H[g+8>>2]=l;H[g+12>>2]=f;a=H[g+8>>2];d=a<<4;a=H[g+12>>2]<<4|a>>>28;f=H[g>>2];b=H[g+4>>2];e=b;b=b>>>28|d;c=e&268435455;f=f|(o|0)!=(p|0)&(H[g+16>>2]|H[g+24>>2]|(H[g+20>>2]|H[g+28>>2]))!=0;e:{if((c|0)==134217728&(f|0)!=0|c>>>0>134217728){b=b+1|0;a=b?a:a+1|0;break e}if(f|(c|0)!=134217728){break e}c=a;a=b;b=b+(b&1)|0;a=a>>>0>b>>>0?c+1|0:c}f=a>>>0>1048575;a=f?a^1048576:a;e=0}oa=g+32|0;u(0,b|0);u(1,a|(r&-2147483648|f<<20));s=q,t=+w(),M[s>>3]=t}function Sc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;q=H[a+24>>2];if(!H[q+16>>2]){return 1}r=H[q+24>>2];o=H[H[H[a+20>>2]>>2]+20>>2];while(1){e=H[r+36>>2];H[b+36>>2]=e;c=N(e,152);e=H[o+28>>2];d=c+e|0;u=H[a+64>>2];a:{if(u){e=e+N(H[o+24>>2],152)|0;p=H[e-144>>2]-H[e-152>>2]|0;c=d+12|0;f=d+4|0;e=H[d+8>>2];h=H[d>>2];g=36;break a}c=d+148|0;f=d+140|0;e=H[d+144>>2];h=H[d+136>>2];p=e-h|0;g=52}v=H[g+o>>2];b:{c:{if(!v){break c}l=H[f>>2];n=H[c>>2];i=e-h|0;f=H[b+40>>2];c=f&31;if((f&63)>>>0>=32){d=-1<<c;c=0}else{g=(1<<c)-1&-1>>>32-c;c=-1<<c;d=g|c}m=c^-1;j=H[b+20>>2];k=m+j|0;g=d^-1;c=g;c=k>>>0<j>>>0?c+1|0:c;d=f&31;if((f&63)>>>0>=32){k=c>>>d|0}else{k=((1<<d)-1&c)<<32-d|k>>>d}d=H[b+8>>2];j=H[b+16>>2];m=j+m|0;c=g;c=m>>>0<j>>>0?c+1|0:c;g=f&31;if((f&63)>>>0>=32){f=c>>>g|0}else{f=((1<<g)-1&c)<<32-g|m>>>g}c=f+d|0;d:{if(f>>>0<h>>>0){s=h-f|0;g=0;if(c>>>0>=e>>>0){m=0;e=i;break d}e=c-h|0;m=i-e|0;break d}g=f-h|0;if(c>>>0>=e>>>0){e=i-g|0;s=0;m=0;break d}m=e-c|0;s=0;e=d}c=n-l|0;f=H[b+12>>2];i=f+k|0;e:{if(k>>>0<l>>>0){t=l-k|0;k=0;j=0;if(i>>>0>=n>>>0){break e}j=c;c=i-l|0;j=j-c|0;break e}k=k-l|0;if(i>>>0>=n>>>0){c=c-k|0;t=0;j=0;break e}t=0;c=f;j=n-i|0}h=0;if((g|k|(m|j)|(c|e))<0){break b}i=N(k,p)+g|0;g=H[b+44>>2];l=N(d,t)+s|0;f:{g:{if(!(i|g|(l|(d|0)!=(p|0))|(d|0)!=(e|0))){if((c|0)!=(f|0)){break g}e=(u?36:52)+o|0;H[b+44>>2]=H[e>>2];H[e>>2]=0;break c}if(g){break f}}Ie(f,0,d);if(ra|!f){break b}d=N(d,f);if(d>>>0>1073741823){break b}d=Ja(d<<2);H[b+44>>2]=d;if(!d){break b}f=H[b+8>>2];g=H[b+12>>2];if((f|0)==(e|0)&(g|0)==(c|0)){break f}f=N(f,g)<<2;if(!f){break f}y(d,0,f)}if(!c){break c}g=c&1;e=e<<2;h=H[b+44>>2]+(l<<2)|0;d=(i<<2)+v|0;if((c|0)!=1){i=c&2147483646;c=0;while(1){l=!e;if(!l){B(h,d,e)}n=p<<2;d=n+d|0;f=(H[b+8>>2]<<2)+h|0;if(!l){B(f,d,e)}d=d+n|0;h=f+(H[b+8>>2]<<2)|0;c=c+2|0;if((i|0)!=(c|0)){continue}break}}if(!g|!e){break c}B(h,d,e)}o=o+76|0;r=r+52|0;b=b+52|0;h=1;w=w+1|0;if(w>>>0<K[q+16>>2]){continue}}break}return h}function Bb(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0;if(a){a:{if(H[a>>2]){b=H[a+12>>2];if(b){mb(b);Da(H[a+12>>2]);H[a+12>>2]=0}b=H[a+16>>2];if(b){Da(b);H[a+16>>2]=0;H[a+20>>2]=0}Da(H[a+64>>2]);H[a+60>>2]=0;H[a+64>>2]=0;Da(H[a+72>>2]);H[a+72>>2]=0;Da(H[a+88>>2]);H[a+88>>2]=0;break a}b=H[a+44>>2];if(b){Da(b);H[a+44>>2]=0}b=H[a+32>>2];if(b){Da(b);H[a+32>>2]=0;H[a+36>>2]=0}b=H[a+52>>2];if(!b){break a}Da(b);H[a+52>>2]=0;H[a+56>>2]=0}dc(H[a+232>>2]);b=H[a+180>>2];if(b){e=N(H[a+128>>2],H[a+132>>2]);if(e){while(1){mb(b);b=b+5644|0;c=c+1|0;if((e|0)!=(c|0)){continue}break}b=H[a+180>>2]}Da(b);H[a+180>>2]=0}b=H[a+140>>2];if(b){c=H[a+136>>2];if(c){b=0;while(1){e=H[H[a+140>>2]+(b<<3)>>2];if(e){Da(e);c=H[a+136>>2]}b=b+1|0;if(c>>>0>b>>>0){continue}break}b=H[a+140>>2]}H[a+136>>2]=0;Da(b);H[a+140>>2]=0}Da(H[a+160>>2]);H[a+144>>2]=0;H[a+160>>2]=0;Da(H[a+124>>2]);H[a+124>>2]=0;if(!(I[a+212|0]&2)){Da(H[a+192>>2])}y(a+104|0,0,112);sb(H[a+216>>2]);H[a+216>>2]=0;sb(H[a+220>>2]);H[a+216>>2]=0;d=H[a+224>>2];if(d){b=H[d+28>>2];if(b){Da(b);H[d+28>>2]=0}c=H[d+40>>2];if(c){if(H[d+36>>2]){while(1){e=N(g,40);b=H[(e+c|0)+36>>2];if(b){Da(b);c=H[d+40>>2];H[(e+c|0)+36>>2]=0}b=H[(c+e|0)+16>>2];if(b){Da(b);c=H[d+40>>2];H[(e+c|0)+16>>2]=0}b=H[(c+e|0)+24>>2];if(b){Da(b);c=H[d+40>>2];H[(e+c|0)+24>>2]=0}g=g+1|0;if(g>>>0<K[d+36>>2]){continue}break}}Da(c);H[d+40>>2]=0}Da(d)}H[a+224>>2]=0;Va(H[a+96>>2]);H[a+96>>2]=0;Va(H[a+100>>2]);H[a+100>>2]=0;f=H[a+236>>2];if(f){b:{if(!H[f+8>>2]){break b}if(H[f+12>>2]){H[f+40>>2]=0;while(1){if(H[f+24>>2]>0){continue}break}}H[f+16>>2]=1;Da(H[f>>2]);c=H[f+28>>2];if(!c){break b}while(1){b=H[c+4>>2];Da(c);H[f+28>>2]=b;c=b;if(b){continue}break}}d=H[f+36>>2];if(d){g=H[d+4>>2];if((g|0)>0){b=0;while(1){e=H[d>>2]+N(b,12)|0;c=H[e+8>>2];if(c){sa[c|0](H[e+4>>2]);g=H[d+4>>2]}b=b+1|0;if((g|0)>(b|0)){continue}break}}Da(H[d>>2]);Da(d)}Da(f)}H[a+236>>2]=0;Da(a)}}function ke(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;g=oa-16|0;oa=g;if(H[a+8>>2]==16){h=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{h=H[a+12>>2]}a:{if(!c){Ca(d,1,4259,0);break a}i=H[a+96>>2];e=1;Ea(b,g+8|0,1);f=H[g+8>>2];if(f>>>0>=2){Ca(d,2,9792,0);break a}if((f+1|0)!=(c|0)){e=0;Ca(d,2,4259,0);break a}d=H[i+16>>2];b:{if(!d){break b}e=H[h+5584>>2];if(d>>>0>=8){i=d&-8;c=0;while(1){H[e+8636>>2]=0;H[e+7556>>2]=0;H[e+6476>>2]=0;H[e+5396>>2]=0;H[e+4316>>2]=0;H[e+3236>>2]=0;H[e+2156>>2]=0;H[e+1076>>2]=0;e=e+8640|0;c=c+8|0;if((i|0)!=(c|0)){continue}break}}d=d&7;if(!d){break b}c=0;while(1){H[e+1076>>2]=0;e=e+1080|0;c=c+1|0;if((d|0)!=(c|0)){continue}break}}c=H[h+5608>>2];if(c){Da(c);H[h+5608>>2]=0;f=H[g+8>>2]}if(!f){e=1;break a}i=0;while(1){b=b+1|0;Ea(b,g+12|0,1);c:{if(!H[h+5632>>2]){break c}d=H[h+5628>>2];if(H[d>>2]!=H[g+12>>2]){break c}f=H[d+4>>2];j=H[a+96>>2];if((f|0)!=H[j+16>>2]){break c}c=H[d+8>>2];if(c){e=0;f=N(f,f);if(H[c+16>>2]!=(N(f,H[(H[c>>2]<<2)+24896>>2])|0)){break a}k=Ga(f<<2);H[h+5608>>2]=k;if(!k){break a}sa[H[(H[c>>2]<<2)+25200>>2]](H[c+12>>2],k,f)}c=H[d+12>>2];if(!c){break c}e=0;d=H[j+16>>2];if(H[c+16>>2]!=(N(d,H[(H[c>>2]<<2)+24896>>2])|0)){break a}f=Ga(d<<2);if(!f){break a}sa[H[(H[c>>2]<<2)+25216>>2]](H[c+12>>2],f,d);c=H[j+16>>2];d:{if(!c){break d}j=c&7;e=H[h+5584>>2];e:{if(c>>>0<8){c=f;break e}k=c&-8;d=0;c=f;while(1){H[e+1076>>2]=H[c>>2];H[e+2156>>2]=H[c+4>>2];H[e+3236>>2]=H[c+8>>2];H[e+4316>>2]=H[c+12>>2];H[e+5396>>2]=H[c+16>>2];H[e+6476>>2]=H[c+20>>2];H[e+7556>>2]=H[c+24>>2];H[e+8636>>2]=H[c+28>>2];e=e+8640|0;c=c+32|0;d=d+8|0;if((k|0)!=(d|0)){continue}break}}d=0;if(!j){break d}while(1){H[e+1076>>2]=H[c>>2];e=e+1080|0;c=c+4|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}}Da(f)}e=1;i=i+1|0;if(i>>>0<K[g+8>>2]){continue}break}}oa=g+16|0;return e|0}function Cb(a,b,c,d,e,f,g,h){var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;m=H[H[a+24>>2]+24>>2]+N(b,52)|0;l=H[m+4>>2];k=l-1|0;o=H[a+60>>2];j=k+o|0;p=0-!l|0;i=p;r=H[H[H[a+20>>2]>>2]+20>>2]+N(b,76)|0;n=H[r+12>>2];i=Ke(j,j>>>0<o>>>0?i+1|0:i,l,0);q=i>>>0>n>>>0?n:i;j=H[m>>2];m=j-1|0;s=H[a+56>>2];n=m+s|0;o=0-!j|0;i=o;t=H[r+8>>2];i=Ke(n,n>>>0<s>>>0?i+1|0:i,j,0);n=i>>>0>t>>>0?t:i;i=p;t=H[r+4>>2];s=H[a+52>>2];k=s+k|0;i=Ke(k,k>>>0<s>>>0?i+1|0:i,l,0);k=i>>>0<t>>>0?t:i;i=o;p=H[r>>2];l=m;m=H[a+48>>2];l=l+m|0;i=Ke(l,l>>>0<m>>>0?i+1|0:i,j,0);i=i>>>0<p>>>0?p:i;l=0;p=H[(H[H[a+32>>2]+5584>>2]+N(b,1080)|0)+20>>2];c=H[r+20>>2]+(c?0-c|0:-1)|0;a:{if(!c){a=n;l=i;b=k;break a}m=c-1|0;j=(d&1)<<m;if(j>>>0<i>>>0){a=c&31;l=i-j|0;if((c&63)>>>0>=32){i=-1<<a;a=0}else{b=(1<<a)-1&-1>>>32-a;a=-1<<a;i=b|a}b=a^-1;a=l+b|0;i=i^-1;i=a>>>0<b>>>0?i+1|0:i;b=a;a=c&31;if((c&63)>>>0>=32){l=i>>>a|0}else{l=((1<<a)-1&i)<<32-a|b>>>a}}a=0;b=0;d=d>>>1<<m;if(d>>>0<k>>>0){b=c&31;o=k-d|0;if((c&63)>>>0>=32){i=-1<<b;b=0}else{i=(1<<b)-1&-1>>>32-b;b=-1<<b;i=i|b}k=b^-1;b=o+k|0;i=i^-1;i=b>>>0<k>>>0?i+1|0:i;k=b;b=c&31;if((c&63)>>>0>=32){b=i>>>b|0}else{b=((1<<b)-1&i)<<32-b|k>>>b}}if(j>>>0<n>>>0){a=c&31;k=n-j|0;if((c&63)>>>0>=32){i=-1<<a;a=0}else{i=(1<<a)-1&-1>>>32-a;a=-1<<a;i=i|a}j=a^-1;a=k+j|0;i=i^-1;i=a>>>0<j>>>0?i+1|0:i;j=a;a=c&31;if((c&63)>>>0>=32){a=i>>>a|0}else{a=((1<<a)-1&i)<<32-a|j>>>a}}if(d>>>0>=q>>>0){q=0;break a}k=q-d|0;d=c&31;if((c&63)>>>0>=32){i=-1<<d;d=0}else{i=(1<<d)-1&-1>>>32-d;d=-1<<d;i=i|d}j=d^-1;d=k+j|0;i=i^-1;i=d>>>0<j>>>0?i+1|0:i;j=d;d=c&31;if((c&63)>>>0>=32){q=i>>>d|0}else{q=((1<<d)-1&i)<<32-d|j>>>d}}c=(p|0)==1?2:3;d=c+a|0;d=(a>>>0>d>>>0?-1:d)>>>0>e>>>0;a=c+q|0;d=d&(a>>>0<q>>>0?-1:a)>>>0>f>>>0;a=l-c|0;d=d&(a>>>0<=l>>>0?a:0)>>>0<g>>>0;a=b-c|0;return d&(a>>>0<=b>>>0?a:0)>>>0<h>>>0}function Ee(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;e=oa-80|0;oa=e;H[e+76>>2]=1;k=H[a+44>>2];d=H[H[a+224>>2]+40>>2];a:{b:{if(!d|!H[d+16>>2]){break b}c:{d=d+N(k,40)|0;if(!H[d+4>>2]){d=H[a+52>>2];f=H[a+48>>2]+2|0;d=f>>>0<2?d+1|0:d;if(fb(b,f,d,c)){break c}Ca(c,1,5440,0);break a}d=H[d+16>>2];if(!fb(b,H[d>>2],H[d+4>>2],c)){Ca(c,1,5440,0);break a}if((Ka(b,H[a+16>>2],2,c)|0)!=2){Ca(c,1,2472,0);break a}Ea(H[a+16>>2],e+72|0,2);if(H[e+72>>2]==65424){break c}Ca(c,1,4073,0);break a}if(H[a+8>>2]!=256){break b}H[a+8>>2]=8}h=N(H[a+132>>2],H[a+128>>2]);d:{if(!h){break d}f=H[a+180>>2];d=0;if(h>>>0>=8){i=h&-8;while(1){H[(f+N(d,5644)|0)+5588>>2]=-1;H[(f+N(d|1,5644)|0)+5588>>2]=-1;H[(f+N(d|2,5644)|0)+5588>>2]=-1;H[(f+N(d|3,5644)|0)+5588>>2]=-1;H[(f+N(d|4,5644)|0)+5588>>2]=-1;H[(f+N(d|5,5644)|0)+5588>>2]=-1;H[(f+N(d|6,5644)|0)+5588>>2]=-1;H[(f+N(d|7,5644)|0)+5588>>2]=-1;d=d+8|0;j=j+8|0;if((i|0)!=(j|0)){continue}break}}h=h&7;if(!h){break d}while(1){H[(f+N(d,5644)|0)+5588>>2]=-1;d=d+1|0;g=g+1|0;if((h|0)!=(g|0)){continue}break}}g=0;if(!Za(a,e+72|0,0,e+68|0,e- -64|0,e+60|0,e+56|0,e+52|0,e+76|0,b,c)){break a}h=k+1|0;while(1){e:{if(!H[e+76>>2]){break e}d=H[e+72>>2];if(!gb(a,d,0,0,b,c)){break a}i=H[a+128>>2];j=H[a+132>>2];f=d+1|0;H[e+32>>2]=f;H[e+36>>2]=N(i,j);Ca(c,4,11795,e+32|0);if(!Sc(H[a+232>>2],H[H[a+100>>2]+24>>2])){break a}g=H[a+180>>2]+N(d,5644)|0;i=H[g+5596>>2];if(i){Da(i);H[g+5596>>2]=0;H[g+5600>>2]=0}H[e+16>>2]=f;Ca(c,4,16601,e+16|0);if((d|0)==(k|0)){d=H[a+224>>2];f=H[d+8>>2];d=H[d+12>>2];f=f+2|0;d=f>>>0<2?d+1|0:d;if(fb(b,f,d,c)){break e}g=0;Ca(c,1,5440,0);break a}H[e+4>>2]=h;H[e>>2]=f;Ca(c,2,13648,e);g=0;if(Za(a,e+72|0,0,e+68|0,e- -64|0,e+60|0,e+56|0,e+52|0,e+76|0,b,c)){continue}break a}break}g=Rc(a,c)}oa=e+80|0;return g|0}function qc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;j=oa-256|0;oa=j;a:{if(!a){a=0;break a}if(!(H[a>>2]==(b|0)&H[a+4>>2]==(c|0))){H[a+4>>2]=c;H[a>>2]=b;H[j>>2]=c;H[j+128>>2]=b;e=c;g=b;while(1){o=i;i=i+1|0;h=i<<2;n=(e+1|0)/2|0;H[h+j>>2]=n;k=h+(j+128|0)|0;h=(g+1|0)/2|0;H[k>>2]=h;m=N(e,g);f=m+f|0;e=n;g=h;if(m>>>0>1){continue}break}H[a+8>>2]=f;b:{c:{d:{if(!f){b=H[a+12>>2];if(!b){break d}Da(b);H[a+12>>2]=0;break d}e=f<<4;if(e>>>0<=K[a+16>>2]){break b}f=Ia(H[a+12>>2],e);if(f){break c}Ca(d,1,6451,0);b=H[a+12>>2];if(!b){break d}Da(b);H[a+12>>2]=0}Da(a);a=0;break a}H[a+12>>2]=f;c=H[a+16>>2];b=e-c|0;if(b){y(c+f|0,0,b)}H[a+16>>2]=e;c=H[a+4>>2];b=H[a>>2]}g=H[a+12>>2];if(o){d=0;e=(N(b,c)<<4)+g|0;f=e;while(1){b=d<<2;k=H[b+j>>2];e:{if((k|0)<=0){break e}m=k-1|0;l=0;f:{g:{c=H[b+(j+128|0)>>2];if((c|0)<=0){n=k&1;i=0;if((k|0)!=1){break g}b=f;break f}while(1){b=f;f=c;while(1){h:{H[g>>2]=e;if((f|0)==1){g=g+16|0;e=e+16|0;break h}H[g+16>>2]=e;e=e+16|0;g=g+32|0;h=(f|0)>2;f=f-2|0;if(h){continue}}break}h=((l|0)==(m|0)|l)&1;f=h?e:b+(c<<4)|0;e=h?e:b;l=l+1|0;if((k|0)!=(l|0)){continue}break}break e}h=k&2147483646;while(1){b=(i|0)==(m|0);i=i+2|0;e=b?e:f;f=e;b=e;l=l+2|0;if((h|0)!=(l|0)){continue}break}}if(!n){f=e;break e}f=(c<<4)+b|0;c=((i|0)==(m|0)|i)&1;f=c?e:f;e=c?e:b}d=d+1|0;if((o|0)!=(d|0)){continue}break}}H[g>>2]=0}c=H[a+8>>2];if(!c){break a}e=H[a+12>>2];if(c>>>0>=4){b=c&-4;g=0;while(1){H[e+60>>2]=0;H[e+52>>2]=999;H[e+56>>2]=0;H[e+44>>2]=0;H[e+36>>2]=999;H[e+40>>2]=0;H[e+28>>2]=0;H[e+20>>2]=999;H[e+24>>2]=0;H[e+12>>2]=0;H[e+4>>2]=999;H[e+8>>2]=0;e=e- -64|0;g=g+4|0;if((b|0)!=(g|0)){continue}break}}b=c&3;if(!b){break a}g=0;while(1){H[e+12>>2]=0;H[e+4>>2]=999;H[e+8>>2]=0;e=e+16|0;g=g+1|0;if((b|0)!=(g|0)){continue}break}}oa=j+256|0;return a}function ob(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;g=H[a+16>>2];if(g>>>0>=32){return H[a+8>>2]}d=H[a+20>>2];a:{if((d|0)>=4){b=H[a>>2];c=H[b-3>>2];d=d-4|0;H[a+20>>2]=d;H[a>>2]=b-4;break a}if((d|0)<=0){break a}k=d&1;b=H[a>>2];b:{if((d|0)==1){e=24;break b}j=d&2147483646;e=24;while(1){h=b-1|0;H[a>>2]=h;i=I[b|0];b=b-2|0;H[a>>2]=b;H[a+20>>2]=d-1;h=I[h|0];d=d-2|0;H[a+20>>2]=d;c=i<<e|c|h<<e-8;e=e-16|0;f=f+2|0;if((j|0)!=(f|0)){continue}break}}if(k){H[a>>2]=b-1;b=I[b|0];H[a+20>>2]=d-1;c=b<<e|c}d=0}b=H[a+24>>2];j=c&255;H[a+24>>2]=j>>>0>143;b=b?(c&2130706432)==2130706432?7:8:8;h=b+(c>>>0<=2415919103?8:(c&8323072)==8323072?7:8)|0;f=c>>>16&255;i=h+(f>>>0<=143?8:(c&32512)==32512?7:8)|0;e=c>>>8&255;k=i+(g+(e>>>0<=143?8:(c&127)==127?7:8)|0)|0;H[a+16>>2]=k;l=H[a+12>>2];b=f<<b|c>>>24|e<<h|j<<i;c=g&31;if((g&63)>>>0>=32){e=b<<c;b=0}else{e=(1<<c)-1&b>>>32-c;b=b<<c}g=b|H[a+8>>2];b=e|l;h=b;H[a+8>>2]=g;H[a+12>>2]=b;if(k>>>0<=31){c:{if((d|0)>=4){b=H[a>>2];c=H[b-3>>2];H[a+20>>2]=d-4;H[a>>2]=b-4;break c}if((d|0)<=0){c=0;break c}i=d&1;b=H[a>>2];d:{if((d|0)==1){e=24;c=0;break d}l=d&2147483646;e=24;c=0;f=0;while(1){m=b-1|0;H[a>>2]=m;n=I[b|0];b=b-2|0;H[a>>2]=b;H[a+20>>2]=d-1;m=I[m|0];d=d-2|0;H[a+20>>2]=d;c=n<<e|c|m<<e-8;e=e-16|0;f=f+2|0;if((l|0)!=(f|0)){continue}break}}if(!i){break c}H[a>>2]=b-1;b=I[b|0];H[a+20>>2]=d-1;c=b<<e|c}d=c&255;H[a+24>>2]=d>>>0>143;j=j>>>0<=143?8:(c&2130706432)==2130706432?7:8;i=j+(c>>>0<=2415919103?8:(c&8323072)==8323072?7:8)|0;f=c>>>16&255;l=i+(f>>>0<=143?8:(c&32512)==32512?7:8)|0;e=c>>>8&255;H[a+16>>2]=l+((e>>>0<=143?8:(c&127)==127?7:8)+k|0);b=a;a=f<<j|c>>>24|e<<i|d<<l;c=k&31;if((k&63)>>>0>=32){d=a<<c;a=0}else{d=(1<<c)-1&a>>>32-c;a=a<<c}g=a|g;H[b+8>>2]=g;H[b+12>>2]=d|h}return g}function _c(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;j=H[a+96>>2];l=N(H[a+128>>2],H[a+132>>2]);a:{if(l){b=H[j+16>>2];m=N(b,1080);k=N(b,b)<<2;e=H[a+12>>2];b=H[a+180>>2];while(1){n=H[b+5584>>2];B(b,e,5644);H[b+5608>>2]=0;H[b+5588>>2]=-1;H[b+5168>>2]=0;H[b+5636>>2]=0;H[b+5616>>2]=0;H[b+5624>>2]=0;H[b+5628>>2]=0;H[b+5584>>2]=n;F[b+5640|0]=I[b+5640|0]&252;b:{if(!H[e+5608>>2]){break b}d=Ga(k);H[b+5608>>2]=d;if(!d){return 0}if(!k){break b}B(d,H[e+5608>>2],k)}d=N(H[e+5624>>2],20);f=Ga(d);H[b+5616>>2]=f;i=0;if(!f){break a}if(d){B(f,H[e+5616>>2],d)}g=H[e+5620>>2];if(g){d=H[e+5616>>2];f=H[b+5616>>2];h=0;while(1){if(H[d+12>>2]){g=Ga(H[d+16>>2]);H[f+12>>2]=g;if(!g){return 0}o=H[d+16>>2];if(o){B(g,H[d+12>>2],o)}g=H[e+5620>>2]}H[b+5624>>2]=H[b+5624>>2]+1;f=f+20|0;d=d+20|0;h=h+1|0;if(h>>>0<g>>>0){continue}break}}d=N(H[e+5636>>2],20);f=Ga(d);H[b+5628>>2]=f;if(!f){break a}if(d){B(f,H[e+5628>>2],d)}i=H[e+5636>>2];H[b+5636>>2]=i;if(i){d=H[e+5628>>2];f=H[b+5628>>2];h=0;while(1){g=H[d+8>>2];if(g){H[f+8>>2]=H[b+5616>>2]+(g-H[e+5616>>2]|0)}g=H[d+12>>2];if(g){H[f+12>>2]=H[b+5616>>2]+(g-H[e+5616>>2]|0)}f=f+20|0;d=d+20|0;h=h+1|0;if((i|0)!=(h|0)){continue}break}}if(m){B(n,H[e+5584>>2],m)}b=b+5644|0;p=p+1|0;if((p|0)!=(l|0)){continue}break}}i=1;e=Fa(1,72);b=0;c:{if(!e){break c}F[e+40|0]=I[e+40|0]&254|1;d=Fa(1,4);H[e+20>>2]=d;b=e;if(d){break c}Da(b);b=0}H[a+232>>2]=b;if(!b){return 0}f=H[a+236>>2];e=0;H[b+28>>2]=a+104;H[b+24>>2]=j;d=Fa(1,848);H[H[b+20>>2]>>2]=d;d:{if(!d){break d}d=Fa(H[j+16>>2],76);h=H[H[b+20>>2]>>2];H[h+20>>2]=d;if(!d){break d}H[h+16>>2]=H[j+16>>2];e=H[a+188>>2];H[b+44>>2]=f;H[b>>2]=e;e=1}if(e){break a}dc(H[a+232>>2]);i=0;H[a+232>>2]=0;Ca(c,1,3668,0)}return i|0}function Na(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;h=H[a+16>>2];if(h>>>0>=32){return H[a+8>>2]}d=H[a+24>>2];a:{if((d|0)>=4){b=H[a>>2];c=H[b>>2];g=d-4|0;H[a+24>>2]=g;H[a>>2]=b+4;break a}c=H[a+28>>2]?-1:0;if((d|0)<=0){g=d;break a}j=d&1;b=H[a>>2];b:{if((d|0)==1){f=b;break b}i=d&2147483646;while(1){H[a>>2]=b+1;k=I[b|0];f=b+2|0;H[a>>2]=f;H[a+24>>2]=d-1;b=I[b+1|0];d=d-2|0;H[a+24>>2]=d;c=((255<<e^-1)&c|k<<e)&(65280<<e^-1)|b<<(e|8);e=e+16|0;b=f;g=g+2|0;if((i|0)!=(g|0)){continue}break}}g=0;if(!j){break a}H[a>>2]=f+1;b=I[f|0];H[a+24>>2]=d-1;c=(255<<e^-1)&c|b<<e}b=H[a+20>>2];i=c>>>24|0;H[a+20>>2]=(i|0)==255;f=c>>>16&255;d=c>>>8&255;b=b?7:8;c=c&255;e=b+((c|0)==255?7:8)|0;k=((d|0)==255?7:8)+e|0;j=(h+((f|0)==255?7:8)|0)+k|0;H[a+16>>2]=j;l=H[a+12>>2];b=c|(d<<b|f<<e|i<<k);c=h&31;if((h&63)>>>0>=32){f=b<<c;b=0}else{f=(1<<c)-1&b>>>32-c;b=b<<c}h=b|H[a+8>>2];b=f|l;k=b;H[a+8>>2]=h;H[a+12>>2]=b;if(j>>>0<=31){c:{if((g|0)>=4){b=H[a>>2];d=H[b>>2];H[a+24>>2]=g-4;H[a>>2]=b+4;break c}e=0;d=H[a+28>>2]?-1:0;if((g|0)<=0){break c}l=g&1;b=H[a>>2];d:{if((g|0)==1){c=b;break d}m=g&2147483646;f=0;while(1){H[a>>2]=b+1;n=I[b|0];c=b+2|0;H[a>>2]=c;H[a+24>>2]=g-1;b=I[b+1|0];g=g-2|0;H[a+24>>2]=g;d=((255<<e^-1)&d|n<<e)&(65280<<e^-1)|b<<(e|8);e=e+16|0;b=c;f=f+2|0;if((m|0)!=(f|0)){continue}break}}if(!l){break c}H[a>>2]=c+1;b=I[c|0];H[a+24>>2]=g-1;d=(255<<e^-1)&d|b<<e}c=d>>>24|0;H[a+20>>2]=(c|0)==255;f=d>>>16&255;g=d>>>8&255;e=(i|0)==255?7:8;d=d&255;i=e+((d|0)==255?7:8)|0;l=((g|0)==255?7:8)+i|0;H[a+16>>2]=(((f|0)==255?7:8)+j|0)+l;b=a;a=d|(g<<e|f<<i|c<<l);c=j&31;if((j&63)>>>0>=32){f=a<<c;a=0}else{f=(1<<c)-1&a>>>32-c;a=a<<c}h=a|h;H[b+8>>2]=h;H[b+12>>2]=f|k}return h}function Wc(a,b,c,d,e){var f=0,g=0,h=0,i=0,j=0,k=0;i=oa-32|0;oa=i;if(H[a+8>>2]==16){f=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{f=H[a+12>>2]}a:{if(K[d>>2]<=4){a=0;Ca(e,1,2607,0);break a}f=H[f+5584>>2]+N(b,1080)|0;Ea(c,f+4|0,1);h=H[f+4>>2]+1|0;H[f+4>>2]=h;if(h>>>0>=34){H[i+4>>2]=33;H[i>>2]=h;Ca(e,1,7635,i);a=0;break a}g=H[a+184>>2];if(g>>>0>=h>>>0){H[i+24>>2]=h;H[i+20>>2]=g;H[i+16>>2]=b;Ca(e,1,16423,i+16|0);H[a+8>>2]=H[a+8>>2]|32768;a=0;break a}Ea(c+1|0,f+8|0,1);H[f+8>>2]=H[f+8>>2]+2;Ea(c+2|0,f+12|0,1);a=H[f+12>>2]+2|0;H[f+12>>2]=a;b=H[f+8>>2];if(!(!(b>>>0>10|a>>>0>10)&a+b>>>0<13)){a=0;Ca(e,1,5468,0);break a}Ea(c+3|0,f+16|0,1);if(I[f+16|0]&128){a=0;Ca(e,1,6564,0);break a}Ea(c+4|0,f+20|0,1);if(K[f+20>>2]>=2){a=0;Ca(e,1,6499,0);break a}b=H[d>>2]-5|0;H[d>>2]=b;a=1;h=H[f+4>>2];if(!(F[f|0]&1)){if(!h){break a}d=f+944|0;e=f+812|0;b=0;c=0;if(h>>>0>=4){k=h&-4;g=0;while(1){f=c<<2;H[f+e>>2]=15;H[d+f>>2]=15;j=f|4;H[j+e>>2]=15;H[d+j>>2]=15;j=f|8;H[j+e>>2]=15;H[d+j>>2]=15;f=f|12;H[f+e>>2]=15;H[d+f>>2]=15;c=c+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}f=h&3;if(!f){break a}while(1){a=c<<2;H[a+e>>2]=15;H[a+d>>2]=15;a=1;c=c+1|0;b=b+1|0;if((f|0)!=(b|0)){continue}break}break a}if(b>>>0>=h>>>0){b:{if(!h){g=0;break b}Ea(c+5|0,i+28|0,1);a=H[i+28>>2];H[f+944>>2]=a>>>4;H[f+812>>2]=a&15;g=H[f+4>>2];if(g>>>0>=2){h=f+944|0;k=f+812|0;a=c+6|0;c=1;while(1){Ea(a,i+28|0,1);c:{b=H[i+28>>2];if(b>>>0>=16){g=b&15;if(g){break c}}a=0;Ca(e,1,6025,0);break a}j=c<<2;H[j+k>>2]=g;H[h+j>>2]=b>>>4;a=a+1|0;c=c+1|0;g=H[f+4>>2];if(c>>>0<g>>>0){continue}break}}b=H[d>>2]}H[d>>2]=b-g;a=1;break a}a=0;Ca(e,1,2607,0)}oa=i+32|0;return a}function Ce(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;f=oa-16|0;oa=f;H[f+12>>2]=c;j=H[a+96>>2];if(H[a+8>>2]==16){e=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{e=H[a+12>>2]}F[e+5640|0]=I[e+5640|0]|1;a:{if(c>>>0<=4){c=0;Ca(d,1,4565,0);break a}Ea(b,e,1);if(K[e>>2]>=8){c=0;Ca(d,1,4531,0);break a}Ea(b+1|0,f+8|0,1);c=H[f+8>>2];H[e+4>>2]=c;if((c|0)>=5){Ca(d,1,4490,0);H[e+4>>2]=-1}Ea(b+2|0,e+8|0,2);l=H[e+8>>2];if(l-65536>>>0<=4294901760){H[f>>2]=l;Ca(d,1,8111,f);c=0;break a}c=H[a+188>>2];H[e+12>>2]=c?c:l;Ea(b+4|0,e+16|0,1);if(K[e+16>>2]>=2){c=0;Ca(d,1,5536,0);break a}o=b+5|0;H[f+12>>2]=H[f+12>>2]-5;m=H[j+16>>2];b:{if(!m){break b}g=H[e>>2]&1;h=H[e+5584>>2];b=0;if(m>>>0>=8){p=h+7560|0;q=h+6480|0;r=h+5400|0;s=h+4320|0;l=h+3240|0;j=h+2160|0;e=h+1080|0;c=m&-8;while(1){i=N(b,1080);H[i+h>>2]=g;H[e+i>>2]=g;H[j+i>>2]=g;H[i+l>>2]=g;H[i+s>>2]=g;H[i+r>>2]=g;H[i+q>>2]=g;H[i+p>>2]=g;b=b+8|0;k=k+8|0;if((c|0)!=(k|0)){continue}break}}c=m&7;if(!c){break b}while(1){H[N(b,1080)+h>>2]=g;b=b+1|0;n=n+1|0;if((c|0)!=(n|0)){continue}break}}c=0;if(!Wc(a,0,o,f+12|0,d)){Ca(d,1,4565,0);break a}if(H[f+12>>2]){Ca(d,1,4565,0);break a}if(H[a+8>>2]==16){b=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{b=H[a+12>>2]}if(K[H[a+96>>2]+16>>2]>=2){b=H[b+5584>>2];j=H[b+4>>2]<<2;n=b+944|0;e=b+812|0;k=1;c=b;while(1){H[c+1084>>2]=H[b+4>>2];H[c+1088>>2]=H[b+8>>2];H[c+1092>>2]=H[b+12>>2];H[c+1096>>2]=H[b+16>>2];H[c+1100>>2]=H[b+20>>2];d=!j;if(!d){B(c+1892|0,e,j)}if(!d){B(c+2024|0,n,j)}c=c+1080|0;k=k+1|0;if(k>>>0<K[H[a+96>>2]+16>>2]){continue}break}}c=1}oa=f+16|0;return c|0}function jc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;H[a+8>>2]=0;H[a+12>>2]=0;H[a>>2]=b;H[a+28>>2]=d;H[a+16>>2]=0;H[a+20>>2]=0;h=c-1|0;H[a+24>>2]=h;n=b&3;a:{if((c|0)<=0){e=b;b=d;break a}e=b+1|0;H[a>>2]=e;b=I[b|0]}g=b;i=8;H[a+16>>2]=8;j=(g|0)==255;H[a+20>>2]=j;H[a+8>>2]=g;H[a+12>>2]=0;b:{if((n|0)==3){break b}k=c-2|0;H[a+24>>2]=k;c:{if((c|0)<2){b=e;e=d;break c}b=e+1|0;H[a>>2]=b;e=I[e|0]}j=(e|0)==255;H[a+20>>2]=j;i=(g|0)==255?15:16;H[a+16>>2]=i;g=g|e<<8;H[a+8>>2]=g;H[a+12>>2]=0;if((n|0)==2){e=b;c=h;h=k;break b}o=c-3|0;H[a+24>>2]=o;d:{if((c|0)<3){f=b;b=d;break d}f=b+1|0;H[a>>2]=f;b=I[b|0]}j=(b|0)==255;H[a+20>>2]=j;l=((e|0)==255?7:8)+i|0;H[a+16>>2]=l;e=i&31;if((i&63)>>>0>=32){m=b<<e;e=0}else{m=(1<<e)-1&b>>>32-e;e=b<<e}g=e|g;H[a+8>>2]=g;H[a+12>>2]=m;if((n|0)==1){e=f;i=l;c=k;h=o;break b}h=c-4|0;H[a+24>>2]=h;e:{if((c|0)<4){e=f;c=d;break e}e=f+1|0;H[a>>2]=e;c=I[f|0]}j=(c|0)==255;H[a+20>>2]=j;i=l+((b|0)==255?7:8)|0;H[a+16>>2]=i;b=l&31;if((l&63)>>>0>=32){f=c<<b;b=0}else{f=(1<<b)-1&c>>>32-b;b=c<<b}g=b|g;b=f|m;m=b;H[a+8>>2]=g;H[a+12>>2]=b;c=o}f:{if((c|0)>=5){d=H[e>>2];H[a+24>>2]=c-5;H[a>>2]=e+4;break f}b=0;d=d?-1:0;if((c|0)<2){break f}while(1){c=e+1|0;H[a>>2]=c;e=I[e|0];f=h-1|0;H[a+24>>2]=f;d=(255<<b^-1)&d|e<<b;b=b+8|0;k=h>>>0>1;e=c;h=f;if(k){continue}break}}b=d>>>24|0;H[a+20>>2]=(b|0)==255;c=d>>>16&255;e=d>>>8&255;h=j?7:8;d=d&255;f=h+((d|0)==255?7:8)|0;k=((e|0)==255?7:8)+f|0;H[a+16>>2]=(((c|0)==255?7:8)+i|0)+k;b=d|(e<<h|c<<f|b<<k);c=a;c=a;a=b;b=i&31;if((i&63)>>>0>=32){d=a<<b;a=0}else{d=(1<<b)-1&a>>>32-b;a=a<<b}H[c+8>>2]=a|g;H[c+12>>2]=d|m}function Ab(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;j=oa-32|0;oa=j;p=H[a+16>>2];a:{if(!p){k=1;break a}d=H[a>>2];c=d>>31;h=c;b:{if((c|0)<0){break b}e=H[a+4>>2];c=e>>31;l=c;if((c|0)<0){break b}f=H[a+8>>2];c=f>>31;m=c;if((c|0)<0){break b}i=H[a+12>>2];c=i>>31;if((c|0)<0){break b}a=H[a+24>>2];s=d-1|0;t=h-!d|0;u=e-1|0;v=l-!e|0;w=f-1|0;x=m-!f|0;y=i-1|0;z=c-!i|0;while(1){c=t;d=H[a>>2];e=d+s|0;c=d>>>0>e>>>0?c+1|0:c;h=Ke(e,c,d,0);H[a+16>>2]=h;c=v;e=H[a+4>>2];f=e+u|0;c=e>>>0>f>>>0?c+1|0:c;l=Ke(f,c,e,0);H[a+20>>2]=l;i=H[a+40>>2];f=i&31;if((i&63)>>>0>=32){c=1<<f;g=0}else{g=1<<f;c=g-1&1>>>32-f}n=g;k=c;f=n-1|0;c=c-!n|0;m=c;q=d>>31;g=q+x|0;r=d+w|0;g=r>>>0<d>>>0?g+1|0:g;d=Je(r,g,d,q);c=(d>>31)+c|0;g=d;d=d+f|0;c=g>>>0>d>>>0?c+1|0:c;g=d;d=i&31;if((i&63)>>>0>=32){d=c>>d}else{d=((1<<d)-1&c)<<32-d|g>>>d}c=(h>>31)+m|0;g=h;h=f+h|0;c=g>>>0>h>>>0?c+1|0:c;g=d;d=i&31;if((i&63)>>>0>=32){c=c>>d}else{c=((1<<d)-1&c)<<32-d|h>>>d}c=g-c|0;if((c|0)<0){H[j+4>>2]=c;H[j>>2]=o;Ca(b,1,13510,j);k=0;break a}H[a+8>>2]=c;d=e>>31;c=d+z|0;h=e+y|0;c=h>>>0<e>>>0?c+1|0:c;d=Je(h,c,e,d);c=(d>>31)+m|0;e=d;d=d+f|0;c=e>>>0>d>>>0?c+1|0:c;e=d;d=i&31;if((i&63)>>>0>=32){e=c>>d}else{e=((1<<d)-1&c)<<32-d|e>>>d}c=k+(l>>31)|0;d=l+n|0;c=d>>>0<n>>>0?c+1|0:c;f=d-1|0;h=e;d=c-!d|0;e=f;c=i&31;if((i&63)>>>0>=32){c=d>>c}else{c=((1<<c)-1&d)<<32-c|e>>>c}c=h-c|0;if((c|0)<0){H[j+20>>2]=c;H[j+16>>2]=o;Ca(b,1,13579,j+16|0);k=0;break a}H[a+12>>2]=c;a=a+52|0;k=1;o=o+1|0;if((p|0)!=(o|0)){continue}break}break a}Ca(b,1,6720,0)}oa=j+32|0;return k}function sc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;j=oa-256|0;oa=j;f=Fa(1,20);a:{if(!f){Ca(c,1,6413,0);f=0;break a}H[f+4>>2]=b;H[f>>2]=a;H[j>>2]=b;H[j+128>>2]=a;while(1){p=g;g=g+1|0;h=g<<2;d=(b+1|0)/2|0;H[h+j>>2]=d;m=h+(j+128|0)|0;h=(a+1|0)/2|0;H[m>>2]=h;i=N(a,b);e=i+e|0;b=d;a=h;if(i>>>0>1){continue}break}H[f+8>>2]=e;if(!e){Da(f);f=0;break a}d=Fa(e,16);H[f+12>>2]=d;if(!d){Ca(c,1,3564,0);Da(f);f=0;break a}l=H[f+8>>2];H[f+16>>2]=l<<4;a=d;if(p){e=(N(H[f+4>>2],H[f>>2])<<4)+d|0;b=e;while(1){c=n<<2;i=H[c+j>>2];b:{if((i|0)<=0){break b}o=i-1|0;h=0;c:{c=H[c+(j+128|0)>>2];if((c|0)<=0){g=0;if((i|0)!=1){k=i&2147483646;while(1){m=(g|0)==(o|0);g=g+2|0;e=m?b:e;b=e;h=h+2|0;if((k|0)!=(h|0)){continue}break}}if(i&1){break c}b=e;break b}while(1){g=e;e=c;while(1){d:{H[a>>2]=b;if((e|0)==1){a=a+16|0;b=b+16|0;break d}H[a+16>>2]=b;b=b+16|0;a=a+32|0;k=(e|0)>2;e=e-2|0;if(k){continue}}break}k=((h|0)==(o|0)|h)&1;e=k?b:g+(c<<4)|0;b=k?b:g;h=h+1|0;if((i|0)!=(h|0)){continue}break}break b}g=((g|0)==(o|0)|g)&1;c=g?b:(c<<4)+e|0;b=g?b:e;e=c}n=n+1|0;if((n|0)!=(p|0)){continue}break}}H[a>>2]=0;e:{if(!l){break e}if(l>>>0>=4){a=l&-4;b=0;while(1){H[d+60>>2]=0;H[d+52>>2]=999;H[d+56>>2]=0;H[d+44>>2]=0;H[d+36>>2]=999;H[d+40>>2]=0;H[d+28>>2]=0;H[d+20>>2]=999;H[d+24>>2]=0;H[d+12>>2]=0;H[d+4>>2]=999;H[d+8>>2]=0;d=d- -64|0;b=b+4|0;if((a|0)!=(b|0)){continue}break}}a=l&3;if(!a){break e}b=0;while(1){H[d+12>>2]=0;H[d+4>>2]=999;H[d+8>>2]=0;d=d+16|0;b=b+1|0;if((a|0)!=(b|0)){continue}break}}}oa=j+256|0;return f}function Ia(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;if(!b){return 0}a:{if(!a){a=kb(8,b);break a}if(!b){Da(a);a=0;break a}b:{if(b>>>0>4294967239){break b}h=b>>>0<=8?8:b+3&-4;b=h+8|0;c:{d:{k=a-4|0;f=k;c=H[f>>2];e=c+f|0;j=H[e>>2];g=j+e|0;e:{f:{if(H[g-4>>2]!=(j|0)){d=b+f|0;if(d+16>>>0<=g>>>0){c=H[e+4>>2];e=H[e+8>>2];H[c+8>>2]=e;H[e+4>>2]=c;c=g-d|0;H[d>>2]=c;H[(d+(c&-4)|0)-4>>2]=c|1;e=H[d>>2]-8|0;g:{if(e>>>0<=127){c=(e>>>3|0)-1|0;break g}g=Q(e);c=((e>>>29-g^4)-(g<<2)|0)+110|0;if(e>>>0<=4095){break g}c=((e>>>30-g^2)-(g<<1)|0)+71|0;c=c>>>0>=63?63:c}e=c<<4;H[d+4>>2]=e+26400;e=e+26408|0;H[d+8>>2]=H[e>>2];H[e>>2]=d;H[H[d+8>>2]+4>>2]=d;e=H[6859];d=c&31;if((c&63)>>>0>=32){c=1<<d;g=0}else{g=1<<d;c=g-1&1>>>32-d}H[6858]=g|H[6858];H[6859]=c|e;H[f>>2]=b;break d}if(d>>>0>g>>>0){break f}b=H[e+4>>2];d=H[e+8>>2];H[b+8>>2]=d;H[d+4>>2]=b;b=c+j|0;H[f>>2]=b;break d}if(c>>>0>=b+16>>>0){H[f>>2]=b;H[(f+(b&-4)|0)-4>>2]=b;d=b+f|0;b=c-b|0;H[d>>2]=b;H[(d+(b&-4)|0)-4>>2]=b|1;c=H[d>>2]-8|0;h:{if(c>>>0<=127){b=(c>>>3|0)-1|0;break h}f=Q(c);b=((c>>>29-f^4)-(f<<2)|0)+110|0;if(c>>>0<=4095){break h}b=((c>>>30-f^2)-(f<<1)|0)+71|0;b=b>>>0>=63?63:b}c=b<<4;H[d+4>>2]=c+26400;c=c+26408|0;H[d+8>>2]=H[c>>2];H[c>>2]=d;H[H[d+8>>2]+4>>2]=d;c=H[6859];d=b&31;if((b&63)>>>0>=32){b=1<<d;e=0}else{e=1<<d;b=e-1&1>>>32-d}H[6858]=e|H[6858];H[6859]=b|c;d=1;break c}d=1;if(b>>>0<=c>>>0){break e}}d=0}break c}H[(f+(b&-4)|0)-4>>2]=b;d=1}if(d){break a}b=kb(8,h);if(!b){break b}i=H[k>>2]-8|0;eb(b,a,h>>>0<i>>>0?h:i);Da(a);i=b}a=i}return a}function Qb(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0;a:{d=Fa(1,48);if(d){b=H[a+224>>2];c=H[b+4>>2];H[d>>2]=H[b>>2];H[d+4>>2]=c;c=H[b+12>>2];H[d+8>>2]=H[b+8>>2];H[d+12>>2]=c;c=H[b+20>>2];H[d+16>>2]=H[b+16>>2];H[d+20>>2]=c;c=H[b+24>>2];H[d+24>>2]=c;f=Ga(N(c,24));H[d+28>>2]=f;if(!f){Da(d);return 0}b=H[H[a+224>>2]+28>>2];b:{if(b){c=N(H[d+24>>2],24);if(!c){break b}B(f,b,c);break b}Da(f);H[d+28>>2]=0}c=H[H[a+224>>2]+36>>2];H[d+36>>2]=c;b=Fa(c,40);H[d+40>>2]=b;if(!b){Da(H[d+28>>2]);Da(d);return 0}c:{if(H[H[a+224>>2]+40>>2]){if(!H[d+36>>2]){break c}while(1){e=N(h,40);c=H[(e+H[H[a+224>>2]+40>>2]|0)+20>>2];H[(b+e|0)+20>>2]=c;g=Ga(N(c,24));c=H[d+40>>2];f=c+e|0;H[f+24>>2]=g;if(!g){if(h){b=0;while(1){Da(H[(H[d+40>>2]+N(b,40)|0)+24>>2]);b=b+1|0;if((h|0)!=(b|0)){continue}break}c=H[d+40>>2]}break a}b=H[(e+H[H[a+224>>2]+40>>2]|0)+24>>2];d:{if(b){c=N(H[f+20>>2],24);if(c){B(g,b,c)}b=H[d+40>>2];break d}Da(g);b=H[d+40>>2];H[(e+b|0)+24>>2]=0}c=H[(e+H[H[a+224>>2]+40>>2]|0)+4>>2];H[(b+e|0)+4>>2]=c;g=Ga(N(c,24));c=H[d+40>>2];f=c+e|0;H[f+16>>2]=g;if(!g){if(h){b=0;while(1){a=N(b,40);Da(H[(a+H[d+40>>2]|0)+24>>2]);Da(H[(a+H[d+40>>2]|0)+16>>2]);b=b+1|0;if((h|0)!=(b|0)){continue}break}c=H[d+40>>2]}break a}b=H[(e+H[H[a+224>>2]+40>>2]|0)+16>>2];e:{if(b){c=N(H[f+4>>2],24);if(c){B(g,b,c)}b=H[d+40>>2];break e}Da(g);b=H[d+40>>2];H[(e+b|0)+16>>2]=0}c=b+e|0;H[c+32>>2]=0;H[c+36>>2]=0;h=h+1|0;if(h>>>0<K[d+36>>2]){continue}break}break c}Da(b);H[d+40>>2]=0}}else{d=0}return d|0}Da(c);Da(H[d+28>>2]);Da(d);return 0}function kb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{while(1){if(a-1&a|b>>>0>4294967239){break b}j=a>>>0>8;a=j?a:8;d=H[6859];e=d;g=H[6858];b=b>>>0<=8?8:b+3&-4;c:{if(b>>>0<=127){i=(b>>>3|0)-1|0;break c}c=Q(b);i=((b>>>29-c^4)-(c<<2)|0)+110|0;if(b>>>0<=4095){break c}c=((b>>>30-c^2)-(c<<1)|0)+71|0;i=c>>>0>=63?63:c}h=i;f=h&31;if((h&63)>>>0>=32){c=0;d=d>>>f|0}else{c=d>>>f|0;d=((1<<f)-1&d)<<32-f|g>>>f}if(d|c){while(1){f=c;d:{if(c|d){e=c-1|0;g=e+1|0;i=e;e=d-1|0;g=(e|0)!=-1?g:i;c=Q(c^g);c=(c|0)==32?Q(d^e)+32|0:c;e=63-c|0;ra=0-(c>>>0>63)|0;break d}ra=0;e=64}g=e;e=g&31;if((g&63)>>>0>=32){c=0;i=f>>>e|0}else{c=f>>>e|0;i=((1<<e)-1&f)<<32-e|d>>>e}h=g+h|0;d=h<<4;f=H[d+26408>>2];e=d+26400|0;e:{if((f|0)!=(e|0)){d=Ib(f,a,b);if(d){break a}d=H[f+4>>2];g=H[f+8>>2];H[d+8>>2]=g;H[g+4>>2]=d;H[f+8>>2]=e;H[f+4>>2]=H[e+4>>2];H[e+4>>2]=f;H[H[f+4>>2]+8>>2]=f;h=h+1|0;d=(c&1)<<31|i>>>1;c=c>>>1|0;break e}d=H[6859];k=27432,l=H[6858]&Ne(-2,-1,h),H[k>>2]=l;H[6859]=ra&d;d=i^1}if(c|d){continue}break}g=H[6858];e=H[6859]}c=Q(e);f=63-((c|0)==32?Q(g)+32|0:c)|0;f:{if(!(e|g)){c=0;break f}d=f<<4;c=H[d+26408>>2];if(!e&g>>>0<1073741824){break f}h=99;e=d+26400|0;if((e|0)==(c|0)){break f}while(1){if(!h){break f}d=Ib(c,a,b);if(d){break a}h=h-1|0;c=H[c+8>>2];if((e|0)!=(c|0)){continue}break}}if(Lc((j?a+48|0:48)+b|0)){continue}break}if(!c){break b}f=(f<<4)+26400|0;if((f|0)==(c|0)){break b}while(1){d=Ib(c,a,b);if(d){break a}c=H[c+8>>2];if((f|0)!=(c|0)){continue}break}}d=0}return d}function Fd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=H[a+48>>2];if(e>>>0>=b>>>0){H[a+48>>2]=e-b;H[a+36>>2]=H[a+36>>2]+b;e=c+H[a+60>>2]|0;d=b+H[a+56>>2]|0;e=d>>>0<b>>>0?e+1|0:e;H[a+56>>2]=d;H[a+60>>2]=e;ra=c;return b|0}if(I[a+68|0]&4){H[a+48>>2]=0;H[a+36>>2]=e+H[a+36>>2];g=H[a+60>>2];c=H[a+56>>2];b=c+e|0;H[a+56>>2]=b;H[a+60>>2]=b>>>0<c>>>0?g+1|0:g;ra=e?0:-1;return(e?e:-1)|0}if(e){H[a+48>>2]=0;H[a+36>>2]=H[a+32>>2];h=b;f=e;b=b-e|0;c=c-(e>>>0>h>>>0)|0}a:{if((c|0)>0){h=1}else{h=!!b&(c|0)>=0}if(h){while(1){h=H[a+12>>2];e=c+g|0;i=b+f|0;e=H[a+60>>2]+(i>>>0<f>>>0?e+1|0:e)|0;j=i;i=i+H[a+56>>2]|0;e=j>>>0>i>>>0?e+1|0:e;if((e|0)==(h|0)&i>>>0>K[a+8>>2]|e>>>0>h>>>0){Ca(d,4,15630,0);H[a+48>>2]=0;H[a+36>>2]=H[a+32>>2];b=g+H[a+60>>2]|0;c=f+H[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;H[a+56>>2]=c;H[a+60>>2]=b;d=H[a+8>>2];f=d-c|0;e=H[a+12>>2];g=e-((c>>>0>d>>>0)+b|0)|0;h=sa[H[a+28>>2]](d,e,H[a>>2])|0;i=H[a+68>>2];if(h){H[a+56>>2]=d;H[a+60>>2]=e}H[a+68>>2]=i|4;a=(c|0)==(d|0)&(b|0)==(e|0);b=a?-1:f;break a}e=sa[H[a+24>>2]](b,c,H[a>>2])|0;h=ra;i=h;if((e&i)==-1){Ca(d,4,15630,0);H[a+68>>2]=H[a+68>>2]|4;e=g+H[a+60>>2]|0;b=f+H[a+56>>2]|0;e=b>>>0<f>>>0?e+1|0:e;H[a+56>>2]=b;H[a+60>>2]=e;a=!(g|f);b=a?-1:f;break a}g=g+i|0;f=e+f|0;g=f>>>0<e>>>0?g+1|0:g;h=b;b=b-e|0;c=c-((e>>>0>h>>>0)+i|0)|0;if(!!b&(c|0)>=0|(c|0)>0){continue}break}}b=g+H[a+60>>2]|0;c=f+H[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;H[a+56>>2]=c;H[a+60>>2]=b;ra=g;return f|0}ra=a?-1:g;return b|0}function Jd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=oa-80|0;oa=e;a:{if(c>>>0<=2){Ca(d,1,14478,0);break a}if(I[a+124|0]){Ca(d,4,11193,0);g=1;break a}g=1;Ea(b,a+40|0,1);Ea(b+1|0,a+52|0,1);Ea(b+2|0,a+44|0,1);f=b+3|0;b:{c:{d:{e:{f:{h=H[a+40>>2];switch(h-1|0){case 0:break f;case 1:break e;default:break d}}if(c>>>0<=6){H[e+16>>2]=c;Ca(d,1,15155,e+16|0);g=0;break a}if(!((c|0)==7|H[a+48>>2]==14)){H[e+48>>2]=c;Ca(d,2,15155,e+48|0)}Ea(f,a+48|0,4);if(H[a+48>>2]!=14){break b}f=Ga(36);if(!f){g=0;Ca(d,1,7993,0);break a}H[f>>2]=14;H[e+64>>2]=0;H[e+56>>2]=0;H[e+72>>2]=0;H[e+60>>2]=0;H[e+68>>2]=0;H[e+76>>2]=0;g=4470064;H[e+52>>2]=4470064;H[f+4>>2]=1145390592;g:{if((c|0)!=7){if((c|0)==35){Ea(b+7|0,e+76|0,4);Ea(b+11|0,e+72|0,4);Ea(b+15|0,e+68|0,4);Ea(b+19|0,e- -64|0,4);Ea(b+23|0,e+60|0,4);Ea(b+27|0,e+56|0,4);Ea(b+31|0,e+52|0,4);H[f+4>>2]=0;g=H[e+52>>2];c=H[e+56>>2];d=H[e+64>>2];i=H[e+68>>2];j=H[e+76>>2];h=H[e+72>>2];b=H[e+60>>2];break g}H[e+32>>2]=c;Ca(d,2,15191,e+32|0)}c=0;d=0;h=0;b=0}H[f+24>>2]=b;H[f+16>>2]=i;H[f+8>>2]=j;H[f+32>>2]=g;H[f+28>>2]=c;H[f+20>>2]=d;H[f+12>>2]=h;H[a+112>>2]=0;H[a+108>>2]=f;break b}b=c-3|0;H[a+112>>2]=b;d=Fa(1,b);H[a+108>>2]=d;if(!d){break c}if((c|0)<=3){break b}c=0;while(1){Ea(f,e+76|0,1);F[H[a+108>>2]+c|0]=H[e+76>>2];f=f+1|0;c=c+1|0;if((b|0)!=(c|0)){continue}break}break b}if(h>>>0<3){break a}H[e>>2]=h;Ca(d,4,15950,e);break a}g=0;H[a+112>>2]=0;break a}g=1;F[a+124|0]=1}oa=e+80|0;return g|0}function Ka(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;h=H[a+48>>2];if(h>>>0>=c>>>0){if(c){B(b,H[a+36>>2],c)}H[a+36>>2]=H[a+36>>2]+c;H[a+48>>2]=H[a+48>>2]-c;b=H[a+60>>2];d=H[a+56>>2]+c|0;b=d>>>0<c>>>0?b+1|0:b;H[a+56>>2]=d;H[a+60>>2]=b;return c}if(I[a+68|0]&4){if(h){B(b,H[a+36>>2],h)}b=H[a+48>>2];H[a+48>>2]=0;H[a+36>>2]=b+H[a+36>>2];g=H[a+60>>2];c=b;b=H[a+56>>2]+b|0;g=c>>>0>b>>>0?g+1|0:g;H[a+56>>2]=b;H[a+60>>2]=g;return h?h:-1}a:{if(h){if(h){B(b,H[a+36>>2],h)}i=H[a+32>>2];H[a+36>>2]=i;e=H[a+48>>2];H[a+48>>2]=0;f=H[a+60>>2];g=H[a+56>>2]+e|0;f=g>>>0<e>>>0?f+1|0:f;H[a+56>>2]=g;H[a+60>>2]=f;c=c-e|0;b=b+e|0;break a}i=H[a+32>>2];H[a+36>>2]=i}b:{while(1){c:{e=H[a>>2];f=H[a+16>>2];g=H[a+64>>2];d:{if(g>>>0>c>>>0){f=sa[f|0](i,g,e)|0;H[a+48>>2]=f;if((f|0)==-1){break b}if(c>>>0>f>>>0){if(f){B(b,H[a+36>>2],f)}i=H[a+32>>2];H[a+36>>2]=i;e=H[a+48>>2];break d}if(c){B(b,H[a+36>>2],c)}H[a+36>>2]=H[a+36>>2]+c;H[a+48>>2]=H[a+48>>2]-c;b=H[a+60>>2];d=H[a+56>>2]+c|0;b=d>>>0<c>>>0?b+1|0:b;H[a+56>>2]=d;H[a+60>>2]=b;return c+h|0}e=sa[f|0](b,c,e)|0;H[a+48>>2]=e;if((e|0)==-1){break b}if(c>>>0<=e>>>0){break c}i=H[a+32>>2];H[a+36>>2]=i;f=e}H[a+48>>2]=0;g=H[a+60>>2];j=H[a+56>>2]+e|0;g=j>>>0<e>>>0?g+1|0:g;H[a+56>>2]=j;H[a+60>>2]=g;b=b+e|0;c=c-e|0;h=f+h|0;continue}break}H[a+48>>2]=0;H[a+36>>2]=H[a+32>>2];f=H[a+60>>2];b=H[a+56>>2]+e|0;f=b>>>0<e>>>0?f+1|0:f;H[a+56>>2]=b;H[a+60>>2]=f;return e+h|0}Ca(d,4,15630,0);H[a+48>>2]=0;H[a+68>>2]=H[a+68>>2]|4;return h?h:-1}function Rb(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;g=oa-16|0;oa=g;o=H[H[a+96>>2]+16>>2];b=Fa(1,56);H[g+12>>2]=b;a:{if(!b){break a}j=H[H[a+96>>2]+16>>2];H[b+24>>2]=j;H[b>>2]=H[a+108>>2];H[b+4>>2]=H[a+112>>2];H[b+8>>2]=H[a+116>>2];H[b+12>>2]=H[a+120>>2];H[b+16>>2]=H[a+128>>2];h=H[a+132>>2];H[b+52>>2]=0;H[b+20>>2]=h;i=H[a+12>>2];H[b+32>>2]=H[i>>2];H[b+36>>2]=H[i+4>>2];H[b+40>>2]=H[i+8>>2];H[b+44>>2]=H[i+16>>2];a=Fa(j,1080);H[b+48>>2]=a;if(a){if(o){while(1){a=N(k,1080);d=a+H[b+48>>2]|0;c=a+H[i+5584>>2]|0;H[d+4>>2]=H[c>>2];a=H[c+4>>2];H[d+8>>2]=a;H[d+12>>2]=H[c+8>>2];H[d+16>>2]=H[c+12>>2];H[d+20>>2]=H[c+16>>2];H[d+24>>2]=H[c+20>>2];b:{if(a>>>0>32){break b}if(a){B(d+948|0,c+944|0,a)}a=H[c+4>>2];if(!a){break b}B(d+816|0,c+812|0,a)}a=H[c+24>>2];H[d+28>>2]=a;H[d+808>>2]=H[c+804>>2];f=1;c:{if((a|0)!=1){a=N(H[c+4>>2],3);if(a-3>>>0>95){break c}f=a-2|0}p=f&1;l=d+420|0;m=d+32|0;n=c+28|0;a=0;if((f|0)!=1){j=f&-2;f=0;while(1){h=a<<2;e=(a<<3)+n|0;H[h+m>>2]=H[e+4>>2];H[h+l>>2]=H[e>>2];e=a|1;h=e<<2;e=(e<<3)+n|0;H[h+m>>2]=H[e+4>>2];H[h+l>>2]=H[e>>2];a=a+2|0;f=f+2|0;if((j|0)!=(f|0)){continue}break}}if(!p){break c}e=a<<2;a=(a<<3)+n|0;H[e+m>>2]=H[a+4>>2];H[e+l>>2]=H[a>>2]}H[d+812>>2]=H[c+808>>2];k=k+1|0;if((k|0)!=(o|0)){continue}break}}e=b;break a}if(g+12|0){a=H[g+12>>2];b=H[a+48>>2];if(b){Da(b);a=H[g+12>>2]}Da(a);H[g+12>>2]=0}}oa=g+16|0;return e|0}function kc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;f=H[a+28>>2]+N(b,152)|0;d=H[f-144>>2]-H[f-152>>2]|0;e=H[f-140>>2]-H[f-148>>2]|0;c=e>>>0>=64?64:e;g=d>>>0>=64?64:d;a:{if(!(!d|!e|(!g|!c)|g>>>0>4294967295/(c>>>0)>>>2>>>0)){f=Fa(1,28);H[f+12>>2]=c;H[f+8>>2]=g;H[f+4>>2]=e;H[f>>2]=d;h=e;e=c+e|0;i=h>>>0>e>>>0?1:i;e=Ke(e-1|0,i-!e|0,c,0);H[f+20>>2]=e;c=0;h=d;d=d+g|0;c=h>>>0>d>>>0?1:c;c=Ke(d-1|0,c-!d|0,g,0);H[f+16>>2]=c;Ie(e,0,c);b:{if(ra){break b}c=Fa(4,N(c,e));H[f+24>>2]=c;if(!c){break b}break a}Da(f)}f=0}if(!f){return 0}c:{if(b){while(1){o=N(n,152);e=o+H[a+28>>2]|0;c=H[e+24>>2];if(c){r=e+28|0;d=H[e+20>>2];g=H[e+16>>2];l=0;while(1){if(N(d,g)){i=N(l,36)+r|0;m=0;while(1){k=H[i+20>>2]+N(m,40)|0;c=H[k+20>>2];j=H[k+16>>2];if(N(c,j)){g=0;while(1){d=H[k+24>>2]+N(g,68)|0;p=H[d+60>>2];if(p){j=H[d+12>>2];s=H[d+20>>2];t=H[d+16>>2];q=H[d+8>>2];d=q-H[i>>2]|0;h=H[i+16>>2];if(h&1){c=H[a+28>>2]+o|0;d=(H[c-144>>2]+d|0)-H[c-152>>2]|0}c=j-H[i+4>>2]|0;if(h&2){h=c;c=H[a+28>>2]+o|0;c=(h+H[c-140>>2]|0)-H[c-148>>2]|0}h=d;d=t-q|0;if(!ab(f,h,c,h+d|0,(s-j|0)+c|0,p,1,d)){break c}j=H[k+16>>2];c=H[k+20>>2]}g=g+1|0;if(g>>>0<N(c,j)>>>0){continue}break}g=H[e+16>>2];d=H[e+20>>2]}m=m+1|0;if(m>>>0<N(d,g)>>>0){continue}break}c=H[e+24>>2]}l=l+1|0;if(l>>>0<c>>>0){continue}break}}n=n+1|0;if((n|0)!=(b|0)){continue}break}}return f}Xa(f);return 0}function Ob(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;a:{b:{e=H[a+60>>2];if(!e){if(H[b+16>>2]){break b}return 1}i=Ga(N(e,52));if(!i){break a}e=0;if(H[b+16>>2]){d=H[b+24>>2];while(1){e=N(f,52);Da(H[(e+d|0)+44>>2]);d=H[b+24>>2];H[(e+d|0)+44>>2]=0;f=f+1|0;e=H[b+16>>2];if(f>>>0<e>>>0){continue}break}}if(H[a+60>>2]){f=H[H[a+100>>2]+24>>2];e=0;while(1){h=N(H[H[a+64>>2]+(e<<2)>>2],52);d=h+f|0;c=H[d+4>>2];g=i+N(e,52)|0;H[g>>2]=H[d>>2];H[g+4>>2]=c;H[g+48>>2]=H[d+48>>2];c=H[d+44>>2];H[g+40>>2]=H[d+40>>2];H[g+44>>2]=c;c=H[d+36>>2];H[g+32>>2]=H[d+32>>2];H[g+36>>2]=c;c=H[d+28>>2];H[g+24>>2]=H[d+24>>2];H[g+28>>2]=c;c=H[d+20>>2];H[g+16>>2]=H[d+16>>2];H[g+20>>2]=c;c=H[d+12>>2];H[g+8>>2]=H[d+8>>2];H[g+12>>2]=c;f=H[H[a+100>>2]+24>>2];c=h+f|0;H[g+36>>2]=H[c+36>>2];H[g+44>>2]=H[c+44>>2];H[c+44>>2]=0;e=e+1|0;c=H[a+60>>2];if(e>>>0<c>>>0){continue}break}e=H[b+16>>2]}if(e){d=H[H[a+100>>2]+24>>2];f=0;while(1){c=N(f,52);Da(H[(c+d|0)+44>>2]);d=H[H[a+100>>2]+24>>2];H[(c+d|0)+44>>2]=0;f=f+1|0;if(f>>>0<K[b+16>>2]){continue}break}c=H[a+60>>2]}H[b+16>>2]=c;Da(H[b+24>>2]);H[b+24>>2]=i;return 1}e=H[b+24>>2];f=H[H[a+100>>2]+24>>2];while(1){h=N(d,52);c=h+e|0;H[c+36>>2]=H[(f+h|0)+36>>2];Da(H[c+44>>2]);e=H[b+24>>2];f=H[H[a+100>>2]+24>>2];c=h+f|0;H[(h+e|0)+44>>2]=H[c+44>>2];H[c+44>>2]=0;d=d+1|0;if(d>>>0<K[b+16>>2]){continue}break}return 1}Va(H[a+96>>2]);H[a+96>>2]=0;return 0}function oe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=oa-16|0;oa=h;if(H[a+8>>2]==16){f=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{f=H[a+12>>2]}a:{if(c>>>0<=1){Ca(d,1,4132,0);a=0;break a}Ea(b,h+12|0,2);b:{if(H[h+12>>2]){Ca(d,2,3608,0);break b}if(c>>>0<=6){Ca(d,1,4132,0);a=0;break a}Ea(b+2|0,h+12|0,2);e=H[f+5616>>2];k=I[h+12|0];c:{d:{e:{g=H[f+5620>>2];if(!g){a=e;break e}a=e;while(1){if(H[a+8>>2]==(k|0)){break e}a=a+20|0;i=i+1|0;if((i|0)!=(g|0)){continue}break}break d}if((g|0)!=(i|0)){break c}}if(H[f+5624>>2]==(g|0)){a=g+10|0;H[f+5624>>2]=a;a=Ia(e,N(a,20));e=H[f+5616>>2];if(!a){Da(e);H[f+5624>>2]=0;H[f+5616>>2]=0;H[f+5620>>2]=0;Ca(d,1,4158,0);a=0;break a}f:{if((a|0)==(e|0)){break f}l=H[f+5632>>2];if(!l){break f}m=H[f+5628>>2];i=0;while(1){g=N(i,20)+m|0;j=H[g+8>>2];if(j){H[g+8>>2]=a+(j-e|0)}j=H[g+12>>2];if(j){H[g+12>>2]=a+(j-e|0)}i=i+1|0;if((l|0)!=(i|0)){continue}break}}H[f+5616>>2]=a;e=H[f+5620>>2];g=N(H[f+5624>>2]-e|0,20);if(g){y(a+N(e,20)|0,0,g)}g=H[f+5620>>2];e=H[f+5616>>2]}H[f+5620>>2]=g+1;a=N(g,20)+e|0}e=H[a+12>>2];if(e){Da(e);H[a+12>>2]=0;H[a+16>>2]=0}H[a+8>>2]=k;e=H[h+12>>2];H[a>>2]=e>>>10&3;H[a+4>>2]=e>>>8&3;Ea(b+4|0,h+12|0,2);if(H[h+12>>2]){Ca(d,2,3023,0);break b}c=c-6|0;e=Ga(c);H[a+12>>2]=e;if(!e){Ca(d,1,4132,0);a=0;break a}if(c){B(e,b+6|0,c)}H[a+16>>2]=c}a=1}oa=h+16|0;return a|0}function Wa(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{if(!H[a+12>>2]){k=1;if(H[a+4>>2]>0|H[a+8>>2]>1){break b}break a}e=1;if(H[a+8>>2]>0){break b}if(H[a+4>>2]<2){break a}}b=H[a>>2];f=b+(e<<5)|0;g=H[a+16>>2];h=H[a+20>>2];if(g>>>0<h>>>0){d=g;while(1){c=(d<<6)+f|0;L[c>>2]=L[c>>2]*O(1.2301740646362305);L[c+4>>2]=L[c+4>>2]*O(1.2301740646362305);L[c+8>>2]=L[c+8>>2]*O(1.2301740646362305);L[c+12>>2]=L[c+12>>2]*O(1.2301740646362305);L[c+16>>2]=L[c+16>>2]*O(1.2301740646362305);L[c+20>>2]=L[c+20>>2]*O(1.2301740646362305);L[c+24>>2]=L[c+24>>2]*O(1.2301740646362305);L[c+28>>2]=L[c+28>>2]*O(1.2301740646362305);d=d+1|0;if((h|0)!=(d|0)){continue}break}}i=b+(k<<5)|0;j=H[a+28>>2];c=H[a+24>>2];if(j>>>0>c>>>0){d=c;while(1){b=(d<<6)+i|0;L[b>>2]=L[b>>2]*O(1.625732421875);L[b+4>>2]=L[b+4>>2]*O(1.625732421875);L[b+8>>2]=L[b+8>>2]*O(1.625732421875);L[b+12>>2]=L[b+12>>2]*O(1.625732421875);L[b+16>>2]=L[b+16>>2]*O(1.625732421875);L[b+20>>2]=L[b+20>>2]*O(1.625732421875);L[b+24>>2]=L[b+24>>2]*O(1.625732421875);L[b+28>>2]=L[b+28>>2]*O(1.625732421875);d=d+1|0;if((j|0)!=(d|0)){continue}break}}b=f+32|0;d=H[a+8>>2];a=H[a+4>>2];e=a-e|0;e=(d|0)<(e|0)?d:e;pb(i,b,g,h,e,O(-.4435068666934967));l=i+32|0;d=d-k|0;a=(a|0)<(d|0)?a:d;pb(f,l,c,j,a,O(-.8829110860824585));pb(i,b,g,h,e,O(.05298011749982834));pb(f,l,c,j,a,O(1.5861343145370483))}}function dc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if(a){b=H[a+20>>2];if(b){g=H[b>>2];if(g){d=H[g+20>>2];if(H[g+16>>2]){i=F[a+40|0]&1?16:17;while(1){c=H[d+28>>2];if(c){b=H[d+32>>2];l=(b>>>0)/152|0;j=0;if(b>>>0>=152){while(1){b=H[c+48>>2];if(b){f=H[c+52>>2];h=(f>>>0)/40|0;e=0;if(f>>>0>=40){while(1){bb(H[b+32>>2]);H[b+32>>2]=0;bb(H[b+36>>2]);H[b+36>>2]=0;sa[i|0](b);b=b+40|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}b=H[c+48>>2]}Da(b);H[c+48>>2]=0}b=H[c+84>>2];if(b){f=H[c+88>>2];h=(f>>>0)/40|0;e=0;if(f>>>0>=40){while(1){bb(H[b+32>>2]);H[b+32>>2]=0;bb(H[b+36>>2]);H[b+36>>2]=0;sa[i|0](b);b=b+40|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}b=H[c+84>>2]}Da(b);H[c+84>>2]=0}b=H[c+120>>2];if(b){f=H[c+124>>2];h=(f>>>0)/40|0;e=0;if(f>>>0>=40){while(1){bb(H[b+32>>2]);H[b+32>>2]=0;bb(H[b+36>>2]);H[b+36>>2]=0;sa[i|0](b);b=b+40|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}b=H[c+120>>2]}Da(b);H[c+120>>2]=0}c=c+152|0;j=j+1|0;if((l|0)!=(j|0)){continue}break}c=H[d+28>>2]}Da(c);H[d+28>>2]=0}a:{if(!H[d+40>>2]){break a}b=H[d+36>>2];if(!b){break a}Da(b);H[d+44>>2]=0;H[d+48>>2]=0;H[d+36>>2]=0;H[d+40>>2]=0}Da(H[d+52>>2]);d=d+76|0;k=k+1|0;if(k>>>0<K[g+16>>2]){continue}break}d=H[g+20>>2]}Da(d);H[g+20>>2]=0;Da(H[H[a+20>>2]>>2]);b=H[a+20>>2];H[b>>2]=0}Da(b);H[a+20>>2]=0}Da(H[a+68>>2]);Da(a)}}function lc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;c=H[a+8>>2];f=c+H[a+4>>2]|0;a:{if(!H[a+12>>2]){if((f|0)<2){break a}h=(c<<2)+b|0;d=H[h>>2];e=H[b>>2]-(d+1>>1)|0;i=H[a>>2];b:{if(f>>>0<4){c=d;break b}k=(f-4>>>1|0)+1|0;a=1;while(1){c=a<<2;m=H[c+b>>2];c=H[c+h>>2];l=i+(g<<2)|0;H[l>>2]=e;j=e;e=m-((c+d|0)+2>>2)|0;H[l+4>>2]=(j+e>>1)+d;g=g+2|0;j=(a|0)!=(k|0);d=c;a=a+1|0;if(j){continue}break}}H[i+(g<<2)>>2]=e;if(f&1){d=f-1|0;a=H[(d<<1)+b>>2]-(c+1>>1)|0;H[i+(d<<2)>>2]=a;e=a+e>>1;d=-8}else{d=-4}a=f<<2;H[d+(a+i|0)>>2]=c+e;if(!a){break a}B(b,i,a);return}c:{switch(f-1|0){case 0:H[b>>2]=H[b>>2]/2;return;case 1:a=H[a>>2];c=(c<<2)+b|0;d=H[b>>2]-(H[c>>2]+1>>1)|0;H[a+4>>2]=d;H[a>>2]=d+H[c>>2];c=H[a+4>>2];H[b>>2]=H[a>>2];H[b+4>>2]=c;return;default:break c}}if((f|0)<3){break a}h=H[a>>2];k=(c<<2)+b|0;d=H[k+4>>2];a=H[k>>2];e=H[b>>2]-((d+a|0)+2>>2)|0;H[h>>2]=e+a;g=1;m=f-2|0;l=f&1;a=!l;d:{if(m-a>>>0<2){c=d;break d}o=((f-a|0)-4>>>1|0)+1|0;a=1;while(1){p=H[(a<<2)+b>>2];j=a+1|0;c=H[k+(j<<2)>>2];n=h+(g<<2)|0;H[n>>2]=e;i=e;e=p-((c+d|0)+2>>2)|0;H[n+4>>2]=(i+e>>1)+d;g=g+2|0;i=(a|0)!=(o|0);d=c;a=j;if(i){continue}break}}H[h+(g<<2)>>2]=e;e:{if(!l){g=H[((f<<1)+b|0)-4>>2]-(c+1>>1)|0;H[h+(m<<2)>>2]=(g+e>>1)+c;break e}g=c+e|0}a=f<<2;H[(a+h|0)-4>>2]=g;if(!a){break a}B(b,h,a)}}function bc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=H[a+24>>2];j=H[e+16>>2];if(!j){return 0}f=H[e+24>>2];e=H[H[H[a+20>>2]>>2]+20>>2];a:{b:{if(!b){b=0;while(1){c=H[f+24>>2];a=H[e+28>>2]+N(H[e+24>>2],152)|0;d=H[a-140>>2];g=H[a-144>>2]-H[a-152>>2]|0;a=H[a-148>>2];h=d-a|0;Ie(g,0,h);if(!(!ra|(a|0)==(d|0))){break a}a=(c>>>3|0)+((c&7)!=0)|0;c=(a|0)==3?4:a;a=!c;d=N(g,h);Ie(c,0,d);if(!(!ra|a)){break a}a=-1;c=N(c,d);if(c>>>0>(b^-1)>>>0){break b}e=e+76|0;f=f+52|0;b=b+c|0;a=b;i=i+1|0;if((j|0)!=(i|0)){continue}break}break b}b=0;if(!H[a+64>>2]){while(1){c=H[f+24>>2];a=H[e+28>>2]+N(H[e+24>>2],152)|0;d=H[a-4>>2];g=H[a-8>>2]-H[a-16>>2]|0;a=H[a-12>>2];h=d-a|0;Ie(g,0,h);if(!(!ra|(a|0)==(d|0))){break a}a=(c>>>3|0)+((c&7)!=0)|0;c=(a|0)==3?4:a;a=!c;d=N(g,h);Ie(c,0,d);if(!(!ra|a)){break a}a=-1;c=N(c,d);if(c>>>0>(b^-1)>>>0){break b}e=e+76|0;f=f+52|0;b=b+c|0;a=b;i=i+1|0;if((j|0)!=(i|0)){continue}break}break b}while(1){c=H[f+24>>2];a=H[e+28>>2]+N(H[e+24>>2],152)|0;d=H[a-140>>2];g=H[a-144>>2]-H[a-152>>2]|0;a=H[a-148>>2];h=d-a|0;Ie(g,0,h);if(!(!ra|(a|0)==(d|0))){break a}a=(c>>>3|0)+((c&7)!=0)|0;c=(a|0)==3?4:a;a=!c;d=N(g,h);Ie(c,0,d);if(!(!ra|a)){break a}a=-1;c=N(c,d);if(c>>>0>(b^-1)>>>0){break b}e=e+76|0;f=f+52|0;b=b+c|0;a=b;i=i+1|0;if((j|0)!=(i|0)){continue}break}}return a}return-1}function Sb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;d=oa-256|0;oa=d;if(a){Pa(1806,17,c);H[d+240>>2]=H[a>>2];Ha(c,2348,d+240|0);H[d+224>>2]=H[a+4>>2];Ha(c,2361,d+224|0);H[d+208>>2]=H[a+8>>2];Ha(c,7260,d+208|0);H[d+192>>2]=H[a+16>>2];Ha(c,2319,d+192|0);if((b|0)>0){while(1){e=H[a+5584>>2];H[d+176>>2]=h;Ha(c,1844,d+176|0);e=e+N(h,1080)|0;H[d+160>>2]=H[e>>2];Ha(c,2347,d+160|0);H[d+144>>2]=H[e+4>>2];Ha(c,7374,d+144|0);H[d+128>>2]=H[e+8>>2];Ha(c,7162,d+128|0);H[d+112>>2]=H[e+12>>2];Ha(c,7178,d+112|0);H[d+96>>2]=H[e+16>>2];Ha(c,2330,d+96|0);H[d+80>>2]=H[e+20>>2];Ha(c,7440,d+80|0);Pa(1567,23,c);if(H[e+4>>2]){i=e+944|0;j=e+812|0;f=0;while(1){g=f<<2;k=H[j+g>>2];H[d+68>>2]=H[i+g>>2];H[d+64>>2]=k;Ha(c,1693,d- -64|0);f=f+1|0;if(f>>>0<K[e+4>>2]){continue}break}}Mc(c);H[d+48>>2]=H[e+24>>2];Ha(c,7194,d+48|0);H[d+32>>2]=H[e+804>>2];Ha(c,7243,d+32|0);i=1;Pa(1591,20,c);a:{if(H[e+24>>2]!=1){f=H[e+4>>2];if((f|0)<=0){break a}i=N(f,3)-2|0}j=e+28|0;f=0;while(1){g=j+(f<<3)|0;l=d,m=Ne(H[g>>2],H[g+4>>2],32),H[l+16>>2]=m;H[d+20>>2]=ra;Ha(c,1693,d+16|0);f=f+1|0;if((i|0)!=(f|0)){continue}break}}Mc(c);H[d>>2]=H[e+808>>2];Ha(c,7226,d);Pa(1707,5,c);h=h+1|0;if((h|0)!=(b|0)){continue}break}}Pa(1708,4,c)}oa=d+256|0}function Ge(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{e=b;if(e){if(!c){break j}if(!d){break i}e=Q(d)-Q(e)|0;if(e>>>0<=31){break h}break b}if((d|0)==1|d>>>0>1){break b}b=(a>>>0)/(c>>>0)|0;pa=a-N(b,c)|0;qa=0;ra=0;return b}if(!a){break g}if(!d){break f}f=d-1|0;if(f&d){break f}pa=a;qa=e&f;a=e>>>He(d)|0;ra=0;return a}f=c-1|0;if(!(f&c)){break e}k=(Q(c)+33|0)-Q(e)|0;g=0-k|0;break c}k=e+1|0;g=63-e|0;break c}pa=0;a=(e>>>0)/(d>>>0)|0;qa=e-N(a,d)|0;ra=0;return a}e=Q(d)-Q(e)|0;if(e>>>0<31){break d}break b}pa=a&f;qa=0;if((c|0)==1){break a}c=He(c);d=c&31;if((c&63)>>>0>=32){e=0;a=b>>>d|0}else{e=b>>>d|0;a=((1<<d)-1&b)<<32-d|a>>>d}ra=e;return a}k=e+1|0;g=63-e|0}f=a;e=k&63;h=e&31;if((e&63)>>>0>=32){e=0;f=b>>>h|0}else{e=b>>>h|0;f=((1<<h)-1&b)<<32-h|f>>>h}h=g&63;g=a;i=h&31;if((h&63)>>>0>=32){j=a<<i;a=0}else{j=(1<<i)-1&g>>>32-i|b<<i;a=g<<i}b=j;if(k){g=d-1|0;l=c-1|0;g=(l|0)!=-1?g+1|0:g;h=l;while(1){e=e<<1|f>>>31;f=f<<1|b>>>31;l=e;i=g-(e+(f>>>0>h>>>0)|0)|0;m=i>>31;j=m;e=f;i=c&j;f=e-i|0;e=l-((d&j)+(e>>>0<i>>>0)|0)|0;j=b<<1|a>>>31;a=n|a<<1;b=j|o;l=m&1;n=l;k=k-1|0;if(k){continue}break}}pa=f;qa=e;j=b<<1|a>>>31;a=l|a<<1;ra=j|o;return a}pa=a;qa=b;a=0;b=0}ra=b;return a}function Vc(a,b,c,d,e){var f=0,g=0,h=0,i=0;h=oa-16|0;oa=h;if(H[a+8>>2]==16){a=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{a=H[a+12>>2]}f=H[d>>2];a:{if(!f){d=0;Ca(e,1,2642,0);break a}a=H[a+5584>>2];H[d>>2]=f-1;Ea(c,h+12|0,1);g=N(b,1080)+a|0;a=H[h+12>>2];H[g+804>>2]=a>>>5;b=a&31;H[g+24>>2]=b;a=c+1|0;b:{c:{d:{e:{f:{switch(b|0){case 0:f=H[d>>2];break e;case 1:break d;default:break f}}f=H[d>>2]>>>1|0}if(f>>>0>=98){H[h+4>>2]=97;H[h+8>>2]=97;H[h>>2]=f;Ca(e,2,16056,h);b=H[g+24>>2]}if(b){b=f;if(b){break d}a=0;break c}if(f){b=g+28|0;c=0;while(1){Ea(a,h+12|0,1);if(c>>>0<=96){e=H[h+12>>2];i=b+(c<<3)|0;H[i+4>>2]=0;H[i>>2]=e>>>3}a=a+1|0;c=c+1|0;if((f|0)!=(c|0)){continue}break}}a=H[d>>2];if(a>>>0<f>>>0){d=0;break a}a=a-f|0;break b}e=g+28|0;c=0;while(1){Ea(a,h+12|0,2);if(c>>>0<=96){f=e+(c<<3)|0;i=H[h+12>>2];H[f+4>>2]=i&2047;H[f>>2]=i>>>11}a=a+2|0;c=c+1|0;if((c|0)!=(b|0)){continue}break}a=b<<1}b=H[d>>2];if(a>>>0>b>>>0){d=0;break a}a=b-a|0}H[d>>2]=a;d=1;if(H[g+24>>2]!=1){break a}f=g+28|0;c=H[g+32>>2];e=H[g+28>>2];a=1;while(1){b=f+(a<<3)|0;H[b+4>>2]=c;H[b+12>>2]=c;g=e-((a>>>0)/3|0)|0;H[b+8>>2]=(g|0)>0?g:0;g=b;b=e-((a-1>>>0)/3|0)|0;H[g>>2]=(b|0)>0?b:0;a=a+2|0;if((a|0)!=97){continue}break}}oa=h+16|0;return d}function ue(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;f=oa-32|0;oa=f;g=1;a:{if(c>>>0<=1){g=0;Ca(d,1,10062,0);break a}if(H[a+76>>2]){break a}Ea(b,f+28|0,1);Ea(b+1|0,f+24|0,1);e=H[f+24>>2];i=e>>>4&3;if((i|0)==3){H[a+76>>2]=1;Ca(d,2,11558,0);break a}c=c-2|0;j=(e>>>5&2)+2|0;h=i+j|0;e=(c>>>0)/(h>>>0)|0;if((c|0)!=(N(e,h)|0)){H[a+76>>2]=1;Ca(d,2,11139,0);break a}if(c>>>0<h>>>0){break a}b:{c=H[a+68>>2];if(c>>>0<=(e^-1)>>>0){c=c+e|0;if(c>>>0<536870912){break b}}H[a+76>>2]=1;Ca(d,2,9400,0);break a}h=Ia(H[a+72>>2],c<<3);if(!h){H[a+76>>2]=1;Ca(d,2,9443,0);break a}c=b+2|0;H[a+72>>2]=h;c:{if(i){k=e>>>0<=1?1:e;e=0;while(1){Ea(c,f+20|0,i);b=H[f+20>>2];if(b>>>0>=N(H[a+132>>2],H[a+128>>2])>>>0){break c}b=c+i|0;Ea(b,f+16|0,j);c=H[a+68>>2];g=h+(c<<3)|0;G[g>>1]=H[f+20>>2];H[g+4>>2]=H[f+16>>2];g=1;H[a+68>>2]=c+1;c=b+j|0;e=e+1|0;if((k|0)!=(e|0)){continue}break}break a}i=e>>>0<=1?1:e;b=H[a+68>>2];e=0;while(1){H[f+20>>2]=b;if(N(H[a+132>>2],H[a+128>>2])>>>0<=b>>>0){break c}Ea(c,f+16|0,j);k=H[a+68>>2];g=h+(k<<3)|0;G[g>>1]=b;H[g+4>>2]=H[f+16>>2];g=1;b=k+1|0;H[a+68>>2]=b;c=c+j|0;e=e+1|0;if((i|0)!=(e|0)){continue}break}break a}H[a+76>>2]=1;H[f>>2]=b;Ca(d,2,7799,f)}oa=f+32|0;return g|0}function Ld(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;h=oa-16|0;oa=h;a:{if(!(I[a+100|0]&2)){Ca(d,1,11356,0);a=0;break a}H[a+104>>2]=0;b:{c:{d:{if(c){while(1){if(c>>>0<=7){Ca(d,1,3403,0);break b}g=h+12|0;Ea(b,g,4);e=H[h+12>>2];Ea(b+4|0,g,4);f=8;g=H[h+12>>2];e:{f:{g:{switch(e|0){case 1:if(c>>>0<16){e=3443;break c}Ea(b+8|0,h+8|0,4);if(H[h+8>>2]){e=8449;break c}Ea(b+12|0,h+12|0,4);e=H[h+12>>2];if(e){break f}e=3268;break c;case 0:break g;default:break e}}Ca(d,1,3268,0);break b}f=16}if(e>>>0<f>>>0){Ca(d,1,9148,0);break b}if(c>>>0<e>>>0){Ca(d,1,9076,0);a=0;break a}h:{i:{j=b+f|0;k=e-f|0;j:{k:{l:{m:{if((g|0)<=1668246641){if((g|0)==1651532643){break m}if((g|0)==1667523942){break k}if((g|0)!=1668112752){break i}f=25296;break j}if((g|0)==1885564018){break l}f=25264;if((g|0)==1768449138){break j}if((g|0)!=1668246642){break i}f=25272;break j}f=25280;break j}f=25288;break j}f=25304}if(sa[H[f+4>>2]](a,j,k,d)|0){break h}a=0;break a}H[a+104>>2]=H[a+104>>2]|2147483647}i=(g|0)==1768449138?1:i;b=b+e|0;c=c-e|0;if(c){continue}break}if(i){break d}}Ca(d,1,8976,0);a=0;break a}F[a+132|0]=1;H[a+100>>2]=H[a+100>>2]|4;a=1;break a}Ca(d,1,e,0)}Ca(d,1,1968,0);a=0}oa=h+16|0;return a|0}function Pb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{if(!c){break a}b:{e=H[a+184>>2];if(!e){break b}g=H[a+96>>2];if(!g|!H[g+16>>2]|(e|0)!=H[H[g+24>>2]+40>>2]){break b}h=H[c+16>>2];if(!h){break b}f=H[c+24>>2];if(H[f+40>>2]|H[f+44>>2]){break b}g=0;if(h>>>0>=8){j=h&-8;while(1){H[(f+N(g,52)|0)+40>>2]=e;H[(f+N(g|1,52)|0)+40>>2]=e;H[(f+N(g|2,52)|0)+40>>2]=e;H[(f+N(g|3,52)|0)+40>>2]=e;H[(f+N(g|4,52)|0)+40>>2]=e;H[(f+N(g|5,52)|0)+40>>2]=e;H[(f+N(g|6,52)|0)+40>>2]=e;H[(f+N(g|7,52)|0)+40>>2]=e;g=g+8|0;k=k+8|0;if((j|0)!=(k|0)){continue}break}}h=h&7;if(h){while(1){H[(f+N(g,52)|0)+40>>2]=e;g=g+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(Ab(c,d)){break b}return 0}f=H[a+100>>2];if(!f){f=zb();H[a+100>>2]=f;if(!f){break a}}Lb(c,f);if(!Ya(H[a+216>>2],22,d)){break a}h=H[a+216>>2];e=H[h>>2];f=H[h+8>>2];c:{if(e){i=1;j=e&1;if((e|0)==1){e=0}else{k=e&-2;g=0;while(1){e=0;d:{if(!i){break d}e=0;if(!(sa[H[f>>2]](a,b,d)|0)){break d}e=(sa[H[f+4>>2]](a,b,d)|0)!=0}i=e;f=f+8|0;g=g+2|0;if((k|0)!=(g|0)){continue}break}e=!i}i=j?0:i;if(!(e|!j)){i=(sa[H[f>>2]](a,b,d)|0)!=0}Qa(h);if(i){break c}Va(H[a+96>>2]);H[a+96>>2]=0;return 0}Qa(h)}i=Ob(a,c)}return i|0}function Yd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;if(!Ya(H[b+8>>2],54,d)){return 0}j=H[b+4>>2];e=H[j>>2];h=H[j+8>>2];a:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;while(1){i=0;b:{if(!f){break b}i=0;if(!(sa[H[h>>2]](b,a,d)|0)){break b}i=(sa[H[h+4>>2]](b,a,d)|0)!=0}f=i;h=h+8|0;g=g+2|0;if((e|0)!=(g|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(sa[H[h>>2]](b,a,d)|0)!=0}Qa(j);if(f){break a}return 0}Qa(j)}j=H[b+8>>2];e=H[j>>2];h=H[j+8>>2];c:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;g=0;while(1){i=0;d:{if(!f){break d}i=0;if(!(sa[H[h>>2]](b,a,d)|0)){break d}i=(sa[H[h+4>>2]](b,a,d)|0)!=0}f=i;h=h+8|0;g=g+2|0;if((e|0)!=(g|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(sa[H[h>>2]](b,a,d)|0)!=0}Qa(j);if(f){break c}return 0}Qa(j)}if(!I[b+132|0]){Ca(d,1,11696,0);return 0}if(!I[b+133|0]){Ca(d,1,11667,0);return 0}d=Yb(a,H[b>>2],c,d);e:{if(!c){break e}a=H[c>>2];if(!a){break e}g=1;f:{g:{switch(H[b+48>>2]-12|0){case 5:g=2;break f;case 6:g=3;break f;case 12:g=4;break f;case 0:g=5;break f;case 4:break f;default:break g}}g=-1}H[a+20>>2]=g;c=H[b+108>>2];if(!c){break e}H[a+28>>2]=c;H[a+32>>2]=H[b+112>>2];H[b+108>>2]=0}return d|0}function Lb(a,b){var c=0,d=0,e=0,f=0,g=0;H[b>>2]=H[a>>2];H[b+4>>2]=H[a+4>>2];H[b+8>>2]=H[a+8>>2];H[b+12>>2]=H[a+12>>2];c=H[b+24>>2];if(c){d=H[b+16>>2];if(d){c=0;while(1){f=H[(H[b+24>>2]+N(c,52)|0)+44>>2];if(f){Da(f);d=H[b+16>>2]}c=c+1|0;if(d>>>0>c>>>0){continue}break}c=H[b+24>>2]}Da(c);H[b+24>>2]=0}c=H[a+16>>2];H[b+16>>2]=c;c=Ga(N(c,52));H[b+24>>2]=c;if(c){if(H[b+16>>2]){f=0;while(1){g=N(f,52);c=g+c|0;d=H[a+24>>2]+g|0;e=H[d+4>>2];H[c>>2]=H[d>>2];H[c+4>>2]=e;H[c+48>>2]=H[d+48>>2];e=H[d+44>>2];H[c+40>>2]=H[d+40>>2];H[c+44>>2]=e;e=H[d+36>>2];H[c+32>>2]=H[d+32>>2];H[c+36>>2]=e;e=H[d+28>>2];H[c+24>>2]=H[d+24>>2];H[c+28>>2]=e;e=H[d+20>>2];H[c+16>>2]=H[d+16>>2];H[c+20>>2]=e;e=H[d+12>>2];H[c+8>>2]=H[d+8>>2];H[c+12>>2]=e;c=H[b+24>>2];H[(g+c|0)+44>>2]=0;f=f+1|0;if(f>>>0<K[b+16>>2]){continue}break}}H[b+20>>2]=H[a+20>>2];c=H[a+32>>2];H[b+32>>2]=c;a:{if(c){c=Ga(c);H[b+28>>2]=c;if(!c){H[b+28>>2]=0;H[b+32>>2]=0;return}b=H[a+32>>2];if(!b){break a}B(c,H[a+28>>2],b);return}H[b+28>>2]=0}return}H[b+16>>2]=0;H[b+24>>2]=0}function Yb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;f=zb();H[b+96>>2]=f;a:{b:{if(!f){break b}c:{if(Ya(H[b+220>>2],18,d)){if(Ya(H[b+220>>2],19,d)){break c}}break a}i=H[b+220>>2];e=H[i>>2];g=H[i+8>>2];d:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;while(1){h=0;e:{if(!f){break e}h=0;if(!(sa[H[g>>2]](b,a,d)|0)){break e}h=(sa[H[g+4>>2]](b,a,d)|0)!=0}f=h;g=g+8|0;j=j+2|0;if((e|0)!=(j|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(sa[H[g>>2]](b,a,d)|0)!=0}Qa(i);if(f){break d}break a}Qa(i)}f:{if(Ya(H[b+216>>2],20,d)){if(Ya(H[b+216>>2],21,d)){break f}}break a}i=H[b+216>>2];e=H[i>>2];g=H[i+8>>2];g:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;j=0;while(1){h=0;h:{if(!f){break h}h=0;if(!(sa[H[g>>2]](b,a,d)|0)){break h}h=(sa[H[g+4>>2]](b,a,d)|0)!=0}f=h;g=g+8|0;j=j+2|0;if((e|0)!=(j|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(sa[H[g>>2]](b,a,d)|0)!=0}Qa(i);if(f){break g}break a}Qa(i)}a=zb();H[c>>2]=a;if(!a){break b}Lb(H[b+96>>2],a);l=1}return l|0}Va(H[b+96>>2]);H[b+96>>2]=0;return 0}function pb(a,b,c,d,e,f){var g=0,h=O(0),i=0,j=O(0);g=(c<<6)+b|0;a=c?g+-64|0:a;i=d>>>0<e>>>0?d:e;a:{if(i>>>0<=c>>>0){b=a;break a}h=L[a>>2];while(1){b=g;g=b-32|0;j=h;h=L[b>>2];L[g>>2]=O(O(j+h)*f)+L[g>>2];g=b-28|0;L[g>>2]=O(O(L[a+4>>2]+L[b+4>>2])*f)+L[g>>2];g=b-24|0;L[g>>2]=O(O(L[a+8>>2]+L[b+8>>2])*f)+L[g>>2];g=b-20|0;L[g>>2]=O(O(L[a+12>>2]+L[b+12>>2])*f)+L[g>>2];g=b-16|0;L[g>>2]=O(O(L[a+16>>2]+L[b+16>>2])*f)+L[g>>2];g=b-12|0;L[g>>2]=O(O(L[a+20>>2]+L[b+20>>2])*f)+L[g>>2];g=b-8|0;L[g>>2]=O(O(L[a+24>>2]+L[b+24>>2])*f)+L[g>>2];g=b-4|0;L[g>>2]=O(O(L[a+28>>2]+L[b+28>>2])*f)+L[g>>2];g=b- -64|0;a=b;c=c+1|0;if((i|0)!=(c|0)){continue}break}}if(d>>>0>e>>>0){a=g-32|0;f=O(f+f);L[a>>2]=O(L[b>>2]*f)+L[a>>2];a=g-28|0;L[a>>2]=O(L[b+4>>2]*f)+L[a>>2];a=g-24|0;L[a>>2]=O(L[b+8>>2]*f)+L[a>>2];a=g-20|0;L[a>>2]=O(L[b+12>>2]*f)+L[a>>2];a=g-16|0;L[a>>2]=O(L[b+16>>2]*f)+L[a>>2];a=g-12|0;L[a>>2]=O(L[b+20>>2]*f)+L[a>>2];a=g-8|0;L[a>>2]=O(L[b+24>>2]*f)+L[a>>2];a=g-4|0;L[a>>2]=O(L[b+28>>2]*f)+L[a>>2]}}function Lc(a){var b=0,c=0,d=0,e=0,f=0;d=H[6518];b=a+7&-8;c=b+7&-8;a=d+c|0;a:{b:{if(!(a>>>0<=d>>>0?c:0)){if(a>>>0<=ta()<<16>>>0){break b}if(ka(a|0)|0){break b}}H[6597]=48;d=-1;break a}H[6518]=a}if((d|0)!=-1){a=b+d|0;H[a-4>>2]=16;c=a-16|0;H[c>>2]=16;b=H[6856];if(b){f=H[b+8>>2]}else{f=0}c:{d:{if((f|0)==(d|0)){e=d-(H[d-4>>2]&-2)|0;f=H[e-4>>2];H[b+8>>2]=a;a=e-(f&-2)|0;if(F[(a+H[a>>2]|0)-4|0]&1){b=H[a+4>>2];e=H[a+8>>2];H[b+8>>2]=e;H[e+4>>2]=b;b=c-a|0;H[a>>2]=b;break c}a=d-16|0;break d}H[d>>2]=16;H[d+8>>2]=a;H[d+4>>2]=b;H[d+12>>2]=16;H[6856]=d;a=d+16|0}b=c-a|0;H[a>>2]=b}H[((b&-4)+a|0)-4>>2]=b|1;c=H[a>>2]-8|0;e:{if(c>>>0<=127){b=(c>>>3|0)-1|0;break e}e=Q(c);b=((c>>>29-e^4)-(e<<2)|0)+110|0;if(c>>>0<=4095){break e}b=((c>>>30-e^2)-(e<<1)|0)+71|0;b=b>>>0>=63?63:b}c=b<<4;H[a+4>>2]=c+26400;c=c+26408|0;H[a+8>>2]=H[c>>2];H[c>>2]=a;H[H[a+8>>2]+4>>2]=a;c=H[6858];e=H[6859];a=b&31;if((b&63)>>>0>=32){b=1<<a;f=0}else{f=1<<a;b=f-1&1>>>32-a}H[6858]=f|c;H[6859]=b|e}return(d|0)!=-1}function Hd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;f=oa-16|0;oa=f;a:{if(H[a+120>>2]|c>>>0<3){break a}Ea(b,f+12|0,2);k=J[f+12>>1];if(k-1025>>>0<=4294966271){H[f>>2]=k;Ca(d,1,3526,f);break a}Ea(b+2|0,f+12|0,1);i=J[f+12>>1];if(!i){Ca(d,1,3174,0);break a}if(i+3>>>0>c>>>0){break a}h=Ga(N(i,k)<<2);if(!h){break a}j=Ga(i);if(!j){Da(h);break a}l=Ga(i);if(!l){Da(h);Da(j);break a}g=Ga(20);if(!g){Da(h);Da(j);Da(l);break a}d=b+3|0;H[g+8>>2]=j;H[g+4>>2]=l;G[g+16>>1]=k;H[g>>2]=h;m=H[f+12>>2];H[g+12>>2]=0;F[g+18|0]=m;H[a+120>>2]=g;while(1){Ea(d,f+12|0,1);F[e+j|0]=(I[f+12|0]&127)+1;F[e+l|0]=(H[f+12>>2]&128)>>>7;d=d+1|0;e=e+1|0;if((i|0)!=(e|0)){continue}break}g=0;while(1){e=0;a=0;while(1){e=I[e+j|0]+7>>>3|0;e=e>>>0>=4?4:e;if((e+(d-b|0)|0)>(c|0)){e=0;break a}Ea(d,f+12|0,e);H[h>>2]=H[f+12>>2];h=h+4|0;d=d+e|0;a=a+1|0;e=a&65535;if(i>>>0>e>>>0){continue}break}e=1;g=g+1|0;if((g&65535)>>>0<k>>>0){continue}break}}oa=f+16|0;return e|0}function zd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;f=-1;e=-1;if(!(I[a+68|0]&8)){f=H[a+32>>2];H[a+36>>2]=f;a:{b:{c:{e=H[a+48>>2];if(e){while(1){e=sa[H[a+20>>2]](f,e,H[a>>2])|0;if((e|0)==-1){break c}f=e+H[a+36>>2]|0;H[a+36>>2]=f;e=H[a+48>>2]-e|0;H[a+48>>2]=e;if(e){continue}break}f=H[a+32>>2]}H[a+36>>2]=f;if(!!b&(c|0)>=0|(c|0)>0){break b}f=0;e=0;break a}H[a+68>>2]=H[a+68>>2]|8;Ca(d,4,15604,0);H[a+48>>2]=0;H[a+68>>2]=H[a+68>>2]|8;ra=-1;return-1}f=0;e=0;while(1){g=sa[H[a+24>>2]](b,c,H[a>>2])|0;h=ra;i=h;if((g&h)==-1){Ca(d,4,15589,0);H[a+68>>2]=H[a+68>>2]|8;b=e+H[a+60>>2]|0;c=f+H[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;H[a+56>>2]=c;H[a+60>>2]=b;a=!(e|f);b=a?-1:f;ra=a?-1:e;return b|0}e=e+i|0;f=f+g|0;e=f>>>0<g>>>0?e+1|0:e;h=b;b=b-g|0;c=c-(i+(g>>>0>h>>>0)|0)|0;if(!!b&(c|0)>=0|(c|0)>0){continue}break}}b=e+H[a+60>>2]|0;c=f+H[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;H[a+56>>2]=c;H[a+60>>2]=b}ra=e;return f|0}function Kc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;b=a;a:{if(b&3){while(1){c=I[b|0];if(!c|(c|0)==61){break a}b=b+1|0;if(b&3){continue}break}}b:{c:{d=H[b>>2];if(((d|16843008-d)&-2139062144)!=-2139062144){break c}while(1){c=d^1027423549;if(((16843008-c|c)&-2139062144)!=-2139062144){break c}d=H[b+4>>2];c=b+4|0;b=c;if(((16843008-d|d)&-2139062144)==-2139062144){continue}break}break b}c=b}while(1){b=c;d=I[b|0];if(!d){break a}c=b+1|0;if((d|0)!=61){continue}break}}if((a|0)==(b|0)){return 0}g=b-a|0;d:{if(I[g+a|0]){break d}f=H[6860];if(!f){break d}b=H[f>>2];if(!b){break d}while(1){e:{d=a;c=b;h=g;e=0;f:{if(!g){break f}e=I[d|0];if(e){g:{while(1){i=I[c|0];if((i|0)!=(e|0)|!i){break g}h=h-1|0;if(!h){break g}c=c+1|0;e=I[d+1|0];d=d+1|0;if(e){continue}break}e=0}}else{e=0}e=e-I[c|0]|0}if(!e){b=b+g|0;if(I[b|0]==61){break e}}b=H[f+4>>2];f=f+4|0;if(b){continue}break d}break}j=b+1|0}return j}function qe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;g=oa-16|0;oa=g;a:{if(c>>>0<=1){Ca(d,1,3983,0);a=0;break a}if(F[a+212|0]&1){Ca(d,1,12668,0);a=0;break a}a=H[a+180>>2]+N(H[a+228>>2],5644)|0;F[a+5640|0]=I[a+5640|0]|2;Ea(b,g+12|0,1);e=H[a+5164>>2];b:{if(!e){f=H[g+12>>2]+1|0;e=Fa(f,8);H[a+5164>>2]=e;if(!e){Ca(d,1,4009,0);a=0;break a}H[a+5160>>2]=f;break b}f=H[g+12>>2];if(f>>>0<K[a+5160>>2]){break b}h=e;e=f+1|0;f=Ia(h,e<<3);if(!f){Ca(d,1,4009,0);a=0;break a}H[a+5164>>2]=f;h=H[a+5160>>2];i=e-h<<3;if(i){y(f+(h<<3)|0,0,i)}H[a+5160>>2]=e;e=H[a+5164>>2]}h=e;e=H[g+12>>2];if(H[h+(e<<3)>>2]){H[g>>2]=e;Ca(d,1,7063,g);a=0;break a}c=c-1|0;e=Ga(c);a=H[a+5164>>2];f=H[g+12>>2];H[a+(f<<3)>>2]=e;if(!e){Ca(d,1,4009,0);a=0;break a}H[(a+(f<<3)|0)+4>>2]=c;if(c){B(H[a+(H[g+12>>2]<<3)>>2],b+1|0,c)}a=1}oa=g+16|0;return a|0}function Ib(a,b,c){var d=0,e=0,f=0,g=0;e=a+4|0;d=(e+b|0)-1&0-b;b=H[a>>2];if(d+c>>>0<=(b+a|0)-4>>>0){f=H[a+4>>2];g=H[a+8>>2];H[f+8>>2]=g;H[g+4>>2]=f;if((d|0)!=(e|0)){d=d-e|0;f=a-(H[a-4>>2]&-2)|0;e=d+H[f>>2]|0;H[f>>2]=e;H[(f+(e&-4)|0)-4>>2]=e;a=a+d|0;b=b-d|0;H[a>>2]=b}a:{if(c+24>>>0<=b>>>0){e=a+c|0;b=(b-c|0)-8|0;H[e+8>>2]=b;g=e+8|0;H[(g+(b&-4)|0)-4>>2]=b|1;d=H[e+8>>2]-8|0;b:{if(d>>>0<=127){b=(d>>>3|0)-1|0;break b}f=Q(d);b=((d>>>29-f^4)-(f<<2)|0)+110|0;if(d>>>0<=4095){break b}b=((d>>>30-f^2)-(f<<1)|0)+71|0;b=b>>>0>=63?63:b}d=b<<4;H[e+12>>2]=d+26400;d=d+26408|0;H[e+16>>2]=H[d>>2];H[d>>2]=g;H[H[e+16>>2]+4>>2]=g;d=H[6858];f=H[6859];e=b&31;if((b&63)>>>0>=32){b=1<<e;g=0}else{g=1<<e;b=g-1&1>>>32-e}H[6858]=g|d;H[6859]=b|f;b=c+8|0;H[a>>2]=b;c=(b&-4)+a|0;break a}c=a+b|0}H[c-4>>2]=b;a=a+4|0}else{a=0}return a}function we(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;g=oa-16|0;oa=g;i=H[H[a+96>>2]+16>>2];h=i>>>0<257?1:2;e=(h<<1)+5|0;f=(c>>>0)/(e>>>0)|0;a:{if(!((N(e,f)|0)==(c|0)&c>>>0>=e>>>0)){Ca(d,1,4643,0);a=0;break a}if(H[a+8>>2]==16){e=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{e=H[a+12>>2]}a=0;c=I[e+5640|0];a=c&4?H[e+420>>2]+1|0:a;f=f+a|0;if(f>>>0>=32){H[g>>2]=f;Ca(d,1,7781,g);a=0;break a}F[e+5640|0]=c|4;if(a>>>0<f>>>0){c=(e+N(a,148)|0)+424|0;while(1){Ea(b,c,1);b=b+1|0;Ea(b,c+4|0,h);b=b+h|0;Ea(b,c+8|0,2);d=H[c+8>>2];j=H[e+8>>2];H[c+8>>2]=d>>>0<j>>>0?d:j;Ea(b+2|0,c+12|0,1);b=b+3|0;Ea(b,c+16|0,h);b=b+h|0;Ea(b,g+12|0,1);H[c+36>>2]=H[g+12>>2];d=H[c+16>>2];H[c+16>>2]=d>>>0<i>>>0?d:i;c=c+148|0;b=b+1|0;a=a+1|0;if((f|0)!=(a|0)){continue}break}}H[e+420>>2]=f-1;a=1}oa=g+16|0;return a|0}function mb(a){var b=0,c=0,d=0,e=0;a:{if(!a){break a}b=H[a+5164>>2];if(b){c=H[a+5160>>2];if(c){b=0;while(1){d=H[H[a+5164>>2]+(b<<3)>>2];if(d){Da(d);c=H[a+5160>>2]}b=b+1|0;if(c>>>0>b>>>0){continue}break}b=H[a+5164>>2]}H[a+5160>>2]=0;Da(b);H[a+5164>>2]=0}b=H[a+5172>>2];if(b){Da(b);H[a+5172>>2]=0}b=H[a+5584>>2];if(b){Da(b);H[a+5584>>2]=0}b=H[a+5612>>2];if(b){Da(b);H[a+5612>>2]=0}b=H[a+5608>>2];if(b){Da(b);H[a+5608>>2]=0}b=H[a+5628>>2];if(b){Da(b);H[a+5636>>2]=0;H[a+5628>>2]=0;H[a+5632>>2]=0}b=H[a+5616>>2];if(b){e=H[a+5620>>2];if(e){c=0;while(1){d=H[b+12>>2];if(d){Da(d);H[b+12>>2]=0;e=H[a+5620>>2]}b=b+20|0;c=c+1|0;if(e>>>0>c>>>0){continue}break}b=H[a+5616>>2]}Da(b);H[a+5616>>2]=0}b=H[a+5604>>2];if(b){Da(b);H[a+5604>>2]=0}b=H[a+5596>>2];if(!b){break a}Da(b);H[a+5596>>2]=0;H[a+5600>>2]=0}}function Kd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=oa-32|0;oa=e;a:{if(H[a+72>>2]){Ca(d,2,7015,0);c=1;break a}if((c|0)!=14){c=0;Ca(d,1,14445,0);break a}Ea(b,a+16|0,4);Ea(b+4|0,a+12|0,4);Ea(b+8|0,a+20|0,2);f=H[a+12>>2];b:{g=H[a+16>>2];c=H[a+20>>2];c:{if(!g){break c}c=H[a+20>>2];if(!f){break c}if(c){break b}c=0}H[e+8>>2]=c;H[e+4>>2]=g;H[e>>2]=f;Ca(d,1,14289,e);c=0;break a}if(c-16385>>>0<=4294950911){c=0;Ca(d,1,14203,0);break a}c=Fa(c,12);H[a+72>>2]=c;if(!c){c=0;Ca(d,1,14240,0);break a}c=1;Ea(b+10|0,a+24|0,1);Ea(b+11|0,a+28|0,1);f=H[a+28>>2];if((f|0)!=7){H[e+16>>2]=f;Ca(d,4,16272,e+16|0)}Ea(b+12|0,a+32|0,1);Ea(b+13|0,a+36|0,1);b=H[a>>2];F[b+212|0]=I[b+212|0]&251|(H[a+24>>2]==255?4:0);b=H[a>>2];H[b+240>>2]=H[a+12>>2];H[b+244>>2]=H[a+16>>2];F[a+133|0]=1}oa=e+32|0;return c|0}function Dc(a,b,c,d){a:{switch(b-9|0){case 0:b=H[c>>2];H[c>>2]=b+4;H[a>>2]=H[b>>2];return;case 6:b=H[c>>2];H[c>>2]=b+4;b=G[b>>1];H[a>>2]=b;H[a+4>>2]=b>>31;return;case 7:b=H[c>>2];H[c>>2]=b+4;H[a>>2]=J[b>>1];H[a+4>>2]=0;return;case 8:b=H[c>>2];H[c>>2]=b+4;b=F[b|0];H[a>>2]=b;H[a+4>>2]=b>>31;return;case 9:b=H[c>>2];H[c>>2]=b+4;H[a>>2]=I[b|0];H[a+4>>2]=0;return;case 16:b=H[c>>2]+7&-8;H[c>>2]=b+8;M[a>>3]=M[b>>3];return;case 17:sa[d|0](a,c);default:return;case 1:case 4:case 14:b=H[c>>2];H[c>>2]=b+4;b=H[b>>2];H[a>>2]=b;H[a+4>>2]=b>>31;return;case 2:case 5:case 11:case 15:b=H[c>>2];H[c>>2]=b+4;H[a>>2]=H[b>>2];H[a+4>>2]=0;return;case 3:case 10:case 12:case 13:break a}}b=H[c>>2]+7&-8;H[c>>2]=b+8;c=H[b+4>>2];H[a>>2]=H[b>>2];H[a+4>>2]=c}function re(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;g=oa-16|0;oa=g;a:{if(c>>>0<=1){Ca(d,1,4311,0);a=0;break a}F[a+212|0]=I[a+212|0]|1;Ea(b,g+12|0,1);e=H[a+140>>2];b:{if(!e){f=H[g+12>>2]+1|0;e=Fa(f,8);H[a+140>>2]=e;if(!e){Ca(d,1,4337,0);a=0;break a}H[a+136>>2]=f;break b}f=H[g+12>>2];if(f>>>0<K[a+136>>2]){break b}h=e;e=f+1|0;f=Ia(h,e<<3);if(!f){Ca(d,1,4337,0);a=0;break a}H[a+140>>2]=f;h=H[a+136>>2];i=e-h<<3;if(i){y(f+(h<<3)|0,0,i)}H[a+136>>2]=e;e=H[a+140>>2]}h=e;e=H[g+12>>2];if(H[h+(e<<3)>>2]){H[g>>2]=e;Ca(d,1,7085,g);a=0;break a}c=c-1|0;e=Ga(c);a=H[a+140>>2];f=H[g+12>>2];H[a+(f<<3)>>2]=e;if(!e){Ca(d,1,4337,0);a=0;break a}H[(a+(f<<3)|0)+4>>2]=c;if(c){B(H[a+(H[g+12>>2]<<3)>>2],b+1|0,c)}a=1}oa=g+16|0;return a|0}function ud(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;d=oa-32|0;oa=d;e=H[a+28>>2];H[d+16>>2]=e;f=H[a+20>>2];H[d+28>>2]=c;H[d+24>>2]=b;b=f-e|0;H[d+20>>2]=b;f=b+c|0;i=2;b=d+16|0;a:{while(1){b:{c:{d:{if(!Hb(_(H[a+60>>2],b|0,i|0,d+12|0)|0)){g=H[d+12>>2];if((g|0)==(f|0)){break d}if((g|0)>=0){break c}break b}if((f|0)!=-1){break b}}b=H[a+44>>2];H[a+28>>2]=b;H[a+20>>2]=b;H[a+16>>2]=b+H[a+48>>2];a=c;break a}h=H[b+4>>2];j=h>>>0<g>>>0;e=(j?8:0)+b|0;h=g-(j?h:0)|0;H[e>>2]=h+H[e>>2];b=(j?12:4)+b|0;H[b>>2]=H[b>>2]-h;f=f-g|0;i=i-j|0;b=e;continue}break}H[a+28>>2]=0;H[a+16>>2]=0;H[a+20>>2]=0;H[a>>2]=H[a>>2]|32;a=0;if((i|0)==2){break a}a=c-H[b+4>>2]|0}oa=d+32|0;return a|0}function Da(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;if(a){b=a-4|0;f=H[b>>2];c=f;d=b;e=H[a-8>>2];a=e&-2;if((a|0)!=(e|0)){d=b-a|0;c=H[d+4>>2];e=H[d+8>>2];H[c+8>>2]=e;H[e+4>>2]=c;c=a+f|0}a=b+f|0;b=H[a>>2];if((b|0)!=H[(a+b|0)-4>>2]){f=H[a+4>>2];a=H[a+8>>2];H[f+8>>2]=a;H[a+4>>2]=f;c=b+c|0}H[d>>2]=c;H[((c&-4)+d|0)-4>>2]=c|1;b=H[d>>2]-8|0;a:{if(b>>>0<=127){a=(b>>>3|0)-1|0;break a}c=Q(b);a=((b>>>29-c^4)-(c<<2)|0)+110|0;if(b>>>0<=4095){break a}a=((b>>>30-c^2)-(c<<1)|0)+71|0;a=a>>>0>=63?63:a}b=a<<4;H[d+4>>2]=b+26400;b=b+26408|0;H[d+8>>2]=H[b>>2];H[b>>2]=d;H[H[d+8>>2]+4>>2]=d;b=H[6858];c=H[6859];d=a&31;if((a&63)>>>0>=32){a=1<<d;e=0}else{e=1<<d;a=e-1&1>>>32-d}H[6858]=e|b;H[6859]=a|c}}function hd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;if(K[a+44>>2]>=8){e=H[a+36>>2];j=e<<5;k=N(e,28);l=N(e,24);m=N(e,20);n=e<<4;o=N(e,12);p=e<<3;f=H[a+40>>2];g=8;while(1){Eb(a,f,H[a+36>>2],8);Wa(a);h=H[a+32>>2];if(h){i=H[a>>2];b=0;while(1){c=(b<<2)+f|0;d=i+(b<<5)|0;L[c>>2]=L[d>>2];L[c+(e<<2)>>2]=L[d+4>>2];L[c+p>>2]=L[d+8>>2];L[c+o>>2]=L[d+12>>2];b=b+1|0;if((h|0)!=(b|0)){continue}break}i=H[a>>2];b=0;while(1){c=(b<<2)+f|0;d=i+(b<<5)|0;L[c+n>>2]=L[d+16>>2];L[c+m>>2]=L[d+20>>2];L[c+l>>2]=L[d+24>>2];L[c+k>>2]=L[d+28>>2];b=b+1|0;if((h|0)!=(b|0)){continue}break}}f=f+j|0;g=g+8|0;if(g>>>0<=K[a+44>>2]){continue}break}}Da(H[a>>2]);Da(a)}function Ed(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;e=oa-16|0;oa=e;a:{if(H[a+116>>2]){break a}if(c>>>0<=1){Ca(d,1,8882,0);break a}Ea(b,e+12|0,2);f=H[e+12>>2];h=f&65535;if(!h){Ca(d,1,8915,0);break a}if(N(h,6)+2>>>0>c>>>0){Ca(d,1,8882,0);break a}d=Ga(N(f,6));if(!d){break a}c=Ga(8);H[a+116>>2]=c;if(!c){Da(d);break a}H[c>>2]=d;f=c;c=J[e+12>>1];G[f+4>>1]=c;if(!c){g=1;break a}c=0;while(1){g=e+12|0;Ea(b+2|0,g,2);f=d+N(c,6)|0;G[f>>1]=H[e+12>>2];Ea(b+4|0,g,2);G[f+2>>1]=H[e+12>>2];b=b+6|0;Ea(b,g,2);G[f+4>>1]=H[e+12>>2];g=1;c=c+1|0;if(c>>>0<J[H[a+116>>2]+4>>1]){continue}break}}oa=e+16|0;return g|0}function Xb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;g=oa-32|0;oa=g;f=H[a+96>>2];a:{if(!f){Ca(d,1,13752,0);e=0;break a}f=Fa(4,H[f+16>>2]);e=0;if(!f){break a}if(b){j=H[a+96>>2];while(1){b:{e=H[(h<<2)+c>>2];c:{if(e>>>0>=K[j+16>>2]){H[g+16>>2]=e;Ca(d,1,2443,g+16|0);break c}i=f+(e<<2)|0;if(!H[i>>2]){break b}H[g>>2]=e;Ca(d,1,3487,g)}Da(f);e=0;break a}H[i>>2]=1;h=h+1|0;if((h|0)!=(b|0)){continue}break}}Da(f);Da(H[a+64>>2]);d:{if(b){d=b<<2;e=Ga(d);H[a+64>>2]=e;if(!e){H[a+60>>2]=0;e=0;break a}if(!d){break d}B(e,c,d);break d}H[a+64>>2]=0}H[a+60>>2]=b;e=1}oa=g+32|0;return e|0}function Pc(a){a=a|0;var b=0,c=0;if(a){Bb(H[a>>2]);H[a>>2]=0;b=H[a+72>>2];if(b){Da(b);H[a+72>>2]=0}b=H[a+68>>2];if(b){Da(b);H[a+68>>2]=0}b=H[a+108>>2];if(b){Da(b);H[a+108>>2]=0}b=H[a+116>>2];if(b){c=H[b>>2];if(c){Da(c);b=H[a+116>>2];H[b>>2]=0}Da(b);H[a+116>>2]=0}b=H[a+120>>2];if(b){c=H[b+12>>2];if(c){Da(c);b=H[a+120>>2];H[b+12>>2]=0}c=H[b+4>>2];if(c){Da(c);b=H[a+120>>2];H[b+4>>2]=0}c=H[b+8>>2];if(c){Da(c);b=H[a+120>>2];H[b+8>>2]=0}c=H[b>>2];if(c){Da(c);b=H[a+120>>2];H[b>>2]=0}Da(b);H[a+120>>2]=0}b=H[a+4>>2];if(b){sb(b);H[a+4>>2]=0}b=H[a+8>>2];if(b){sb(b);H[a+8>>2]=0}Da(a)}}function Ub(){var a=0,b=0,c=0;a:{a=Fa(1,256);if(a){H[a>>2]=1;H[a+208>>2]=1;F[a+212|0]=I[a+212|0]|6;b=Fa(1,5644);H[a+12>>2]=b;if(!b){break a}b=Fa(1,1e3);H[a+16>>2]=b;if(!b){break a}H[a+48>>2]=0;H[a+52>>2]=0;H[a+44>>2]=-1;H[a+20>>2]=1e3;b:{c=Fa(1,48);if(c){H[c+24>>2]=0;H[c+32>>2]=100;b=Fa(100,24);H[c+28>>2]=b;if(b){break b}Da(c)}H[a+224>>2]=0;break a}H[c+40>>2]=0;H[a+224>>2]=c;b=tb();H[a+220>>2]=b;if(!b){break a}b=tb();H[a+216>>2]=b;if(!b){break a}c:{if(!Kc(1419)){break c}}b=vc();H[a+236>>2]=b;if(!b){b=vc();H[a+236>>2]=b;if(!b){break a}}}else{a=0}return a}Bb(a);return 0}function wb(a,b,c,d,e,f){var g=0,h=0,i=0,j=0,k=0,l=0;g=oa-240|0;oa=g;H[g+236>>2]=c;H[g+232>>2]=b;H[g>>2]=a;l=!e;a:{b:{c:{d:{if((b|0)!=1){h=a;i=1;break d}h=a;i=1;if(c){break d}e=a;break c}while(1){j=(d<<2)+f|0;e=h-H[j>>2]|0;if((db(e,a)|0)<=0){e=h;break c}k=l^-1;l=1;e:{if(!((k|(d|0)<2)&1)){j=H[j-8>>2];k=h-8|0;if((db(k,e)|0)>=0){break e}if((db(k-j|0,e)|0)>=0){break e}}H[(i<<2)+g>>2]=e;b=Jc(b,c);xb(g+232|0,b);i=i+1|0;d=b+d|0;h=e;c=H[g+236>>2];b=H[g+232>>2];if(c|(b|0)!=1){continue}break b}break}e=h;break b}if(!l){break a}}Ic(g,i);Gb(e,d,f)}oa=g+240|0}function Gc(a,b,c,d,e){var f=0,g=0,h=0;f=oa-208|0;oa=f;H[f+204>>2]=c;c=f+160|0;y(c,0,40);H[f+200>>2]=H[f+204>>2];a:{if((Fc(0,b,f+200|0,f+80|0,c,d,e)|0)<0){break a}c=H[a+76>>2]<0;g=H[a>>2];H[a>>2]=g&-33;b:{c:{d:{if(!H[a+48>>2]){H[a+48>>2]=80;H[a+28>>2]=0;H[a+16>>2]=0;H[a+20>>2]=0;h=H[a+44>>2];H[a+44>>2]=f;break d}if(H[a+16>>2]){break c}}if(Kb(a)){break b}}Fc(a,b,f+200|0,f+80|0,f+160|0,d,e)}if(h){sa[H[a+36>>2]](a,0,0)|0;H[a+48>>2]=0;H[a+44>>2]=h;H[a+28>>2]=0;H[a+16>>2]=0;H[a+20>>2]=0}H[a>>2]=H[a>>2]|g&32;if(c){break a}}oa=f+208|0}function Be(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=oa-16|0;oa=e;if(H[a+8>>2]==16){g=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{g=H[a+12>>2]}h=H[a+96>>2];f=K[h+16>>2]<257?1:2;a:{if(f>>>0>=c>>>0){c=0;Ca(d,1,4669,0);break a}H[e+12>>2]=(f^-1)+c;Ea(b,e+8|0,f);i=H[e+8>>2];if(i>>>0>=K[h+16>>2]){c=0;Ca(d,1,14067,0);break a}c=1;b=b+f|0;Ea(b,H[g+5584>>2]+N(i,1080)|0,1);if(!Wc(a,H[e+8>>2],b+1|0,e+12|0,d)){c=0;Ca(d,1,4669,0);break a}if(!H[e+12>>2]){break a}c=0;Ca(d,1,4669,0)}oa=e+16|0;return c|0}function Rc(a,b){var c=0,d=0,e=0,f=0,g=0;f=oa-32|0;oa=f;c=H[a+60>>2];a:{b:{if(c){g=1;while(1){e=H[H[a+64>>2]+(d<<2)>>2];if(!H[(H[H[a+100>>2]+24>>2]+N(e,52)|0)+44>>2]){H[f+16>>2]=e;Ca(b,2,7604,f+16|0);g=0;c=H[a+60>>2]}d=d+1|0;if(c>>>0>d>>>0){continue}break}break b}g=1;c=H[a+100>>2];e=1;if(!H[c+16>>2]){break a}while(1){if(!H[(H[c+24>>2]+N(d,52)|0)+44>>2]){H[f>>2]=d;Ca(b,2,7604,f);g=0;c=H[a+100>>2]}d=d+1|0;if(d>>>0<K[c+16>>2]){continue}break}}e=1;if(g){break a}Ca(b,1,2897,0);e=0}oa=f+32|0;return e}function Gd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;f=oa-16|0;oa=f;e=H[a+120>>2];a:{if(!e){Ca(d,1,8836,0);c=0;break a}if(H[e+12>>2]){Ca(d,1,11598,0);c=0;break a}e=I[e+18|0];g=e<<2;if(g>>>0>c>>>0){Ca(d,1,8803,0);c=0;break a}g=Ga(g);c=0;if(!g){break a}if(e){d=0;while(1){c=f+12|0;Ea(b,c,2);h=g+(d<<2)|0;G[h>>1]=H[f+12>>2];Ea(b+2|0,c,1);F[h+2|0]=H[f+12>>2];Ea(b+3|0,c,1);F[h+3|0]=H[f+12>>2];b=b+4|0;d=d+1|0;if((e|0)!=(d|0)){continue}break}}H[H[a+120>>2]+12>>2]=g;c=1}oa=f+16|0;return c|0}function me(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=oa-16|0;oa=e;g=H[H[a+96>>2]+16>>2];a:{if((g+2|0)!=(c|0)){Ca(d,1,4617,0);break a}Ea(b,e+12|0,2);if(H[e+12>>2]!=(g|0)){Ca(d,1,4617,0);break a}if(!g){f=1;break a}c=b+2|0;a=H[H[a+96>>2]+24>>2];b=0;while(1){Ea(c,e+8|0,1);f=H[e+8>>2];h=f&127;i=h+1|0;H[a+24>>2]=i;H[a+32>>2]=f>>>7&1;if(h>>>0>=31){H[e+4>>2]=i;H[e>>2]=b;Ca(d,1,15402,e);f=0;break a}a=a+52|0;f=1;c=c+1|0;b=b+1|0;if((g|0)!=(b|0)){continue}break}}oa=e+16|0;return f|0}function ye(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;e=oa-16|0;oa=e;a:{b:{h=e+8|0;c:{if(K[H[a+96>>2]+16>>2]<=256){if(c){f=-1;g=1;break c}Ca(d,1,4695,0);a=0;break a}if(c>>>0<=1){break b}f=-2;g=2}Ea(b,h,g);H[e+12>>2]=c+f;c=H[e+8>>2];f=H[H[a+96>>2]+16>>2];if(c>>>0>=f>>>0){H[e+4>>2]=f;H[e>>2]=c;Ca(d,1,7712,e);a=0;break a}if(!Vc(a,c,b+g|0,e+12|0,d)){Ca(d,1,4695,0);a=0;break a}a=1;if(!H[e+12>>2]){break a}Ca(d,1,4695,0);a=0;break a}Ca(d,1,4695,0);a=0}oa=e+16|0;return a|0}function pc(a,b,c,d){var e=0,f=0,g=0;g=oa-128|0;oa=g;f=g;c=H[b+12>>2]+(c<<4)|0;e=H[c>>2];a:{if(!e){b=c;break a}while(1){H[f>>2]=c;f=f+4|0;b=e;c=b;e=H[c>>2];if(e){continue}break}}e=0;while(1){c=H[b+8>>2];if((e|0)>(c|0)){H[b+8>>2]=e;c=e}b:{if((c|0)>=(d|0)){break b}while(1){if(H[b+4>>2]<=(c|0)){break b}c:{if(Ta(a,1)){H[b+4>>2]=c;break c}c=c+1|0}if((c|0)<(d|0)){continue}break}}H[b+8>>2]=c;if((f|0)!=(g|0)){f=f-4|0;b=H[f>>2];e=c;continue}break}oa=g+128|0;return H[b+4>>2]<(d|0)}function Qd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;f=H[a+32>>2];H[a+36>>2]=f;a:{e=H[a+48>>2];if(e){while(1){e=sa[H[a+20>>2]](f,e,H[a>>2])|0;if((e|0)==-1){break a}f=e+H[a+36>>2]|0;H[a+36>>2]=f;e=H[a+48>>2]-e|0;H[a+48>>2]=e;if(e){continue}break}f=H[a+32>>2]}H[a+48>>2]=0;H[a+36>>2]=f;if(!(sa[H[a+28>>2]](b,c,H[a>>2])|0)){H[a+68>>2]=H[a+68>>2]|8;return 0}H[a+56>>2]=b;H[a+60>>2]=c;return 1}H[a+68>>2]=H[a+68>>2]|8;Ca(d,4,15604,0);H[a+68>>2]=H[a+68>>2]|8;return 0}function Ca(a,b,c,d){var e=0,f=0;e=oa-528|0;oa=e;a:{if(!a){break a}b:{c:{switch(b-1|0){case 0:b=a+12|0;break b;case 1:b=a+16|0;a=a+4|0;break b;case 3:break c;default:break a}}b=a+20|0;a=a+8|0}b=H[b>>2];if(!b|!c){break a}f=H[a>>2];y(e,0,512);H[e+524>>2]=d;a=oa-160|0;oa=a;H[a+148>>2]=e;H[a+152>>2]=511;y(a,0,144);H[a+76>>2]=-1;H[a+36>>2]=103;H[a+80>>2]=-1;H[a+44>>2]=a+159;H[a+84>>2]=a+148;F[e|0]=0;Gc(a,c,d,104,105);oa=a+160|0;F[e+511|0]=0;sa[b|0](e,f)}oa=e+528|0}function Md(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;if(H[a+100>>2]!=1){Ca(d,1,11401,0);return 0}a:{if(c>>>0<=7){break a}Ea(b,a+56|0,4);Ea(b+4|0,a+60|0,4);if(c&3){break a}c=c-8|0;e=c>>>2|0;H[a+64>>2]=e;b:{if(!c){break b}c=Fa(e,4);H[a+68>>2]=c;if(!c){Ca(d,1,2235,0);return 0}if(!H[a+64>>2]){break b}d=b+8|0;c=0;while(1){Ea(d,H[a+68>>2]+(c<<2)|0,4);d=d+4|0;c=c+1|0;if(c>>>0<K[a+64>>2]){continue}break}}H[a+100>>2]=H[a+100>>2]|2;return 1}Ca(d,1,5955,0);return 0}function rc(a){var b=0,c=0,d=0;a:{if(!a){break a}b=H[a+8>>2];if(!b){break a}a=H[a+12>>2];if(b>>>0>=4){d=b&-4;while(1){H[a+60>>2]=0;H[a+52>>2]=999;H[a+56>>2]=0;H[a+44>>2]=0;H[a+36>>2]=999;H[a+40>>2]=0;H[a+28>>2]=0;H[a+20>>2]=999;H[a+24>>2]=0;H[a+12>>2]=0;H[a+4>>2]=999;H[a+8>>2]=0;a=a- -64|0;c=c+4|0;if((d|0)!=(c|0)){continue}break}}b=b&3;if(!b){break a}c=0;while(1){H[a+12>>2]=0;H[a+4>>2]=999;H[a+8>>2]=0;a=a+16|0;c=c+1|0;if((b|0)!=(c|0)){continue}break}}}
function ze(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=oa-16|0;oa=e;H[e+12>>2]=c;a:{if(!(!Vc(a,0,b,e+12|0,d)|H[e+12>>2])){if(H[a+8>>2]==16){b=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{b=H[a+12>>2]}f=1;if(K[H[a+96>>2]+16>>2]<2){break a}c=H[b+5584>>2];g=c+28|0;b=1;d=c;while(1){H[d+1104>>2]=H[c+24>>2];H[d+1884>>2]=H[c+804>>2];B(d+1108|0,g,776);d=d+1080|0;b=b+1|0;if(b>>>0<K[H[a+96>>2]+16>>2]){continue}break}break a}Ca(d,1,4591,0)}oa=e+16|0;return f|0}function Cc(a,b){a:{b:{if(b>>>0<=127){break b}c:{if(!H[H[6885]>>2]){if((b&-128)==57216){break b}break c}if(b>>>0<=2047){F[a+1|0]=b&63|128;F[a|0]=b>>>6|192;a=2;break a}if(!((b&-8192)!=57344&b>>>0>=55296)){F[a+2|0]=b&63|128;F[a|0]=b>>>12|224;F[a+1|0]=b>>>6&63|128;a=3;break a}if(b-65536>>>0<=1048575){F[a+3|0]=b&63|128;F[a|0]=b>>>18|240;F[a+2|0]=b>>>6&63|128;F[a+1|0]=b>>>12&63|128;a=4;break a}}H[6597]=25;a=-1;break a}F[a|0]=b;a=1}return a}function _d(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!Ya(H[a+8>>2],54,c)){return 0}h=H[a+8>>2];d=H[h>>2];f=H[h+8>>2];a:{if(d){e=1;i=d&1;if((d|0)==1){d=0}else{d=d&-2;while(1){g=0;b:{if(!e){break b}g=0;if(!(sa[H[f>>2]](a,b,c)|0)){break b}g=(sa[H[f+4>>2]](a,b,c)|0)!=0}e=g;f=f+8|0;j=j+2|0;if((d|0)!=(j|0)){continue}break}d=!e}e=i?0:e;if(!(d|!i)){e=(sa[H[f>>2]](a,b,c)|0)!=0}Qa(h);if(e){break a}return 0}Qa(h)}return 1}function Ae(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=oa-16|0;oa=e;g=H[H[a+96>>2]+16>>2];f=g>>>0<257?1:2;a:{if((f+2|0)!=(c|0)){a=0;Ca(d,1,4285,0);break a}if(H[a+8>>2]==16){c=H[a+180>>2]+N(H[a+228>>2],5644)|0}else{c=H[a+12>>2]}Ea(b,e+12|0,f);a=1;b=b+f|0;Ea(b,e+8|0,1);f=H[e+12>>2];if(f>>>0>=g>>>0){H[e+4>>2]=g;H[e>>2]=f;Ca(d,1,14923,e);a=0;break a}Ea(b+1|0,(H[c+5584>>2]+N(f,1080)|0)+808|0,1)}oa=e+16|0;return a|0}function Fe(){var a=0,b=0,c=0;while(1){b=a<<4;c=b+26400|0;H[b+26404>>2]=c;H[b+26408>>2]=c;a=a+1|0;if((a|0)!=64){continue}break}Lc(48);a=oa-16|0;oa=a;a:{if(ma(a+12|0,a+8|0)|0){break a}b=yb((H[a+12>>2]<<2)+4|0);H[6860]=b;if(!b){break a}b=yb(H[a+8>>2]);if(b){c=H[6860];H[c+(H[a+12>>2]<<2)>>2]=0;if(!(la(c|0,b|0)|0)){break a}}H[6860]=0}oa=a+16|0;H[6876]=8192;H[6874]=94352;H[6867]=42;H[6885]=27608;H[6875]=65536}function Jb(a,b,c){var d=0,e=0,f=0;d=H[c+16>>2];a:{if(!d){if(Kb(c)){break a}d=H[c+16>>2]}e=H[c+20>>2];if(d-e>>>0<b>>>0){return sa[H[c+36>>2]](c,a,b)|0}b:{c:{if(!b|H[c+80>>2]<0){break c}d=b;while(1){f=a+d|0;if(I[f-1|0]!=10){d=d-1|0;if(d){continue}break c}break}e=sa[H[c+36>>2]](c,a,d)|0;if(e>>>0<d>>>0){break a}b=b-d|0;e=H[c+20>>2];break b}f=a;d=0}eb(e,f,b);H[c+20>>2]=H[c+20>>2]+b;e=b+d|0}return e}function Ne(a,b,c){var d=0,e=0,f=0,g=0;g=c&63;f=g;e=f&31;if(f>>>0>=32){f=-1>>>e|0}else{d=-1>>>e|0;f=d|(1<<e)-1<<32-e}f=f&a;d=b&d;e=g&31;if(g>>>0>=32){d=f<<e;g=0}else{d=(1<<e)-1&f>>>32-e|d<<e;g=f<<e}f=d;e=0-c&63;d=e&31;if(e>>>0>=32){d=-1<<d;c=0}else{c=-1<<d;d=c|(1<<d)-1&-1>>>32-d}a=c&a;b=b&d;d=e&31;if(e>>>0>=32){c=0;a=b>>>d|0}else{c=b>>>d|0;a=((1<<d)-1&b)<<32-d|a>>>d}a=a|g;ra=c|f;return a}
function ib(a,b,c){var d=0;if(!H[a+12>>2]){sa[b|0](c,H[a+36>>2]);return}d=Ga(8);a:{if(!d){break a}H[d+4>>2]=c;H[d>>2]=b;b=Ga(8);if(!b){Da(d);return}H[b>>2]=d;c=N(H[a+4>>2],100);H[a+40>>2]=c;while(1){if((c|0)<H[a+24>>2]){continue}break}H[b+4>>2]=H[a+20>>2];H[a+20>>2]=b;H[a+24>>2]=H[a+24>>2]+1;b=H[a+28>>2];if(!b){break a}H[H[b>>2]+8>>2]=0;H[a+28>>2]=H[b+4>>2];H[a+32>>2]=H[a+32>>2]-1;Da(b)}}function Xc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;H[a+184>>2]=b;d=H[a+96>>2];a:{if(!d){break a}f=H[d+24>>2];if(!f){break a}e=H[a+12>>2];if(!e|!H[e+5584>>2]){break a}e=H[d+16>>2];if(!e){return 1}d=0;while(1){if(K[(H[H[a+12>>2]+5584>>2]+N(d,1080)|0)+4>>2]<=b>>>0){Ca(c,1,9177,0);return 0}H[(N(d,52)+f|0)+40>>2]=b;g=1;d=d+1|0;if((e|0)!=(d|0)){continue}break}}return g|0}function Mc(a){var b=0,c=0;b=H[a+76>>2];if(!((b|0)>=0&(!b|H[6867]!=(b&1073741823)))){a:{if(H[a+80>>2]==10){break a}b=H[a+20>>2];if((b|0)==H[a+16>>2]){break a}H[a+20>>2]=b+1;F[b|0]=10;return}Nc(a);return}b=a+76|0;c=H[b>>2];H[b>>2]=c?c:1073741823;b:{c:{if(H[a+80>>2]==10){break c}c=H[a+20>>2];if((c|0)==H[a+16>>2]){break c}H[a+20>>2]=c+1;F[c|0]=10;break b}Nc(a)}H[b>>2]=0}function La(a,b,c,d,e,f,g,h){var i=0,j=0;i=+O(e-a|0);j=i*1.402;if(P(j)<2147483647){e=~~j}else{e=-2147483648}e=e+c|0;H[f>>2]=(e|0)>=0?(b|0)>(e|0)?e:b:0;j=+O(d-a|0);i=j*.344+i*.714;if(P(i)<2147483647){a=~~i}else{a=-2147483648}a=c-a|0;H[g>>2]=(a|0)>=0?(a|0)<(b|0)?a:b:0;i=j*1.772;if(P(i)<2147483647){a=~~i}else{a=-2147483648}a=a+c|0;H[h>>2]=(a|0)>=0?(a|0)<(b|0)?a:b:0}function od(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;e=H[a+84>>2];f=H[e>>2];d=H[e+4>>2];h=H[a+28>>2];g=H[a+20>>2]-h|0;g=d>>>0<g>>>0?d:g;if(g){eb(f,h,g);f=g+H[e>>2]|0;H[e>>2]=f;d=H[e+4>>2]-g|0;H[e+4>>2]=d}d=c>>>0>d>>>0?d:c;if(d){eb(f,b,d);f=d+H[e>>2]|0;H[e>>2]=f;H[e+4>>2]=H[e+4>>2]-d}F[f|0]=0;b=H[a+44>>2];H[a+28>>2]=b;H[a+20>>2]=b;return c|0}function Db(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;if(a){c=H[a+4>>2];if(c){Da(c);H[a+4>>2]=0}if(b){c=a;while(1){d=H[c+200>>2];if(d){e=0;f=H[c+196>>2];if(f){while(1){g=H[d+12>>2];if(g){Da(g);H[d+12>>2]=0;f=H[c+196>>2]}d=d+16|0;e=e+1|0;if(e>>>0<f>>>0){continue}break}d=H[c+200>>2]}Da(d);H[c+200>>2]=0}c=c+240|0;h=h+1|0;if((h|0)!=(b|0)){continue}break}}Da(a)}}function Cd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;e=H[c+8>>2];d=e>>>0<=1?1:e;f=H[c+4>>2];g=f-H[c>>2]|0;while(1){h=d;d=d<<1;if(h-g>>>0<b>>>0){continue}break}if((e|0)!=(h|0)){d=Ga(h);if(!d){return-1}e=H[c>>2];if(e){if(g){B(d,e,g)}Da(H[c>>2])}H[c+8>>2]=h;H[c>>2]=d;f=d+g|0;H[c+4>>2]=f}if(b){B(f,a,b)}H[c+4>>2]=H[c+4>>2]+b;return b|0}function ic(a){H[a+100>>2]=20832;H[a+96>>2]=20832;H[a+92>>2]=20832;H[a+88>>2]=20832;H[a+84>>2]=20832;H[a+80>>2]=20832;H[a+76>>2]=20832;H[a+72>>2]=20832;H[a+68>>2]=20832;H[a+64>>2]=20832;H[a+60>>2]=20832;H[a+56>>2]=20832;H[a+52>>2]=20832;H[a+48>>2]=20832;H[a+44>>2]=20832;H[a+40>>2]=20832;H[a+36>>2]=20832;H[a+32>>2]=20832;H[a+28>>2]=20832}function Ta(a,b){var c=0,d=0,e=0,f=0;if((b|0)<=0){return 0}c=H[a+12>>2];d=H[a+16>>2];while(1){e=b;a:{if(d){break a}c=c<<8&65280;H[a+12>>2]=c;d=(c|0)==65280?7:8;H[a+16>>2]=d;b=H[a+8>>2];if(b>>>0>=K[a+4>>2]){break a}H[a+8>>2]=b+1;c=I[b|0]|c;H[a+12>>2]=c}d=d-1|0;H[a+16>>2]=d;b=e-1|0;f=(c>>>d&1)<<b|f;if(e>>>0>1){continue}break}return f}function Id(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;f=oa-16|0;oa=f;e=H[a+24>>2];if((e|0)!=255){H[f>>2]=e;Ca(d,2,2678,f)}a:{b:{if(H[a+20>>2]==(c|0)){if(c){break b}e=1;break a}e=0;Ca(d,1,14510,0);break a}c=0;while(1){e=1;Ea(b,(H[a+72>>2]+N(c,12)|0)+8|0,1);b=b+1|0;c=c+1|0;if(c>>>0<K[a+20>>2]){continue}break}}oa=f+16|0;return e|0}function Ea(a,b,c){var d=0,e=0;H[b>>2]=0;a:{if(!c){break a}d=c&3;b=b+c|0;if(c>>>0>=4){e=c&-4;c=0;while(1){F[b-1|0]=I[a|0];F[b-2|0]=I[a+1|0];F[b-3|0]=I[a+2|0];b=b-4|0;F[b|0]=I[a+3|0];a=a+4|0;c=c+4|0;if((e|0)!=(c|0)){continue}break}}if(!d){break a}c=0;while(1){b=b-1|0;F[b|0]=I[a|0];a=a+1|0;c=c+1|0;if((d|0)!=(c|0)){continue}break}}}function se(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=oa-16|0;oa=e;a:{if(!c){Ca(d,1,4106,0);a=0;break a}Ea(b,e+12|0,1);f=c-1|0;a=1;if(!f){break a}a=0;c=0;while(1){b=b+1|0;Ea(b,e+8|0,1);g=H[e+8>>2];c=g<<24>>31&(g&127|c)<<7;a=a+1|0;if((f|0)!=(a|0)){continue}break}a=1;if(!c){break a}Ca(d,1,4106,0);a=0}oa=e+16|0;return a|0}function nc(a,b,c,d){var e=0,f=0,g=O(0),h=0,i=O(0),j=0,k=O(0);if(d){while(1){e=f<<2;h=e+b|0;i=L[h>>2];j=a+e|0;g=L[j>>2];e=c+e|0;k=L[e>>2];L[j>>2]=O(k*O(1.4019999504089355))+g;L[h>>2]=O(g+O(i*O(-.3441300094127655)))+O(k*O(-.714139997959137));L[e>>2]=g+O(i*O(1.7719999551773071));f=f+1|0;if((f|0)!=(d|0)){continue}break}}}function Gb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;f=oa-240|0;oa=f;H[f>>2]=a;g=1;a:{if((b|0)<2){break a}d=a;while(1){d=d-8|0;h=b-2|0;e=d-H[(h<<2)+c>>2]|0;if((db(a,e)|0)>=0){if((db(a,d)|0)>=0){break a}}i=e;e=(db(e,d)|0)>=0;d=e?i:d;H[(g<<2)+f>>2]=d;g=g+1|0;b=e?b-1|0:h;if((b|0)>1){continue}break}}Ic(f,g);oa=f+240|0}function Ic(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;c=8;f=oa-256|0;oa=f;if((b|0)>=2){h=(b<<2)+a|0;H[h>>2]=f;while(1){e=c>>>0>=256?256:c;eb(H[h>>2],H[a>>2],e);d=0;while(1){g=(d<<2)+a|0;d=d+1|0;eb(H[g>>2],H[(d<<2)+a>>2],e);H[g>>2]=H[g>>2]+e;if((b|0)!=(d|0)){continue}break}c=c-e|0;if(c){continue}break}}oa=f+256|0}function cd(a){a=a|0;var b=0,c=0,d=0,e=0;b=H[a+24>>2];if(b){c=H[a+28>>2];e=(c>>>0)/52|0;if(c>>>0>=52){while(1){c=H[b>>2];if(c){Da(c-1|0);H[b>>2]=0}c=H[b+4>>2];if(c){Da(c);H[b+4>>2]=0}c=H[b+8>>2];if(c){Da(c);H[b+8>>2]=0}b=b+52|0;d=d+1|0;if((e|0)!=(d|0)){continue}break}b=H[a+24>>2]}Da(b);H[a+24>>2]=0}}function dd(a){a=a|0;var b=0,c=0,d=0,e=0;b=H[a+24>>2];if(b){c=H[a+28>>2];e=(c>>>0)/68|0;if(c>>>0>=68){while(1){c=H[b>>2];if(c){Da(c);H[b>>2]=0}c=H[b+4>>2];if(c){Da(c);H[b+4>>2]=0}Da(H[b+60>>2]);H[b+60>>2]=0;b=b+68|0;d=d+1|0;if((e|0)!=(d|0)){continue}break}b=H[a+24>>2]}Da(b);H[a+24>>2]=0}}function id(a,b){a=a|0;b=b|0;var c=0,d=0;c=H[a+32>>2];b=H[a+28>>2];d=b+8|0;if(c>>>0>=d>>>0){while(1){qb(a,H[a+24>>2]+(b<<2)|0,H[a+20>>2],8);c=H[a+32>>2];b=d;d=b+8|0;if(c>>>0>=d>>>0){continue}break}}if(b>>>0<c>>>0){qb(a,H[a+24>>2]+(b<<2)|0,H[a+20>>2],c-b|0)}Da(H[a>>2]);Da(a)}function cb(a,b,c){var d=0,e=0,f=0;a:{if(!b){d=a;e=b;break a}while(1){d=Ke(a,b,10,0);e=ra;a=Ie(d,e,246)+a|0;c=c-1|0;F[c|0]=a|48;f=b>>>0>9;a=d;b=e;if(f){continue}break}}if(d|e){while(1){c=c-1|0;a=(d>>>0)/10|0;F[c|0]=N(a,246)+d|48;b=d>>>0>9;d=a;if(b){continue}break}}return c}function Nd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=oa-16|0;oa=e;a:{if(H[a+100>>2]){Ca(d,1,11302,0);a=0;break a}if((c|0)!=4){Ca(d,1,5991,0);a=0;break a}Ea(b,e+12|0,4);if(H[e+12>>2]!=218793738){Ca(d,1,5007,0);a=0;break a}H[a+100>>2]=H[a+100>>2]|1;a=1}oa=e+16|0;return a|0}function Ya(a,b,c){var d=0,e=0;a:{d=H[a>>2];e=H[a+4>>2];b:{if((d|0)!=(e|0)){e=H[a+8>>2];break b}d=e+10|0;H[a+4>>2]=d;e=Ia(H[a+8>>2],d<<2);if(!e){break a}H[a+8>>2]=e;d=H[a>>2]}H[(d<<2)+e>>2]=b;H[a>>2]=d+1;return 1}Da(H[a+8>>2]);H[a>>2]=0;H[a+4>>2]=0;Ca(c,1,6123,0);return 0}function Nc(a){var b=0,c=0,d=0;c=oa-16|0;oa=c;F[c+15|0]=10;b=H[a+16>>2];a:{if(!b){if(Kb(a)){break a}b=H[a+16>>2]}d=b;b=H[a+20>>2];if(!((d|0)==(b|0)|H[a+80>>2]==10)){H[a+20>>2]=b+1;F[b|0]=10;break a}if((sa[H[a+36>>2]](a,c+15|0,1)|0)!=1){break a}}oa=c+16|0}function Ec(a){var b=0,c=0,d=0,e=0,f=0;d=H[a>>2];b=F[d|0]-48|0;if(b>>>0>9){return 0}while(1){e=-1;if(c>>>0<=214748364){c=N(c,10);e=(c^2147483647)>>>0<b>>>0?-1:c+b|0}b=d+1|0;H[a>>2]=b;f=F[d+1|0];c=e;d=b;b=f-48|0;if(b>>>0<10){continue}break}return c}function Bc(a,b){var c=0,d=0,e=0;x(+a);d=s(1)|0;e=s(0)|0;c=d>>>20&2047;if((c|0)!=2047){if(!c){if(a==0){c=0}else{a=Bc(a*0x10000000000000000,b);c=H[b>>2]+-64|0}H[b>>2]=c;return a}H[b>>2]=c-1022;u(0,e|0);u(1,d&-2146435073|1071644672);a=+w()}return a}function de(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=O(0),f=0,g=0;d=oa-16|0;oa=d;if(c){while(1){Yc(a,d+12|0);e=L[d+12>>2];if(O(P(e))<O(2147483648)){f=~~e}else{f=-2147483648}H[b>>2]=f;b=b+4|0;a=a+4|0;g=g+1|0;if((g|0)!=(c|0)){continue}break}}oa=d+16|0}function Va(a){var b=0,c=0,d=0;if(a){b=H[a+24>>2];if(b){c=H[a+16>>2];if(c){b=0;while(1){d=H[(H[a+24>>2]+N(b,52)|0)+44>>2];if(d){Da(d);c=H[a+16>>2]}b=b+1|0;if(c>>>0>b>>>0){continue}break}b=H[a+24>>2]}Da(b)}b=H[a+28>>2];if(b){Da(b)}Da(a)}}function ce(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;d=oa-16|0;oa=d;if(c){while(1){Vb(a,d+8|0);e=M[d+8>>3];if(P(e)<2147483647){f=~~e}else{f=-2147483648}H[b>>2]=f;b=b+4|0;a=a+8|0;g=g+1|0;if((g|0)!=(c|0)){continue}break}}oa=d+16|0}function Bd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;d=H[c+4>>2];e=H[c>>2]+H[c+8>>2]|0;if((d|0)==(e|0)){ra=-1;return-1}H[c+4>>2]=a+d;f=a;c=e-d|0;d=c;e=a>>>0<c>>>0;a=c>>31;c=e&(a|0)>=(b|0)|(a|0)>(b|0);d=c?f:d;ra=c?b:a;return d|0}function Je(a,b,c,d){var e=0,f=0,g=0,h=0;f=b^d;g=f>>31;e=b>>31;a=a^e;h=a-e|0;e=(b^e)-((a>>>0<e>>>0)+e|0)|0;a=d>>31;b=c^a;f=f>>31;a=Ke(h,e,b-a|0,(a^d)-((a>>>0>b>>>0)+a|0)|0)^f;b=a-f|0;ra=(g^ra)-((a>>>0<f>>>0)+g|0)|0;return b}function Xa(a){var b=0,c=0,d=0,e=0;if(a){b=H[a+20>>2];c=H[a+16>>2];if(N(b,c)){while(1){e=H[H[a+24>>2]+(d<<2)>>2];if(e){Da(e);c=H[a+16>>2];b=H[a+20>>2]}d=d+1|0;if(d>>>0<N(b,c)>>>0){continue}break}}Da(H[a+24>>2]);Da(a)}}function oc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0;if(d){while(1){e=f<<2;g=e+a|0;h=c+e|0;i=H[h>>2];j=b+e|0;k=H[j>>2];e=H[g>>2]-(i+k>>2)|0;H[g>>2]=e+i;H[j>>2]=e;H[h>>2]=e+k;f=f+1|0;if((f|0)!=(d|0)){continue}break}}}function fb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;H[a+48>>2]=0;H[a+36>>2]=H[a+32>>2];e=sa[H[a+28>>2]](b,c,H[a>>2])|0;d=H[a+68>>2];if(!e){H[a+68>>2]=d|4;return 0}H[a+56>>2]=b;H[a+60>>2]=c;H[a+68>>2]=d&-5;return 1}function Oa(a,b,c,d,e){var f=0;f=oa-256|0;oa=f;if(!(e&73728|(c|0)<=(d|0))){d=c-d|0;c=d>>>0<256;Oc(f,b,c?d:256);if(!c){while(1){Ma(a,f,256);d=d-256|0;if(d>>>0>255){continue}break}}Ma(a,f,d)}oa=f+256|0}function Ie(a,b,c){var d=0,e=0,f=0,g=0,h=0;e=c>>>16|0;d=a>>>16|0;h=N(e,d);f=c&65535;a=a&65535;g=N(f,a);d=(g>>>16|0)+N(d,f)|0;a=(d&65535)+N(a,e)|0;ra=h+N(b,c)+(d>>>16)+(a>>>16)|0;return g&65535|a<<16}function Kb(a){var b=0;b=H[a+72>>2];H[a+72>>2]=b-1|b;b=H[a>>2];if(b&8){H[a>>2]=b|32;return-1}H[a+4>>2]=0;H[a+8>>2]=0;b=H[a+44>>2];H[a+28>>2]=b;H[a+20>>2]=b;H[a+16>>2]=b+H[a+48>>2];return 0}function tc(a){var b=0,c=0;a:{if(I[a+12|0]==255){H[a+12>>2]=65280;H[a+16>>2]=7;b=H[a+8>>2];c=0;if(b>>>0>=K[a+4>>2]){break a}H[a+8>>2]=b+1;H[a+12>>2]=I[b|0]|65280}H[a+16>>2]=0;c=1}return c}function Dd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;e=H[c+4>>2];d=H[c>>2]+H[c+8>>2]|0;if((e|0)==(d|0)){return-1}d=d-e|0;b=b>>>0>d>>>0?d:b;if(b){B(a,e,b)}H[c+4>>2]=b+H[c+4>>2];return b|0}function he(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=oa-16|0;oa=d;if(c){while(1){Yc(a,d+12|0);L[b>>2]=L[d+12>>2];b=b+4|0;a=a+4|0;e=e+1|0;if((e|0)!=(c|0)){continue}break}}oa=d+16|0}function ge(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=oa-16|0;oa=d;if(c){while(1){Vb(a,d+8|0);L[b>>2]=M[d+8>>3];b=b+4|0;a=a+8|0;e=e+1|0;if((e|0)!=(c|0)){continue}break}}oa=d+16|0}function jd(a,b){a=a|0;b=b|0;b=H[a+28>>2];if(b>>>0<K[a+32>>2]){while(1){lc(a,H[a+24>>2]+(N(H[a+20>>2],b)<<2)|0);b=b+1|0;if(b>>>0<K[a+32>>2]){continue}break}}Da(H[a>>2]);Da(a)}function nd(a,b){a=a|0;b=+b;var c=0;ja(a|0,0)|0;a=(a|0)==2?27:(a|0)==1?26:14;a:{if(H[7170]>>>a-1&1){H[7202]=H[7202]|1<<a-1;break a}c=H[(a<<2)+25808>>2];if(c){sa[c|0](a)}}}function Tc(a,b){a=a|0;b=b|0;var c=0,d=0;c=H[a>>2];d=H[b>>2];a=H[a+4>>2];b=H[b+4>>2];return(c>>>0>d>>>0&(a|0)>=(b|0)|(a|0)>(b|0))-(c>>>0<d>>>0&(a|0)<=(b|0)|(a|0)<(b|0))|0}function vd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=oa-16|0;oa=e;a=Hb(fa(H[a+60>>2],b|0,c|0,d&255,e+8|0)|0);oa=e+16|0;ra=a?-1:H[e+12>>2];return(a?-1:H[e+8>>2])|0}function yc(a,b,c,d){var e=0,f=0;e=oa-16|0;oa=e;if(c){while(1){Ea(a,e+12|0,d);L[b>>2]=K[e+12>>2];b=b+4|0;a=a+d|0;f=f+1|0;if((f|0)!=(c|0)){continue}break}}oa=e+16|0}function xc(a,b,c,d){var e=0,f=0;e=oa-16|0;oa=e;if(c){while(1){Ea(a,e+12|0,d);H[b>>2]=H[e+12>>2];b=b+4|0;a=a+d|0;f=f+1|0;if((f|0)!=(c|0)){continue}break}}oa=e+16|0}function Vb(a,b){F[b+7|0]=I[a|0];F[b+6|0]=I[a+1|0];F[b+5|0]=I[a+2|0];F[b+4|0]=I[a+3|0];F[b+3|0]=I[a+4|0];F[b+2|0]=I[a+5|0];F[b+1|0]=I[a+6|0];F[b|0]=I[a+7|0]}function Td(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(c){Ca(d,2,10224,0);if(!Nb(H[a>>2],b,c,d,e)){Ca(d,1,6210,0);return 0}a=Qc(a,c,d)}else{a=0}return a|0}function Sa(a){var b=0,c=0,d=0,e=0;b=H[a+12>>2];e=b;c=H[a+8>>2];if(!(b|c)){ra=0;return 0}d=H[a+56>>2];b=c-d|0;ra=e-(H[a+60>>2]+(c>>>0<d>>>0)|0)|0;return b}function Xd(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;return Za(H[a>>2],b,c,d,e,f,g,h,i,j,k)|0}function wc(a,b){var c=0;c=oa-16|0;oa=c;if(a){if(b&3){a=28}else{a=kb(b,a);H[c+12>>2]=a;a=a?0:48}a=a?0:H[c+12>>2]}else{a=0}oa=c+16|0;return a}function ed(a){a=a|0;var b=0;if(a){b=H[a+116>>2];if(b){Da(b);H[a+116>>2]=0}b=H[a+120>>2];if(b){Da(b);H[a+120>>2]=0}Da(H[a+148>>2]);Da(a)}}
function vb(a,b){var c=0,d=0;a:{if(b>>>0<=31){d=H[a>>2];c=a+4|0;break a}b=b-32|0;c=a}c=H[c>>2];H[a>>2]=d<<b;H[a+4>>2]=c<<b|d>>>32-b}function xb(a,b){var c=0,d=0;c=H[a+4>>2];a:{if(b>>>0<=31){d=H[a>>2];break a}b=b-32|0;d=c;c=0}H[a+4>>2]=c>>>b;H[a>>2]=c<<32-b|d>>>b}function be(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(!c){return 0}if(!Pb(H[a>>2],b,c,d)){Ca(d,1,6210,0);return 0}return Qc(a,c,d)|0}function pe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(H[H[a+96>>2]+16>>2]<<2!=(c|0)){Ca(d,1,4464,0);a=0}else{a=1}return a|0}function vc(){var a=0,b=0;a=Fa(1,44);a:{if(a){H[a+16>>2]=0;b=Fa(1,8);H[a+36>>2]=b;if(b){break a}Da(a)}a=0}return a}function $b(a,b){a=a|0;b=b|0;if(!(!a|!b)){H[a+188>>2]=H[b+4>>2];H[a+184>>2]=H[b>>2];H[a+248>>2]=H[b+8248>>2]&2}}function tb(){var a=0,b=0;a=Fa(1,12);if(a){H[a+4>>2]=10;b=Fa(10,4);H[a+8>>2]=b;if(b){return a}Da(a)}return 0}function Ud(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;return Wb(H[a>>2],b,c,d,e,f,g)|0}function jb(a){var b=0;if(a){b=H[a+4>>2];if(b){sa[b|0](H[a>>2])}Da(H[a+32>>2]);H[a+32>>2]=0;Da(a)}}function _b(a,b){a=a|0;b=b|0;a:{if(!a){break a}H[a+208>>2]=b;if(!b){break a}F[a+92|0]=I[a+92|0]|8}}function Ad(a,b,c){a=a|0;b=b|0;c=c|0;b=H[c+8>>2];H[c+4>>2]=H[c>>2]+(a>>>0>b>>>0?b:a);return 1}function Wd(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return gb(H[a>>2],b,c,d,e,f)|0}function te(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(c){a=1}else{Ca(d,1,4375,0);a=0}return a|0}function nb(a){H[a>>2]=0;H[a+4>>2]=0;H[a+16>>2]=0;H[a+20>>2]=0;H[a+8>>2]=0;H[a+12>>2]=0}function ad(a,b,c){a=a|0;b=b|0;c=c|0;return!H[a+8>>2]&(H[a+216>>2]!=0&H[a+220>>2]!=0)}function Ua(a){if(H[a+12>>2]){H[a+40>>2]=0;while(1){if(H[a+24>>2]>0){continue}break}}}function Yc(a,b){F[b+3|0]=I[a|0];F[b+2|0]=I[a+1|0];F[b+1|0]=I[a+2|0];F[b|0]=I[a+3|0]}function lb(a){if(a){sa[H[(H[a+76>>2]?20:16)+a>>2]](H[a+48>>2]);H[a+48>>2]=0;Da(a)}}function ae(a,b){a=a|0;b=b|0;$b(H[a>>2],b);F[a+124|0]=0;H[a+128>>2]=H[b+8248>>2]&1}function Fa(a,b){if(!a|!b){a=0}else{b=N(a,b);a=kb(8,b);if(a){Oc(a,0,b)}}return a}function Ha(a,b,c){var d=0;d=oa-16|0;oa=d;H[d+12>>2]=c;Gc(a,b,c,0,0);oa=d+16|0}function Me(a){var b=0;while(1){if(a){a=a-1&a;b=b+1|0;continue}break}return b}function bb(a){var b=0;if(a){b=H[a+12>>2];if(b){Da(b);H[a+12>>2]=0}Da(a)}}function Vd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Xb(H[a>>2],b,c,d)|0}function Pa(a,b,c){a:{if(H[c+76>>2]<0){a=Jb(a,b,c);break a}a=Jb(a,b,c)}}function Jc(a,b){a=Hc(a-1|0);if(!a){a=Hc(b);a=a?a|32:0}return a}function ac(a){return H[a+12>>2]==H[a+4>>2]|H[a+8>>2]==H[a>>2]}function Od(a,b,c){a=a|0;b=b|0;c=c|0;return Xc(H[a>>2],b,c)|0}function sb(a){var b=0;if(a){b=H[a+8>>2];if(b){Da(b)}Da(a)}}function Hc(a){var b=0,c=0,d=0;return b=He(a),c=0,d=a,d?b:c}function rd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;ra=0;return 0}function ab(a,b,c,d,e,f,g,h){return mc(a,b,c,d,e,f,g,h,0)}function zc(a,b,c,d){return sa[H[a+44>>2]](a,b,c,d)|0}function _a(a,b,c){H[((b<<2)+a|0)+28>>2]=(c<<5)+20832}function Mb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return 1}function Sd(a,b,c){a=a|0;b=b|0;c=c|0;Tb(H[a>>2],b,c)}function ub(a,b,c){return sa[H[a+40>>2]](a,b,0,c)|0}function ne(a,b,c){a=a|0;b=b|0;c=c|0;ra=-1;return-1}function He(a){if(a){return 31-Q(a-1^a)|0}return 32}function Ra(a,b,c,d,e,f,g,h){mc(a,b,c,d,e,f,g,h,1)}function td(a){a=a|0;return Hb(Z(H[a+60>>2])|0)|0}function Hb(a){if(!a){return 0}H[6597]=a;return-1}function je(a,b,c){a=a|0;b=b|0;c=c|0;yc(a,b,c,2)}function ie(a,b,c){a=a|0;b=b|0;c=c|0;yc(a,b,c,4)}function fe(a,b,c){a=a|0;b=b|0;c=c|0;xc(a,b,c,2)}function ee(a,b,c){a=a|0;b=b|0;c=c|0;xc(a,b,c,4)}function Ma(a,b,c){if(!(I[a|0]&32)){Jb(b,c,a)}}function Le(a,b,c){Ge(a,0,b,c);ra=qa;return pa}function xe(a,b,c){a=a|0;b=b|0;c=c|0;return 0}function Zb(a,b,c){a=a|0;b=b|0;c=c|0;return 1}function Uc(a,b,c){a=a|0;b=b|0;c=c|0;return-1}function Ke(a,b,c,d){a=Ge(a,b,c,d);return a}function Ga(a){if(!a){return 0}return yb(a)}function Oc(a,b,c){if(c){y(a,b<<24>>24,c)}}function $d(a,b){a=a|0;b=b|0;_b(H[a>>2],b)}function uc(a){return H[a+8>>2]-H[a>>2]|0}function ld(a){a=a|0;ha();ga(a+128|0);D()}function Rd(a){a=a|0;return Qb(H[a>>2])|0}function Pd(a){a=a|0;return Rb(H[a>>2])|0}function yb(a){a=a|0;return kb(8,a)|0}function bd(a,b){a=a|0;b=b|0;return 0}function xd(a,b){a=a|0;b=b|0;$(a|0)}function wd(a,b){a=a|0;b=b|0;Y(a|0)}function Fb(a){return H[a+28>>2]!=2}function eb(a,b,c){if(c){B(a,b,c)}}function db(a,b){return Tc(a,b)}function rb(a){return wc(a,32)}function Ja(a){return wc(a,16)}function zb(){return Fa(1,36)}function sd(a){a=a|0;return 0}function md(a){a=a|0;Ac();D()}function cc(a,b){a=a|0;b=b|0}function hb(a){if(a){Da(a)}}function Qa(a){H[a>>2]=0}function kd(){Ac();D()}function Ac(){ia();D()}
// EMSCRIPTEN_END_FUNCS
a=I;m(n);var sa=[null,cc,xe,ne,Uc,Uc,fb,Qd,Fd,zd,jd,id,hd,gd,fd,ed,dd,cd,Zb,ad,$c,_c,Zc,Tc,Ee,De,Ce,Be,Ae,ze,ye,we,ve,ue,te,se,re,qe,pe,Mb,oe,me,Mb,Mb,le,ke,je,ie,he,ge,fe,ee,de,ce,Zd,Nd,Md,Ld,Kd,Jd,Id,Hd,Gd,Ed,Dd,Cd,Bd,Ad,Qb,Rb,Tb,Zb,Pb,_b,$b,Bb,Yb,bd,Xb,Xc,Nb,Wb,gb,Za,Rd,Pd,Sd,_d,be,bd,Vd,Od,Td,Ud,$d,ae,Pc,Wd,Xd,Yd,cc,xd,wd,od,qd,pd,kd,td,ud,vd,sd,rd,ld,md];function ta(){return E.byteLength/65536|0}function ya(za){za=za|0;var ua=ta()|0;var va=ua+za|0;if(ua<va&&va<65536){var wa=new ArrayBuffer(N(va,65536));var xa=new Int8Array(wa);xa.set(F);F=new Int8Array(wa);G=new Int16Array(wa);H=new Int32Array(wa);I=new Uint8Array(wa);J=new Uint16Array(wa);K=new Uint32Array(wa);L=new Float32Array(wa);M=new Float64Array(wa);E=wa;a=I}return ua}return{s:Object.create(Object.prototype,{grow:{value:ya},buffer:{get:function(){return E}}}),t:Fe,u:yb,v:Da,w:yd,x:nd}}return Aa(Ba)}
// EMSCRIPTEN_END_ASM


)(info)},instantiate:function(binary,info){return{then:function(ok){var module=new WebAssembly.Module(binary);ok({instance:new WebAssembly.Instance(module,info)})}}},RuntimeError:Error,isWasm2js:true};if(WebAssembly.isWasm2js){wasmBinary=[]}var wasmMemory;var ABORT=false;var EXITSTATUS;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;var runtimeInitialized=false;function updateMemoryViews(){var b=wasmMemory.buffer;HEAP8=new Int8Array(b);HEAP16=new Int16Array(b);HEAPU8=new Uint8Array(b);HEAPU16=new Uint16Array(b);HEAP32=new Int32Array(b);HEAPU32=new Uint32Array(b);HEAPF32=new Float32Array(b);HEAPF64=new Float64Array(b)}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(onPreRuns)}function initRuntime(){runtimeInitialized=true;wasmExports["t"]()}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(onPostRuns)}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var wasmBinaryFile;function findWasmBinary(){if(Module["locateFile"]){return locateFile("openjpeg_nowasm_fallback.wasm")}return new URL("openjpeg_nowasm_fallback.wasm",import.meta.url).href}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw'sync fetching of the wasm failed: you can preload it to Module["wasmBinary"] manually, or emcc.py will do that for you when generating HTML (but not JS)'}function instantiateSync(file,info){var module;var binary=getBinarySync(file);module=new WebAssembly.Module(binary);var instance=new WebAssembly.Instance(module,info);return[instance,module]}function getWasmImports(){return{a:wasmImports}}function createWasm(){function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["s"];updateMemoryViews();removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");var info=getWasmImports();if(Module["instantiateWasm"]){return new Promise((resolve,reject)=>{Module["instantiateWasm"](info,(mod,inst)=>{resolve(receiveInstance(mod,inst))})})}wasmBinaryFile??=findWasmBinary();var result=instantiateSync(wasmBinaryFile,info);return receiveInstance(result[0])}class ExitStatus{name="ExitStatus";constructor(status){this.message=`Program terminated with exit(${status})`;this.status=status}}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var onPostRuns=[];var addOnPostRun=cb=>onPostRuns.push(cb);var onPreRuns=[];var addOnPreRun=cb=>onPreRuns.push(cb);var noExitRuntime=true;var __abort_js=()=>abort("");var runtimeKeepaliveCounter=0;var __emscripten_runtime_keepalive_clear=()=>{noExitRuntime=false;runtimeKeepaliveCounter=0};var timers={};var handleException=e=>{if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)};var keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0;var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){Module["onExit"]?.(code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var maybeExit=()=>{if(!keepRuntimeAlive()){try{_exit(EXITSTATUS)}catch(e){handleException(e)}}};var callUserCallback=func=>{if(ABORT){return}try{func();maybeExit()}catch(e){handleException(e)}};var _emscripten_get_now=()=>performance.now();var __setitimer_js=(which,timeout_ms)=>{if(timers[which]){clearTimeout(timers[which].id);delete timers[which]}if(!timeout_ms)return 0;var id=setTimeout(()=>{delete timers[which];callUserCallback(()=>__emscripten_timeout(which,_emscripten_get_now()))},timeout_ms);timers[which]={id,timeout_ms};return 0};function _copy_pixels_1(compG_ptr,nb_pixels){compG_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);imageData.set(compG)}function _copy_pixels_3(compR_ptr,compG_ptr,compB_ptr,nb_pixels){compR_ptr>>=2;compG_ptr>>=2;compB_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*3);const compR=HEAP32.subarray(compR_ptr,compR_ptr+nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compB=HEAP32.subarray(compB_ptr,compB_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[3*i]=compR[i];imageData[3*i+1]=compG[i];imageData[3*i+2]=compB[i]}}function _copy_pixels_4(compR_ptr,compG_ptr,compB_ptr,compA_ptr,nb_pixels){compR_ptr>>=2;compG_ptr>>=2;compB_ptr>>=2;compA_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compR=HEAP32.subarray(compR_ptr,compR_ptr+nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compB=HEAP32.subarray(compB_ptr,compB_ptr+nb_pixels);const compA=HEAP32.subarray(compA_ptr,compA_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=compR[i];imageData[4*i+1]=compG[i];imageData[4*i+2]=compB[i];imageData[4*i+3]=compA[i]}}var getHeapMax=()=>2147483648;var alignMemory=(size,alignment)=>Math.ceil(size/alignment)*alignment;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536|0;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignMemory(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:lang,_:getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var _environ_get=(__environ,environ_buf)=>{var bufSize=0;var envp=0;for(var string of getEnvStrings()){var ptr=environ_buf+bufSize;HEAPU32[__environ+envp>>2]=ptr;bufSize+=stringToUTF8(string,ptr,Infinity)+1;envp+=4}return 0};var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;for(var string of strings){bufSize+=lengthBytesUTF8(string)+1}HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var _fd_close=fd=>52;var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);return 70}var printCharBuffers=[null,[],[]];var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder:undefined;var UTF8ArrayToString=(heapOrArray,idx=0,maxBytesToRead=NaN)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var printChar=(stream,curr)=>{var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer));buffer.length=0}else{buffer.push(curr)}};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):"";var _fd_write=(fd,iov,iovcnt,pnum)=>{var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){printChar(fd,HEAPU8[ptr+j])}num+=len}HEAPU32[pnum>>2]=num;return 0};function _gray_to_rgba(compG_ptr,nb_pixels){compG_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=imageData[4*i+1]=imageData[4*i+2]=compG[i];imageData[4*i+3]=255}}function _graya_to_rgba(compG_ptr,compA_ptr,nb_pixels){compG_ptr>>=2;compA_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compA=HEAP32.subarray(compA_ptr,compA_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=imageData[4*i+1]=imageData[4*i+2]=compG[i];imageData[4*i+3]=compA[i]}}function _jsPrintWarning(message_ptr){const message=UTF8ToString(message_ptr);(Module.warn||console.warn)(`OpenJPEG: ${message}`)}function _rgb_to_rgba(compR_ptr,compG_ptr,compB_ptr,nb_pixels){compR_ptr>>=2;compG_ptr>>=2;compB_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compR=HEAP32.subarray(compR_ptr,compR_ptr+nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compB=HEAP32.subarray(compB_ptr,compB_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=compR[i];imageData[4*i+1]=compG[i];imageData[4*i+2]=compB[i];imageData[4*i+3]=255}}function _storeErrorMessage(message_ptr){const message=UTF8ToString(message_ptr);if(!Module.errorMessages){Module.errorMessages=message}else{Module.errorMessages+="\n"+message}}var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};{if(Module["noExitRuntime"])noExitRuntime=Module["noExitRuntime"];if(Module["print"])out=Module["print"];if(Module["printErr"])err=Module["printErr"];if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"]}Module["writeArrayToMemory"]=writeArrayToMemory;var wasmImports={m:__abort_js,l:__emscripten_runtime_keepalive_clear,n:__setitimer_js,g:_copy_pixels_1,f:_copy_pixels_3,e:_copy_pixels_4,o:_emscripten_resize_heap,p:_environ_get,q:_environ_sizes_get,b:_fd_close,j:_fd_seek,c:_fd_write,r:_gray_to_rgba,i:_graya_to_rgba,d:_jsPrintWarning,k:_proc_exit,h:_rgb_to_rgba,a:_storeErrorMessage};var wasmExports=createWasm();var ___wasm_call_ctors=wasmExports["t"];var _malloc=Module["_malloc"]=wasmExports["u"];var _free=Module["_free"]=wasmExports["v"];var _jp2_decode=Module["_jp2_decode"]=wasmExports["w"];var __emscripten_timeout=wasmExports["x"];var dynCall_iji=Module["dynCall_iji"]=wasmExports["dynCall_iji"];var dynCall_jji=Module["dynCall_jji"]=wasmExports["dynCall_jji"];var dynCall_iiji=Module["dynCall_iiji"]=wasmExports["dynCall_iiji"];var dynCall_jiji=Module["dynCall_jiji"]=wasmExports["dynCall_jiji"];function run(){if(runDependencies>0){dependenciesFulfilled=run;return}preRun();if(runDependencies>0){dependenciesFulfilled=run;return}function doRun(){Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);Module["onRuntimeInitialized"]?.();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(()=>{setTimeout(()=>Module["setStatus"](""),1);doRun()},1)}else{doRun()}}function preInit(){if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].shift()()}}}preInit();run();moduleRtn=Module;


  return moduleRtn;
}
);
})();
export default OpenJPEG;
