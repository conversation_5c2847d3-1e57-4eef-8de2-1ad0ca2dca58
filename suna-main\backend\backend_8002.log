{"event": "No API key found for provider: OPENAI", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.004697Z"}
{"event": "No API key found for provider: ANTHROPIC", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.004890Z"}
{"event": "No API key found for provider: GROQ", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.005013Z"}
{"event": "No API key found for provider: OPENROUTER", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.005108Z"}
{"event": "No API key found for provider: XAI", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.005399Z"}
{"event": "No API key found for provider: MORPH", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.005493Z"}
{"event": "No API key found for provider: GEMINI", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 49, "timestamp": "2025-09-19T04:22:56.005578Z"}
{"event": "Missing AWS credentials for Bedrock integration - access_key: False, secret_key: False, region: us-east-1", "level": "warning", "filename": "llm.py", "func_name": "setup_api_keys", "lineno": 68, "timestamp": "2025-09-19T04:22:56.005683Z"}
MAILTRAP_API_TOKEN not found in environment variables
{"event": "Default circuit breakers initialized", "level": "info", "filename": "circuit_breaker.py", "func_name": "create_default_breakers", "lineno": 938, "timestamp": "2025-09-19T04:22:58.861450Z"}
INFO:     Started server process [93030]
INFO:     Waiting for application startup.
{"event": "Starting up FastAPI application with instance ID: single in local mode", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 47, "timestamp": "2025-09-19T04:22:59.027852Z"}
{"event": "Versioning API initialized", "level": "info", "filename": "api.py", "func_name": "initialize", "lineno": 228, "timestamp": "2025-09-19T04:22:59.052496Z"}
{"event": "Initialized agent API with instance ID: single", "level": "info", "filename": "api.py", "func_name": "initialize", "lineno": 202, "timestamp": "2025-09-19T04:22:59.052706Z"}
{"event": "Initialized sandbox API with database connection", "level": "info", "filename": "api.py", "func_name": "initialize", "lineno": 23, "timestamp": "2025-09-19T04:22:59.052858Z"}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-19T04:22:59.052996Z"}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-19T04:22:59.059728Z"}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-19T04:22:59.060972Z"}
{"event": "Redis connection initialized successfully", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 63, "timestamp": "2025-09-19T04:22:59.061276Z"}
{"event": "Performance monitoring started", "level": "info", "filename": "performance_monitor.py", "func_name": "start_monitoring", "lineno": 249, "timestamp": "2025-09-19T04:22:59.061595Z"}
{"event": "Monitoring and self-healing systems initialized", "level": "info", "filename": "api.py", "func_name": "initialize", "lineno": 411, "timestamp": "2025-09-19T04:22:59.062031Z"}
{"event": "Monitoring system initialized", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 79, "timestamp": "2025-09-19T04:22:59.062194Z"}
{"event": "Self-healing system started with 6 rules", "level": "info", "filename": "self_healing.py", "func_name": "start_monitoring", "lineno": 583, "timestamp": "2025-09-19T04:22:59.164199Z"}
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T19:57:20.265268Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "382ac6e8-9141-4f36-b226-69667af694ff", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T19:57:20.266105Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "382ac6e8-9141-4f36-b226-69667af694ff", "query_params": ""}
INFO:     127.0.0.1:58926 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:01:16.022370Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:01:16.022910Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "1def9334-fa14-4a14-94ef-abfc10ffbb4a", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:01:16.023570Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "1def9334-fa14-4a14-94ef-abfc10ffbb4a", "query_params": ""}
INFO:     127.0.0.1:44860 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44860 - "OPTIONS /api/billing/subscription HTTP/1.1" 200 OK
INFO:     127.0.0.1:44862 - "OPTIONS /api/billing/available-models HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:01:16.579653Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:01:16.579911Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:01:16.580534Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:01:16.580710Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:01:16.580984Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:01:16.581310Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:01:16.588229Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/available-models", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:01:16.589365Z"}
{"event": "Request started: GET /api/billing/available-models from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:01:16.589751Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "85ac0250-e397-4db4-ae2b-4757ccf54ca4", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:01:16.590231Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "85ac0250-e397-4db4-ae2b-4757ccf54ca4", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:01:16.590378Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "85ac0250-e397-4db4-ae2b-4757ccf54ca4", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:01:16.590602Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "85ac0250-e397-4db4-ae2b-4757ccf54ca4", "query_params": ""}
{"event": "Running in local development mode - billing checks are disabled", "level": "info", "filename": "billing.py", "func_name": "get_available_models", "lineno": 1517, "timestamp": "2025-09-20T20:01:16.590791Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "85ac0250-e397-4db4-ae2b-4757ccf54ca4", "query_params": ""}
INFO:     127.0.0.1:44862 - "GET /api/billing/available-models HTTP/1.1" 200 OK
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:01:16.591628Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:01:16.591991Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:01:16.592316Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:01:16.602032Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:01:16.603230Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:01:16.603546Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "b5152e1a-f780-4526-8667-bcac329eb8bd", "query_params": ""}
INFO:     127.0.0.1:44860 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:02:33.486032Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:02:33.486678Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b1fd3f25-7001-46d0-8052-56f80559fc42", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:02:33.487215Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b1fd3f25-7001-46d0-8052-56f80559fc42", "query_params": ""}
INFO:     127.0.0.1:43964 - "GET /api/health HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:02:39.554756Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:02:39.556232Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:02:39.557486Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:02:39.558128Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "Fetching Composio toolkits with limit: 100, cursor: None, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:02:39.558619Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "Initializing Composio client", "level": "info", "filename": "client.py", "func_name": "get_client", "lineno": 18, "timestamp": "2025-09-20T20:02:39.558904Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "Fetching toolkits with limit: 100, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:02:39.588319Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:02:40.485420Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5159de48-97f5-435a-89b5-4ccf02620d68", "query_params": ""}
INFO:     127.0.0.1:33482 - "GET /api/composio/toolkits HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits/googlesheets/details from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:02:49.832020Z", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:02:49.832542Z", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:02:49.832700Z", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:02:49.832870Z", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Fetching detailed toolkit info for: googlesheets", "level": "info", "filename": "api.py", "func_name": "get_toolkit_details", "lineno": 142, "timestamp": "2025-09-20T20:02:49.833024Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Fetching detailed toolkit info for: googlesheets", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 259, "timestamp": "2025-09-20T20:02:49.833183Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Raw toolkit response for googlesheets: ToolkitRetrieveResponse(deprecated=Deprecated(raw_proxy_info_by_auth_schemes=[{'auth_method': 'OAUTH2', 'proxy': {}}], toolkit_id='f034804e-ac1e-49ee-8e9d-1c4fab4767f4', get_current_user_endpoint='https://www.googleapis.com/oauth2/v3/userinfo', toolkitId='f034804e-ac1e-49ee-8e9d-1c4fab4767f4', getCurrentUserEndpoint='https://www.googleapis.com/oauth2/v3/userinfo', rawProxyInfoByAuthSchemes=[{'auth_method': 'OAUTH2', 'proxy': {}}]), enabled=True, is_local_toolkit=False, meta=Meta(categories=[MetaCategory(name='Productivity & Project Management', slug='productivity-&-project-management')], created_at='Wed May 15 2024', description='Google Sheets is a cloud-based spreadsheet tool enabling real-time collaboration, data analysis, and integration with other Google Workspace apps', logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg', tools_count=36.0, triggers_count=2.0, updated_at='Thu Jul 31 2025', app_url='https://sheets.google.com/'), name='Googlesheets', slug='googlesheets', auth_config_details=[AuthConfigDetail(fields=AuthConfigDetailFields(auth_config_creation=AuthConfigDetailFieldsAuthConfigCreation(optional=[AuthConfigDetailFieldsAuthConfigCreationOptional(description=\"Add this Redirect URL to your app's OAuth allow list.\", display_name='Redirect URI', name='oauth_redirect_uri', required=False, type='string', default='https://backend.composio.dev/api/v1/auth-apps/add', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Scopes to request from the user, comma separated', display_name='Scopes', name='scopes', required=False, type='string', default='https://www.googleapis.com/auth/spreadsheets,https://www.googleapis.com/auth/drive,https://www.googleapis.com/auth/userinfo.email', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Access token injected automatically after OAuth2 authentication flow.', display_name='Access Token', name='bearer_token', required=False, type='string', default=None, legacy_template_name='access_token')], required=[AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client id of the app', display_name='Client id', name='client_id', required=True, type='string', default=None, legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client secret of the app', display_name='Client secret', name='client_secret', required=True, type='string', default=None, legacy_template_name=None)]), connected_account_initiation=AuthConfigDetailFieldsConnectedAccountInitiation(optional=[], required=[])), mode='OAUTH2', name='google_sheets_oauth', proxy=None, deprecated_auth_provider_details={'token_url': 'https://oauth2.googleapis.com/token', 'authorization_url': 'https://accounts.google.com/o/oauth2/v2/auth'})], base_url='https://sheets.googleapis.com/v4', composio_managed_auth_schemes=['OAUTH2'], get_current_user_endpoint='https://www.googleapis.com/oauth2/v3/userinfo')", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 269, "timestamp": "2025-09-20T20:02:50.064834Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Parsed basic toolkit info: slug='googlesheets' name='Googlesheets' description='Google Sheets is a cloud-based spreadsheet tool enabling real-time collaboration, data analysis, and integration with other Google Workspace apps' logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg' tags=[] auth_schemes=['OAUTH2'] categories=['Productivity & Project Management'] auth_config_details=[] connected_account_initiation_fields=None base_url='https://sheets.googleapis.com/v4'", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 296, "timestamp": "2025-09-20T20:02:50.065232Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Successfully fetched detailed info for googlesheets", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 396, "timestamp": "2025-09-20T20:02:50.065410Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
{"event": "Initiation fields: {'required': [], 'optional': []}", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 397, "timestamp": "2025-09-20T20:02:50.065502Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/googlesheets/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "bc2f6e2f-70b7-441b-a71e-a9b73fb4d185", "query_params": ""}
INFO:     127.0.0.1:44142 - "GET /api/composio/toolkits/googlesheets/details HTTP/1.1" 200 OK
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:02:52.202822Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:02:52.203712Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:02:52.203930Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:02:52.204210Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Generated integration user_id: f5a070ad-c686-40ad-99b5-ee0b45c34658 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:02:52.205369Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Starting Composio integration for toolkit: googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:02:52.205591Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:02:52.205755Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:02:52.206093Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:02:52.599385Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:02:52.600002Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Creating auth config for toolkit: googlesheets", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:02:52.600246Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:02:52.600464Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:02:52.600724Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Successfully created auth config: ac_6mnYaS-TmwKF", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:02:52.805716Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_6mnYaS-TmwKF", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:02:52.806203Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_6mnYaS-TmwKF, user: f5a070ad-c686-40ad-99b5-ee0b45c34658", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:02:52.806487Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:02:52.806706Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:02:52.806902Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Successfully created connected account: ca_2f3-6SDgmdym", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:02:53.501486Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_2f3-6SDgmdym", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:02:53.501831Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_6mnYaS-TmwKF']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:02:53.502066Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Using MCP server name: googlesheets-wbul0yrs", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:02:53.502319Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Successfully created MCP server: 48d9b138-92f9-4602-a876-16c66a81670f", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:02:53.941788Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Step 4 complete: Created MCP server 48d9b138-92f9-4602-a876-16c66a81670f", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:02:53.942083Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Generating MCP URL for server: 48d9b138-92f9-4602-a876-16c66a81670f", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:02:53.942241Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:02:54.112432Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:02:54.112833Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: googlesheets", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:02:54.113076Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id=f5a070ad-c686-40ad-99b5-ee0b45c34658", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:02:54.113224Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acb172200>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/FdOwnUOI'", "user_id": "'f5a070ad-c686-40ad-99b5-ee0b45c34658'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/FdOwnUOI',\n    'user_id': 'f5a070ad-c686-40ad-99b5-ee0b45c34658',\n    'created_at': '2025-09-20T20:02:54.113343+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:02:54.113343+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwhulS61WC5DrdhMJy42f-Nfnu-AD2NflXkZv_Mhc9TWAsfXShUhkR6xc4kqmvuXPL647AIX'+508", "config_hash": "'d807d4ccf3b1a6abcb7c4c1b895808f757a14de65e5d53514bcc8b304c641e69'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'5e9d265b-6c5f-4c83-9833-e8d4100324b5'", "now": "datetime.datetime(2025, 9, 20, 20, 2, 54, 120958, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acb01aec0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:02:54.649439Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Failed to integrate toolkit googlesheets: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acb1722c0>", "toolkit_slug": "'googlesheets'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'f5a070ad-c686-40ad-99b5-ee0b45c34658'", "profile_name": "'Googlesheets Profile'", "display_name": "'Googlesheets via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='googlesheets',\n    name='Googlesheets',\n    description='Google Sheets is a cloud-based spreadsheet tool enabling real-time collaboration'+65,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg',\n    tags=[],\n    auth_schemes=['OAUTH2'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_6mnYaS-TmwKF',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='googlesheets'\n)", "connected_account": "ConnectedAccount(\n    id='ca_2f3-6SDgmdym',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/FdOwnUOI',\n    redirect_uri='https://backend.composio.dev/api/v3/s/FdOwnUOI',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/FdOwnUOI',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': '69e575e94f4e9ce259024e49e214a621a095c52cf28118dd',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_6mnYaS-TmwKF',\n    user_id='f5a070ad-c686-40ad-99b5-ee0b45c34658',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='48d9b138-92f9-4602-a876-16c66a81670f',\n    name='googlesheets-wbul0yrs',\n    auth_config_ids=['ac_6mnYaS-TmwKF'],\n    allowed_tools=[\n        'GOOGLESHEETS_ADD_SHEET',\n        'GOOGLESHEETS_AGGREGATE_COLUMN_DATA',\n        'GOOGLESHEETS_APPEND_DIMENSION',\n        'GOOGLESHEETS_BATCH_GET',\n        'GOOGLESHEETS_BATCH_UPDATE',\n        'GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER',\n        'GOOGLESHEETS_CLEAR_BASIC_FILTER',\n        'GOOGLESHEETS_CLEAR_VALUES',\n        'GOOGLESHEETS_CREATE_CHART',\n        'GOOGLESHEETS_CREATE_GOOGLE_SHEET1',\n        ... +26\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?transpor'+5,\n    toolkits=['googlesheets'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/48d9'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/48d9'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/48d9'+51\n    ),\n    updated_at='2025-09-20T20:02:54.090Z',\n    created_at='2025-09-20T20:02:54.090Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acb172200>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/FdOwnUOI'", "user_id": "'f5a070ad-c686-40ad-99b5-ee0b45c34658'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/48d9b138-92f9-4602-a876-16c66a81670f?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/FdOwnUOI',\n    'user_id': 'f5a070ad-c686-40ad-99b5-ee0b45c34658',\n    'created_at': '2025-09-20T20:02:54.113343+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:02:54.113343+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwhulS61WC5DrdhMJy42f-Nfnu-AD2NflXkZv_Mhc9TWAsfXShUhkR6xc4kqmvuXPL647AIX'+508", "config_hash": "'d807d4ccf3b1a6abcb7c4c1b895808f757a14de65e5d53514bcc8b304c641e69'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'5e9d265b-6c5f-4c83-9833-e8d4100324b5'", "now": "datetime.datetime(2025, 9, 20, 20, 2, 54, 120958, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acb01aec0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:02:54.651697Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:02:54.652623Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 2.45s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:02:54.653562Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "c77a932b-49a7-4d18-9232-65a4c3d0b965", "query_params": ""}
INFO:     127.0.0.1:44142 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:02:55.831032Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:02:55.831693Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:02:55.831833Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:02:55.831995Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Generated integration user_id: 5c78330d-5c74-4dfc-9ffb-2c641000a1b8 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:02:55.832146Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Starting Composio integration for toolkit: googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:02:55.832255Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:02:55.832341Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:02:55.832438Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:02:56.254211Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:02:56.255111Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Creating auth config for toolkit: googlesheets", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:02:56.255306Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:02:56.255495Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:02:56.255590Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Successfully created auth config: ac_eObfZ-XfR0Xz", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:02:56.604053Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_eObfZ-XfR0Xz", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:02:56.604389Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_eObfZ-XfR0Xz, user: 5c78330d-5c74-4dfc-9ffb-2c641000a1b8", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:02:56.604536Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:02:56.604659Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:02:56.604781Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Successfully created connected account: ca_pWPZCFlPa4yq", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:02:57.426053Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_pWPZCFlPa4yq", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:02:57.426342Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_eObfZ-XfR0Xz']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:02:57.426533Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Using MCP server name: googlesheets-bdgm62jd", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:02:57.426697Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Successfully created MCP server: bb9e7efb-675b-48db-8ab9-1a920ad69e7d", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:02:57.813397Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Step 4 complete: Created MCP server bb9e7efb-675b-48db-8ab9-1a920ad69e7d", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:02:57.813733Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Generating MCP URL for server: bb9e7efb-675b-48db-8ab9-1a920ad69e7d", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:02:57.813953Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:02:57.954072Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:02:57.954297Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: googlesheets", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:02:57.954415Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id=5c78330d-5c74-4dfc-9ffb-2c641000a1b8", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:02:57.954497Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acadab400>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/2_Iy634s'", "user_id": "'5c78330d-5c74-4dfc-9ffb-2c641000a1b8'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/2_Iy634s',\n    'user_id': '5c78330d-5c74-4dfc-9ffb-2c641000a1b8',\n    'created_at': '2025-09-20T20:02:57.954570+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:02:57.954570+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwhxsV6pSSTHTWGMBZaY7_f0oZRKqjaYU_HbSeLqHDmhyj8eLjvXOjq_Qlme-9GYgapDzK9o'+508", "config_hash": "'dc9f2c714e460f99a0e8b0a48d3f533e85d099a3bd97726554133048460ce50f'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'2856e04a-dbf8-4806-a88a-b342f6acc833'", "now": "datetime.datetime(2025, 9, 20, 20, 2, 57, 954720, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acade7400>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:02:58.250769Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Failed to integrate toolkit googlesheets: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acadab3a0>", "toolkit_slug": "'googlesheets'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'5c78330d-5c74-4dfc-9ffb-2c641000a1b8'", "profile_name": "'Googlesheets Profile'", "display_name": "'Googlesheets via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='googlesheets',\n    name='Googlesheets',\n    description='Google Sheets is a cloud-based spreadsheet tool enabling real-time collaboration'+65,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg',\n    tags=[],\n    auth_schemes=['OAUTH2'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_eObfZ-XfR0Xz',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='googlesheets'\n)", "connected_account": "ConnectedAccount(\n    id='ca_pWPZCFlPa4yq',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/2_Iy634s',\n    redirect_uri='https://backend.composio.dev/api/v3/s/2_Iy634s',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/2_Iy634s',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': 'b0d477307f0c690c40f98429f58ce870e7ceef92770e5937',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_eObfZ-XfR0Xz',\n    user_id='5c78330d-5c74-4dfc-9ffb-2c641000a1b8',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='bb9e7efb-675b-48db-8ab9-1a920ad69e7d',\n    name='googlesheets-bdgm62jd',\n    auth_config_ids=['ac_eObfZ-XfR0Xz'],\n    allowed_tools=[\n        'GOOGLESHEETS_ADD_SHEET',\n        'GOOGLESHEETS_AGGREGATE_COLUMN_DATA',\n        'GOOGLESHEETS_APPEND_DIMENSION',\n        'GOOGLESHEETS_BATCH_GET',\n        'GOOGLESHEETS_BATCH_UPDATE',\n        'GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER',\n        'GOOGLESHEETS_CLEAR_BASIC_FILTER',\n        'GOOGLESHEETS_CLEAR_VALUES',\n        'GOOGLESHEETS_CREATE_CHART',\n        'GOOGLESHEETS_CREATE_GOOGLE_SHEET1',\n        ... +26\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?transpor'+5,\n    toolkits=['googlesheets'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/bb9e'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/bb9e'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/bb9e'+51\n    ),\n    updated_at='2025-09-20T20:02:58.126Z',\n    created_at='2025-09-20T20:02:58.126Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acadab400>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/2_Iy634s'", "user_id": "'5c78330d-5c74-4dfc-9ffb-2c641000a1b8'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/bb9e7efb-675b-48db-8ab9-1a920ad69e7d?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/2_Iy634s',\n    'user_id': '5c78330d-5c74-4dfc-9ffb-2c641000a1b8',\n    'created_at': '2025-09-20T20:02:57.954570+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:02:57.954570+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwhxsV6pSSTHTWGMBZaY7_f0oZRKqjaYU_HbSeLqHDmhyj8eLjvXOjq_Qlme-9GYgapDzK9o'+508", "config_hash": "'dc9f2c714e460f99a0e8b0a48d3f533e85d099a3bd97726554133048460ce50f'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'2856e04a-dbf8-4806-a88a-b342f6acc833'", "now": "datetime.datetime(2025, 9, 20, 20, 2, 57, 954720, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acade7400>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:02:58.253009Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:02:58.253566Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 2.42s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:02:58.253967Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "fede1856-4567-4e1d-8fd6-25944b053a26", "query_params": ""}
INFO:     127.0.0.1:44142 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:03:12.191745Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:03:12.192199Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "95b666c8-9cf6-427e-a176-b576fe426e84", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:03:12.192672Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "95b666c8-9cf6-427e-a176-b576fe426e84", "query_params": ""}
INFO:     127.0.0.1:43968 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:07:07.925317Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:07:07.925742Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "dfe8a77e-ab86-44ef-a27c-c15e6740125c", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:07:07.926443Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "dfe8a77e-ab86-44ef-a27c-c15e6740125c", "query_params": ""}
INFO:     127.0.0.1:45238 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:07:07.928893Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:07:07.929388Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:07:07.930027Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:07:07.930407Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:07:07.930737Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:07:07.931021Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:07:07.939571Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:07:07.940835Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:07:07.941251Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:07:07.941627Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:07:07.951819Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:07:07.952550Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:07:07.952759Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6fadb913-1ce3-4308-885a-0291ff157486", "query_params": ""}
INFO:     127.0.0.1:45238 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:07:10.203142Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:07:10.203893Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:07:10.204076Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:07:10.204310Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Generated integration user_id: b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:07:10.204529Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Starting Composio integration for toolkit: googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:07:10.204694Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:07:10.204880Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:07:10.205082Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:07:10.751098Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:07:10.751640Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Creating auth config for toolkit: googlesheets", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:07:10.751799Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:07:10.751918Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:07:10.752066Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Successfully created auth config: ac_CllxdziofWAp", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:07:10.927224Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_CllxdziofWAp", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:07:10.927443Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_CllxdziofWAp, user: b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:07:10.927817Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:07:10.928065Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:07:10.928179Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Successfully created connected account: ca_iYMi9kEeCb1p", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:07:11.572104Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_iYMi9kEeCb1p", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:07:11.572303Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_CllxdziofWAp']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:07:11.572618Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Using MCP server name: googlesheets-5gwf85kk", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:07:11.572870Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Successfully created MCP server: 2caf1175-0ab4-4256-9ac1-8b86ea9051a1", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:07:12.044360Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Step 4 complete: Created MCP server 2caf1175-0ab4-4256-9ac1-8b86ea9051a1", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:07:12.044530Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Generating MCP URL for server: 2caf1175-0ab4-4256-9ac1-8b86ea9051a1", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:07:12.044731Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:07:12.205465Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:07:12.205627Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: googlesheets", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:07:12.205800Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id=b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:07:12.205931Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acabdeb60>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/nahbmkSS'", "user_id": "'b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/nahbmkSS',\n    'user_id': 'b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e',\n    'created_at': '2025-09-20T20:07:12.206065+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:07:12.206065+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwlwA9oY3TTVZcZ13QgatnQtJYQqBHgdETKreOt3d2fLo-4aiqXDkKprrlYd4ze_-FC5d2ho'+508", "config_hash": "'622a755c45536334ffbb7efc23f797447b7befe36c47adbea0bfb05777d9b407'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'d7944dec-e608-41ff-b04e-4ea95cd9da6a'", "now": "datetime.datetime(2025, 9, 20, 20, 7, 12, 206235, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acafe84c0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:07:12.594565Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Failed to integrate toolkit googlesheets: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acade79a0>", "toolkit_slug": "'googlesheets'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e'", "profile_name": "'Googlesheets Profile'", "display_name": "'Googlesheets via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='googlesheets',\n    name='Googlesheets',\n    description='Google Sheets is a cloud-based spreadsheet tool enabling real-time collaboration'+65,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg',\n    tags=[],\n    auth_schemes=['OAUTH2'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_CllxdziofWAp',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='googlesheets'\n)", "connected_account": "ConnectedAccount(\n    id='ca_iYMi9kEeCb1p',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/nahbmkSS',\n    redirect_uri='https://backend.composio.dev/api/v3/s/nahbmkSS',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/nahbmkSS',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': '137def54e59a22e82c51f83885fa12fc52ff27c574d45480',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_CllxdziofWAp',\n    user_id='b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='2caf1175-0ab4-4256-9ac1-8b86ea9051a1',\n    name='googlesheets-5gwf85kk',\n    auth_config_ids=['ac_CllxdziofWAp'],\n    allowed_tools=[\n        'GOOGLESHEETS_ADD_SHEET',\n        'GOOGLESHEETS_AGGREGATE_COLUMN_DATA',\n        'GOOGLESHEETS_APPEND_DIMENSION',\n        'GOOGLESHEETS_BATCH_GET',\n        'GOOGLESHEETS_BATCH_UPDATE',\n        'GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER',\n        'GOOGLESHEETS_CLEAR_BASIC_FILTER',\n        'GOOGLESHEETS_CLEAR_VALUES',\n        'GOOGLESHEETS_CREATE_CHART',\n        'GOOGLESHEETS_CREATE_GOOGLE_SHEET1',\n        ... +26\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?transpor'+5,\n    toolkits=['googlesheets'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/2caf'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/2caf'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/2caf'+51\n    ),\n    updated_at='2025-09-20T20:07:11.841Z',\n    created_at='2025-09-20T20:07:11.841Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acabdeb60>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/nahbmkSS'", "user_id": "'b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/2caf1175-0ab4-4256-9ac1-8b86ea9051a1?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/nahbmkSS',\n    'user_id': 'b9fe57c1-7c2f-4d5d-aab9-74a694c84d9e',\n    'created_at': '2025-09-20T20:07:12.206065+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:07:12.206065+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwlwA9oY3TTVZcZ13QgatnQtJYQqBHgdETKreOt3d2fLo-4aiqXDkKprrlYd4ze_-FC5d2ho'+508", "config_hash": "'622a755c45536334ffbb7efc23f797447b7befe36c47adbea0bfb05777d9b407'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'d7944dec-e608-41ff-b04e-4ea95cd9da6a'", "now": "datetime.datetime(2025, 9, 20, 20, 7, 12, 206235, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acafe84c0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:07:12.595670Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:07:12.595832Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 2.39s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:07:12.596060Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "a82402de-ad59-44d7-8fc0-2ca9f9204e97", "query_params": ""}
INFO:     127.0.0.1:45256 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:07:13.824556Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:07:13.825293Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:07:13.825456Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:07:13.825679Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Generated integration user_id: e419fcf2-3b40-4200-a570-616ccf1c014d for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:07:13.825967Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Starting Composio integration for toolkit: googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:07:13.826228Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:07:13.826451Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:07:13.826624Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:07:14.281845Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit googlesheets", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:07:14.282376Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Creating auth config for toolkit: googlesheets", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:07:14.282639Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:07:14.282782Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:07:14.282936Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Successfully created auth config: ac_n-w0ytwArsUB", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:07:14.488670Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_n-w0ytwArsUB", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:07:14.488922Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_n-w0ytwArsUB, user: e419fcf2-3b40-4200-a570-616ccf1c014d", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:07:14.489103Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:07:14.489253Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:07:14.489411Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Successfully created connected account: ca_vjXaj0BiMTlI", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:07:15.165913Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_vjXaj0BiMTlI", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:07:15.166226Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_n-w0ytwArsUB']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:07:15.166410Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Using MCP server name: googlesheets-8r6m30s9", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:07:15.166569Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Successfully created MCP server: 01857ccf-bd4d-4f88-b90a-81b1781fb62d", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:07:15.650798Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Step 4 complete: Created MCP server 01857ccf-bd4d-4f88-b90a-81b1781fb62d", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:07:15.651137Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Generating MCP URL for server: 01857ccf-bd4d-4f88-b90a-81b1781fb62d", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:07:15.651426Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:07:15.809170Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:07:15.809658Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: googlesheets", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:07:15.809948Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id=e419fcf2-3b40-4200-a570-616ccf1c014d", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:07:15.810248Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Request started: GET /feature-flags/custom_agents from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:07:15.812006Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "548f9fc3-6df0-42a7-8508-77480bfd69d2", "query_params": ""}
{"event": "Request completed with error: GET /feature-flags/custom_agents | Status: 404 | Time: 0.00s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:07:15.812883Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "548f9fc3-6df0-42a7-8508-77480bfd69d2", "query_params": ""}
INFO:     127.0.0.1:45262 - "GET /feature-flags/custom_agents HTTP/1.1" 404 Not Found
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acafb0520>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/4i9Qdnta'", "user_id": "'e419fcf2-3b40-4200-a570-616ccf1c014d'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/4i9Qdnta',\n    'user_id': 'e419fcf2-3b40-4200-a570-616ccf1c014d',\n    'created_at': '2025-09-20T20:07:15.810601+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:07:15.810601+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwlzXAC1Wwt9RZ-8w74jT-yvjHEBiwzskh2MwirOp43lSSO2H0JgSiPZvTQeohd8h8cLV1JC'+508", "config_hash": "'b91d46aad1fcbc6d31041271a86fdb3eff4d028db7f2f2d16eb37d792a5e947e'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'015b0477-ef4a-46e5-87d0-c684bea40a52'", "now": "datetime.datetime(2025, 9, 20, 20, 7, 15, 810749, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acafbe8c0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:07:16.037982Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Failed to integrate toolkit googlesheets: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acade7a90>", "toolkit_slug": "'googlesheets'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'e419fcf2-3b40-4200-a570-616ccf1c014d'", "profile_name": "'Googlesheets Profile'", "display_name": "'Googlesheets via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='googlesheets',\n    name='Googlesheets',\n    description='Google Sheets is a cloud-based spreadsheet tool enabling real-time collaboration'+65,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg',\n    tags=[],\n    auth_schemes=['OAUTH2'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_n-w0ytwArsUB',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='googlesheets'\n)", "connected_account": "ConnectedAccount(\n    id='ca_vjXaj0BiMTlI',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/4i9Qdnta',\n    redirect_uri='https://backend.composio.dev/api/v3/s/4i9Qdnta',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/4i9Qdnta',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': 'df550c25922455fdb1bae221174082b56ec892f0b4ae7327',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_n-w0ytwArsUB',\n    user_id='e419fcf2-3b40-4200-a570-616ccf1c014d',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='01857ccf-bd4d-4f88-b90a-81b1781fb62d',\n    name='googlesheets-8r6m30s9',\n    auth_config_ids=['ac_n-w0ytwArsUB'],\n    allowed_tools=[\n        'GOOGLESHEETS_ADD_SHEET',\n        'GOOGLESHEETS_AGGREGATE_COLUMN_DATA',\n        'GOOGLESHEETS_APPEND_DIMENSION',\n        'GOOGLESHEETS_BATCH_GET',\n        'GOOGLESHEETS_BATCH_UPDATE',\n        'GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER',\n        'GOOGLESHEETS_CLEAR_BASIC_FILTER',\n        'GOOGLESHEETS_CLEAR_VALUES',\n        'GOOGLESHEETS_CREATE_CHART',\n        'GOOGLESHEETS_CREATE_GOOGLE_SHEET1',\n        ... +26\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?transpor'+5,\n    toolkits=['googlesheets'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/0185'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/0185'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/0185'+51\n    ),\n    updated_at='2025-09-20T20:07:15.496Z',\n    created_at='2025-09-20T20:07:15.496Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acafb0520>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Googlesheets Profile'", "toolkit_slug": "'googlesheets'", "toolkit_name": "'Googlesheets'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/4i9Qdnta'", "user_id": "'e419fcf2-3b40-4200-a570-616ccf1c014d'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'googlesheets',\n    'toolkit_name': 'Googlesheets',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/01857ccf-bd4d-4f88-b90a-81b1781fb62d?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/4i9Qdnta',\n    'user_id': 'e419fcf2-3b40-4200-a570-616ccf1c014d',\n    'created_at': '2025-09-20T20:07:15.810601+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:07:15.810601+00:00\", \"mcp_url\": \"https://apollo.co'+302", "encrypted_config": "'gAAAAABozwlzXAC1Wwt9RZ-8w74jT-yvjHEBiwzskh2MwirOp43lSSO2H0JgSiPZvTQeohd8h8cLV1JC'+508", "config_hash": "'b91d46aad1fcbc6d31041271a86fdb3eff4d028db7f2f2d16eb37d792a5e947e'", "mcp_qualified_name": "'composio.googlesheets'", "profile_id": "'015b0477-ef4a-46e5-87d0-c684bea40a52'", "now": "datetime.datetime(2025, 9, 20, 20, 7, 15, 810749, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Googlesheets Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acafbe8c0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:07:16.039073Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:07:16.039308Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 2.22s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:07:16.039673Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "56a2c5a7-2a8c-4bad-b6be-a51f46f9fbca", "query_params": ""}
INFO:     127.0.0.1:45256 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:14:13.697502Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:13.698028Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "1d91c048-0447-4c8a-a1fc-7a16698339f9", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:14:13.698684Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "1d91c048-0447-4c8a-a1fc-7a16698339f9", "query_params": ""}
INFO:     127.0.0.1:57738 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57738 - "OPTIONS /api/billing/subscription HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:14:13.702359Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:13.702738Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:13.703413Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:13.703706Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:13.704169Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:14:13.704524Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:14:13.714293Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:14:13.715365Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:14:13.715609Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:14:13.715952Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:14:13.724566Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:14:13.725248Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:14:13.725404Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c6758dda-4887-411f-bb3d-585dbbed483f", "query_params": ""}
INFO:     127.0.0.1:57738 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "Request started: GET /feature-flags/custom_agents from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:13.882656Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "26d2e19e-10d7-4bda-840a-91e6db400968", "query_params": ""}
{"event": "Request completed with error: GET /feature-flags/custom_agents | Status: 404 | Time: 0.00s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:14:13.883407Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "26d2e19e-10d7-4bda-840a-91e6db400968", "query_params": ""}
INFO:     127.0.0.1:57760 - "GET /feature-flags/custom_agents HTTP/1.1" 404 Not Found
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:16.522475Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:16.523226Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:16.523649Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:16.523982Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
{"event": "Fetching Composio toolkits with limit: 100, cursor: None, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:14:16.524413Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
{"event": "Fetching toolkits with limit: 100, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:14:16.524775Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:14:17.254857Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "70449191-e6bf-46cc-a360-31874a0d5ab3", "query_params": ""}
INFO:     127.0.0.1:57760 - "GET /api/composio/toolkits HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:14:22.121155Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:22.121545Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "75eb68df-bceb-4450-aab9-c810f0355f3c", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:14:22.122283Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "75eb68df-bceb-4450-aab9-c810f0355f3c", "query_params": ""}
INFO:     127.0.0.1:57750 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57750 - "OPTIONS /api/billing/available-models HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:14:22.671640Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:22.672036Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:22.672529Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:22.672962Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:22.673164Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:14:22.673375Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:14:22.678907Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/available-models", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:14:22.679536Z"}
{"event": "Request started: GET /api/billing/available-models from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:22.679812Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "30424563-de4d-4033-97f9-0d38c31f0f04", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:22.680248Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "30424563-de4d-4033-97f9-0d38c31f0f04", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:22.680376Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "30424563-de4d-4033-97f9-0d38c31f0f04", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:22.680541Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "30424563-de4d-4033-97f9-0d38c31f0f04", "query_params": ""}
{"event": "Running in local development mode - billing checks are disabled", "level": "info", "filename": "billing.py", "func_name": "get_available_models", "lineno": 1517, "timestamp": "2025-09-20T20:14:22.680678Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "30424563-de4d-4033-97f9-0d38c31f0f04", "query_params": ""}
INFO:     127.0.0.1:57750 - "GET /api/billing/available-models HTTP/1.1" 200 OK
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:14:22.681266Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:14:22.681391Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:14:22.681508Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:14:22.686988Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:14:22.687899Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:14:22.688043Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "afd6df96-4d64-428c-afe3-e9d72dbc0da1", "query_params": ""}
INFO:     127.0.0.1:57776 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:26.306125Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:26.306722Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:26.306961Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:26.307201Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
{"event": "Fetching Composio toolkits with limit: 100, cursor: None, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:14:26.307509Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
{"event": "Fetching toolkits with limit: 100, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:14:26.307654Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:14:26.769894Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3db68cd6-a520-4432-a2a5-5bb9548e73eb", "query_params": ""}
INFO:     127.0.0.1:41856 - "GET /api/composio/toolkits HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits/gmail/details from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:29.875605Z", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:29.876086Z", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:29.876256Z", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:29.876440Z", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Fetching detailed toolkit info for: gmail", "level": "info", "filename": "api.py", "func_name": "get_toolkit_details", "lineno": 142, "timestamp": "2025-09-20T20:14:29.876653Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Fetching detailed toolkit info for: gmail", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 259, "timestamp": "2025-09-20T20:14:29.876796Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Raw toolkit response for gmail: ToolkitRetrieveResponse(deprecated=Deprecated(raw_proxy_info_by_auth_schemes=[{'auth_method': 'OAUTH2', 'proxy': {}}, {'auth_method': 'BEARER_TOKEN', 'proxy': {'headers': {'Authorization': 'Bearer {{bearer_token}}'}}}], toolkit_id='a90e7d79-4f7a-4ff2-bd7d-19c78640b8f8', get_current_user_endpoint='https://www.googleapis.com/gmail/v1/users/me/profile', toolkitId='a90e7d79-4f7a-4ff2-bd7d-19c78640b8f8', getCurrentUserEndpoint='https://www.googleapis.com/gmail/v1/users/me/profile', rawProxyInfoByAuthSchemes=[{'auth_method': 'OAUTH2', 'proxy': {}}, {'auth_method': 'BEARER_TOKEN', 'proxy': {'headers': {'Authorization': 'Bearer {{bearer_token}}'}}}]), enabled=True, is_local_toolkit=False, meta=Meta(categories=[MetaCategory(name='Collaboration & Communication', slug='collaboration-&-communication')], created_at='Fri May 03 2024', description='Gmail is Google\u2019s email service, featuring spam protection, search functions, and seamless integration with other G Suite apps for productivity', logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg', tools_count=24.0, triggers_count=1.0, updated_at='Thu Jul 31 2025', app_url='https://mail.google.com'), name='Gmail', slug='gmail', auth_config_details=[AuthConfigDetail(fields=AuthConfigDetailFields(auth_config_creation=AuthConfigDetailFieldsAuthConfigCreation(optional=[AuthConfigDetailFieldsAuthConfigCreationOptional(description=\"Add this Redirect URL to your app's OAuth allow list.\", display_name='Redirect URI', name='oauth_redirect_uri', required=False, type='string', default='https://backend.composio.dev/api/v1/auth-apps/add', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Scopes to request from the user, comma separated', display_name='Scopes', name='scopes', required=False, type='string', default='https://www.googleapis.com/auth/gmail.modify,https://www.googleapis.com/auth/userinfo.profile,https://www.googleapis.com/auth/userinfo.email,https://www.googleapis.com/auth/contacts.readonly,https://www.googleapis.com/auth/contacts.other.readonly,https://www.googleapis.com/auth/profile.language.read,https://www.googleapis.com/auth/user.addresses.read,https://www.googleapis.com/auth/user.birthday.read,https://www.googleapis.com/auth/user.emails.read,https://www.googleapis.com/auth/user.phonenumbers.read,https://www.googleapis.com/auth/profile.emails.read', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Access token injected automatically after OAuth2 authentication flow.', display_name='Access Token', name='bearer_token', required=False, type='string', default=None, legacy_template_name='access_token')], required=[AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client id of the app', display_name='Client id', name='client_id', required=True, type='string', default=None, legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client secret of the app', display_name='Client secret', name='client_secret', required=True, type='string', default=None, legacy_template_name=None)]), connected_account_initiation=AuthConfigDetailFieldsConnectedAccountInitiation(optional=[], required=[])), mode='OAUTH2', name='gmail_oauth', proxy=None, deprecated_auth_provider_details={'token_url': 'https://oauth2.googleapis.com/token', 'authorization_url': 'https://accounts.google.com/o/oauth2/v2/auth'}), AuthConfigDetail(fields=AuthConfigDetailFields(auth_config_creation=AuthConfigDetailFieldsAuthConfigCreation(optional=[], required=[]), connected_account_initiation=AuthConfigDetailFieldsConnectedAccountInitiation(optional=[], required=[AuthConfigDetailFieldsConnectedAccountInitiationRequired(description='Token for bearer token auth', display_name='Token', name='token', required=True, type='string', default=None, legacy_template_name=None), AuthConfigDetailFieldsConnectedAccountInitiationRequired(description='Your Gmail API bearer token', display_name='Bearer Token', name='bearer_token', required=True, type='string', default=None, legacy_template_name='bearer_token')])), mode='BEARER_TOKEN', name='gmail_bearer', proxy=None)], base_url='https://www.googleapis.com', composio_managed_auth_schemes=['OAUTH2'], get_current_user_endpoint='https://www.googleapis.com/gmail/v1/users/me/profile')", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 269, "timestamp": "2025-09-20T20:14:30.041192Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Parsed basic toolkit info: slug='gmail' name='Gmail' description='Gmail is Google\u2019s email service, featuring spam protection, search functions, and seamless integration with other G Suite apps for productivity' logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg' tags=[] auth_schemes=['OAUTH2'] categories=['Collaboration & Communication'] auth_config_details=[] connected_account_initiation_fields=None base_url='https://www.googleapis.com'", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 296, "timestamp": "2025-09-20T20:14:30.041477Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Successfully fetched detailed info for gmail", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 396, "timestamp": "2025-09-20T20:14:30.041709Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
{"event": "Initiation fields: {'required': [], 'optional': []}", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 397, "timestamp": "2025-09-20T20:14:30.041862Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/gmail/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "57ceccc5-012b-4a6f-bd46-a33e2c94d1d1", "query_params": ""}
INFO:     127.0.0.1:41856 - "GET /api/composio/toolkits/gmail/details HTTP/1.1" 200 OK
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:31.467618Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:31.468409Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:31.468557Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:31.468735Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Generated integration user_id: 4ba5d516-f416-4f06-837d-1af7f8c65028 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:14:31.468936Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Starting Composio integration for toolkit: gmail", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:14:31.469268Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:14:31.469599Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:14:31.469810Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:14:31.870272Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit gmail", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:14:31.870916Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Creating auth config for toolkit: gmail", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:14:31.871205Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:14:31.871554Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:14:31.871788Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Successfully created auth config: ac_vmDw2AoyUlmk", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:14:32.070131Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_vmDw2AoyUlmk", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:14:32.070330Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_vmDw2AoyUlmk, user: 4ba5d516-f416-4f06-837d-1af7f8c65028", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:14:32.070490Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:14:32.070780Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:14:32.070953Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Successfully created connected account: ca_v0xiuW2zdfXu", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:14:32.718481Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_v0xiuW2zdfXu", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:14:32.718681Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_vmDw2AoyUlmk']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:14:32.718811Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Using MCP server name: gmail-htulhchc", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:14:32.718989Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Successfully created MCP server: a0539a58-10f0-4329-ad07-e47f21da3dc3", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:14:33.279232Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Step 4 complete: Created MCP server a0539a58-10f0-4329-ad07-e47f21da3dc3", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:14:33.279437Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Generating MCP URL for server: a0539a58-10f0-4329-ad07-e47f21da3dc3", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:14:33.279677Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:14:33.425549Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:14:33.425777Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: gmail", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:14:33.425908Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id=4ba5d516-f416-4f06-837d-1af7f8c65028", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:14:33.426042Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acabdfcd0>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Gmail Profile'", "toolkit_slug": "'gmail'", "toolkit_name": "'Gmail'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/EEUE21tF'", "user_id": "'4ba5d516-f416-4f06-837d-1af7f8c65028'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'gmail',\n    'toolkit_name': 'Gmail',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/EEUE21tF',\n    'user_id': '4ba5d516-f416-4f06-837d-1af7f8c65028',\n    'created_at': '2025-09-20T20:14:33.426153+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:14:33.426153+00:00\", \"mcp_url\": \"https://apollo.co'+288", "encrypted_config": "'gAAAAABozwspeYtRvtBkfdX0mwpatCyBwGb5U2MqlhnMAwwA49qW3d0l7sTXGdYzyn8yuaNTCdr_HQ0i'+508", "config_hash": "'8376cfd8359f3455f7614f2ed21847571044ccb7d439ed8396f458e50121628b'", "mcp_qualified_name": "'composio.gmail'", "profile_id": "'dcaa941d-209f-4daf-adf0-7b1956ca4aea'", "now": "datetime.datetime(2025, 9, 20, 20, 14, 33, 426520, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Gmail Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acac56200>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:14:33.905591Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Failed to integrate toolkit gmail: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acabdfd30>", "toolkit_slug": "'gmail'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'4ba5d516-f416-4f06-837d-1af7f8c65028'", "profile_name": "'Gmail Profile'", "display_name": "'Gmail via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='gmail',\n    name='Gmail',\n    description='Gmail is Google\u2019s email service, featuring spam protection, search functions, an'+63,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg',\n    tags=[],\n    auth_schemes=['OAUTH2', 'BEARER_TOKEN'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_vmDw2AoyUlmk',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='gmail'\n)", "connected_account": "ConnectedAccount(\n    id='ca_v0xiuW2zdfXu',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/EEUE21tF',\n    redirect_uri='https://backend.composio.dev/api/v3/s/EEUE21tF',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/EEUE21tF',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': '042fa00546f7259692a795f03e5bfb892ff1549fe2c287a2',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_vmDw2AoyUlmk',\n    user_id='4ba5d516-f416-4f06-837d-1af7f8c65028',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='a0539a58-10f0-4329-ad07-e47f21da3dc3',\n    name='gmail-htulhchc',\n    auth_config_ids=['ac_vmDw2AoyUlmk'],\n    allowed_tools=[\n        'GMAIL_ADD_LABEL_TO_EMAIL',\n        'GMAIL_CREATE_EMAIL_DRAFT',\n        'GMAIL_CREATE_LABEL',\n        'GMAIL_DELETE_DRAFT',\n        'GMAIL_DELETE_MESSAGE',\n        'GMAIL_FETCH_EMAILS',\n        'GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID',\n        'GMAIL_FETCH_MESSAGE_BY_THREAD_ID',\n        'GMAIL_FORWARD_MESSAGE',\n        'GMAIL_GET_ATTACHMENT',\n        ... +14\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?transpor'+5,\n    toolkits=['gmail'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/a053'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/a053'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/a053'+51\n    ),\n    updated_at='2025-09-20T20:14:33.499Z',\n    created_at='2025-09-20T20:14:33.499Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acabdfcd0>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Gmail Profile'", "toolkit_slug": "'gmail'", "toolkit_name": "'Gmail'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/EEUE21tF'", "user_id": "'4ba5d516-f416-4f06-837d-1af7f8c65028'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'gmail',\n    'toolkit_name': 'Gmail',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/a0539a58-10f0-4329-ad07-e47f21da3dc3?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/EEUE21tF',\n    'user_id': '4ba5d516-f416-4f06-837d-1af7f8c65028',\n    'created_at': '2025-09-20T20:14:33.426153+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:14:33.426153+00:00\", \"mcp_url\": \"https://apollo.co'+288", "encrypted_config": "'gAAAAABozwspeYtRvtBkfdX0mwpatCyBwGb5U2MqlhnMAwwA49qW3d0l7sTXGdYzyn8yuaNTCdr_HQ0i'+508", "config_hash": "'8376cfd8359f3455f7614f2ed21847571044ccb7d439ed8396f458e50121628b'", "mcp_qualified_name": "'composio.gmail'", "profile_id": "'dcaa941d-209f-4daf-adf0-7b1956ca4aea'", "now": "datetime.datetime(2025, 9, 20, 20, 14, 33, 426520, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Gmail Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acac56200>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:14:33.906730Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:14:33.907016Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 2.44s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:14:33.907476Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "0b00bfd5-60e4-4953-9148-8b93c374781b", "query_params": ""}
INFO:     127.0.0.1:41856 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:14:35.075282Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:14:35.075961Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:14:35.076138Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:14:35.076338Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Generated integration user_id: d177f0ec-7c34-47c6-b3fe-f94202e2d5c9 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:14:35.076532Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Starting Composio integration for toolkit: gmail", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:14:35.076682Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:14:35.076790Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:14:35.076893Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:14:35.521612Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit gmail", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:14:35.522394Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Creating auth config for toolkit: gmail", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:14:35.522719Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:14:35.523072Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:14:35.523267Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Successfully created auth config: ac_X4pntity_2vH", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:14:35.722271Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_X4pntity_2vH", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:14:35.722566Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_X4pntity_2vH, user: d177f0ec-7c34-47c6-b3fe-f94202e2d5c9", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:14:35.722689Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:14:35.722841Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:14:35.722983Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Successfully created connected account: ca_sMFI7pF0iwhC", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:14:36.448416Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_sMFI7pF0iwhC", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:14:36.448692Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_X4pntity_2vH']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:14:36.448838Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Using MCP server name: gmail-gvq5hvo4", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:14:36.448969Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Successfully created MCP server: 9fe876ad-b4ed-4197-ad59-3a45af48e732", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:14:36.815210Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Step 4 complete: Created MCP server 9fe876ad-b4ed-4197-ad59-3a45af48e732", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:14:36.815657Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Generating MCP URL for server: 9fe876ad-b4ed-4197-ad59-3a45af48e732", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:14:36.816338Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:14:36.969133Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:14:36.969665Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: gmail", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:14:36.969980Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id=d177f0ec-7c34-47c6-b3fe-f94202e2d5c9", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:14:36.970319Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acac54ee0>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Gmail Profile'", "toolkit_slug": "'gmail'", "toolkit_name": "'Gmail'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/QYMjTxrR'", "user_id": "'d177f0ec-7c34-47c6-b3fe-f94202e2d5c9'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'gmail',\n    'toolkit_name': 'Gmail',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/QYMjTxrR',\n    'user_id': 'd177f0ec-7c34-47c6-b3fe-f94202e2d5c9',\n    'created_at': '2025-09-20T20:14:36.970693+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:14:36.970693+00:00\", \"mcp_url\": \"https://apollo.co'+288", "encrypted_config": "'gAAAAABozwssWkGK55BAoGLMb2YAqGwhYzYcGCc7J7U1IaDCGWiw6FlAOWrqBWgFMlg227ciUIqkriUg'+508", "config_hash": "'7613aa08e6e61b067a9f0436de84a84cdc4edc94c5c3da09bfc240db702f10b5'", "mcp_qualified_name": "'composio.gmail'", "profile_id": "'4d1a59b6-48cf-428b-b64e-c0a9429f6864'", "now": "datetime.datetime(2025, 9, 20, 20, 14, 36, 970871, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Gmail Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acaf71360>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:14:37.244677Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Failed to integrate toolkit gmail: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acac54c40>", "toolkit_slug": "'gmail'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'d177f0ec-7c34-47c6-b3fe-f94202e2d5c9'", "profile_name": "'Gmail Profile'", "display_name": "'Gmail via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='gmail',\n    name='Gmail',\n    description='Gmail is Google\u2019s email service, featuring spam protection, search functions, an'+63,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg',\n    tags=[],\n    auth_schemes=['OAUTH2', 'BEARER_TOKEN'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_X4pntity_2vH',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='gmail'\n)", "connected_account": "ConnectedAccount(\n    id='ca_sMFI7pF0iwhC',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/QYMjTxrR',\n    redirect_uri='https://backend.composio.dev/api/v3/s/QYMjTxrR',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/QYMjTxrR',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': 'd040176825c5e8d0bc287ce1214c0e8eaef221d5ce4ab4e9',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_X4pntity_2vH',\n    user_id='d177f0ec-7c34-47c6-b3fe-f94202e2d5c9',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='9fe876ad-b4ed-4197-ad59-3a45af48e732',\n    name='gmail-gvq5hvo4',\n    auth_config_ids=['ac_X4pntity_2vH'],\n    allowed_tools=[\n        'GMAIL_ADD_LABEL_TO_EMAIL',\n        'GMAIL_CREATE_EMAIL_DRAFT',\n        'GMAIL_CREATE_LABEL',\n        'GMAIL_DELETE_DRAFT',\n        'GMAIL_DELETE_MESSAGE',\n        'GMAIL_FETCH_EMAILS',\n        'GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID',\n        'GMAIL_FETCH_MESSAGE_BY_THREAD_ID',\n        'GMAIL_FORWARD_MESSAGE',\n        'GMAIL_GET_ATTACHMENT',\n        ... +14\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?transpor'+5,\n    toolkits=['gmail'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/9fe8'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/9fe8'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/9fe8'+51\n    ),\n    updated_at='2025-09-20T20:14:37.251Z',\n    created_at='2025-09-20T20:14:37.251Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acac54ee0>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'Gmail Profile'", "toolkit_slug": "'gmail'", "toolkit_name": "'Gmail'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/QYMjTxrR'", "user_id": "'d177f0ec-7c34-47c6-b3fe-f94202e2d5c9'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'gmail',\n    'toolkit_name': 'Gmail',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/9fe876ad-b4ed-4197-ad59-3a45af48e732?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/QYMjTxrR',\n    'user_id': 'd177f0ec-7c34-47c6-b3fe-f94202e2d5c9',\n    'created_at': '2025-09-20T20:14:36.970693+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:14:36.970693+00:00\", \"mcp_url\": \"https://apollo.co'+288", "encrypted_config": "'gAAAAABozwssWkGK55BAoGLMb2YAqGwhYzYcGCc7J7U1IaDCGWiw6FlAOWrqBWgFMlg227ciUIqkriUg'+508", "config_hash": "'7613aa08e6e61b067a9f0436de84a84cdc4edc94c5c3da09bfc240db702f10b5'", "mcp_qualified_name": "'composio.gmail'", "profile_id": "'4d1a59b6-48cf-428b-b64e-c0a9429f6864'", "now": "datetime.datetime(2025, 9, 20, 20, 14, 36, 970871, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'Gmail Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acaf71360>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:14:37.246480Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:14:37.246690Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 2.17s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:14:37.246937Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "8e349966-374c-4f58-ad7f-f9850821e559", "query_params": ""}
INFO:     127.0.0.1:41856 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:15:39.789914Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:15:39.790271Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3c7952d0-8331-4340-9033-ca13992a665a", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:15:39.790716Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3c7952d0-8331-4340-9033-ca13992a665a", "query_params": ""}
INFO:     127.0.0.1:48786 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:16:54.877516Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:16:54.877930Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3d4b05f5-895e-4fd6-a338-309607f0fcfb", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:16:54.878496Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3d4b05f5-895e-4fd6-a338-309607f0fcfb", "query_params": ""}
INFO:     127.0.0.1:38284 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:16:54.879839Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:16:54.880111Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:16:54.880491Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:16:54.880661Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:16:54.880937Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:16:54.881133Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:16:54.895257Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:16:54.896403Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:16:54.896722Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:16:54.897217Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:16:54.908185Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:16:54.908869Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:16:54.909078Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "be99131b-25da-4bf9-8975-4a3f45bdabe7", "query_params": ""}
INFO:     127.0.0.1:38284 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:24:32.447186Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:32.447756Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b7131fd5-a12f-47c2-9fa5-0aecc0506b85", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:24:32.448615Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "b7131fd5-a12f-47c2-9fa5-0aecc0506b85", "query_params": ""}
INFO:     127.0.0.1:55328 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:55328 - "OPTIONS /api/billing/subscription HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:24:32.451664Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:32.452064Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:32.452648Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:32.452885Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:32.453244Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:24:32.453595Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:24:32.462619Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:24:32.463579Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:24:32.463916Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:24:32.464258Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:24:32.473490Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:24:32.474301Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:24:32.474586Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c68c6ab6-84bc-4ae1-ac11-51ed3f490f90", "query_params": ""}
INFO:     127.0.0.1:55328 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "Request started: GET /feature-flags/custom_agents from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:32.635334Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "7d867931-ddad-4edb-9989-33d720afc9eb", "query_params": ""}
{"event": "Request completed with error: GET /feature-flags/custom_agents | Status: 404 | Time: 0.00s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:24:32.636420Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "7d867931-ddad-4edb-9989-33d720afc9eb", "query_params": ""}
INFO:     127.0.0.1:55342 - "GET /feature-flags/custom_agents HTTP/1.1" 404 Not Found
{"event": "Request started: GET /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:35.964799Z", "path": "/api/composio/profiles", "method": "GET", "client_ip": "127.0.0.1", "request_id": "8a1cfa77-8f17-413e-85a1-e510fd2fc789", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:35.965486Z", "path": "/api/composio/profiles", "method": "GET", "client_ip": "127.0.0.1", "request_id": "8a1cfa77-8f17-413e-85a1-e510fd2fc789", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:35.965926Z", "path": "/api/composio/profiles", "method": "GET", "client_ip": "127.0.0.1", "request_id": "8a1cfa77-8f17-413e-85a1-e510fd2fc789", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:35.966368Z", "path": "/api/composio/profiles", "method": "GET", "client_ip": "127.0.0.1", "request_id": "8a1cfa77-8f17-413e-85a1-e510fd2fc789", "query_params": ""}
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:36.057886Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:36.058428Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:36.058666Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:36.058949Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
{"event": "Fetching Composio toolkits with limit: 100, cursor: None, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:24:36.059299Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
{"event": "Fetching toolkits with limit: 100, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:24:36.059531Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:24:36.894567Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22b36593-f9f4-4d2f-a2f4-581c5a2eaa54", "query_params": ""}
INFO:     127.0.0.1:55358 - "GET /api/composio/toolkits HTTP/1.1" 200 OK
INFO:     127.0.0.1:55342 - "GET /api/composio/profiles HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: cursor=Mi0xMDA%3D", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:40.335496Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:40.336287Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:40.336484Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:40.336703Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
{"event": "Fetching Composio toolkits with limit: 100, cursor: Mi0xMDA=, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:24:40.337015Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
{"event": "Fetching toolkits with limit: 100, cursor: Mi0xMDA=, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:24:40.337195Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:24:40.720186Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "610bd824-c9c0-4c51-badc-034daa635db2", "query_params": "cursor=Mi0xMDA%3D"}
INFO:     127.0.0.1:55342 - "GET /api/composio/toolkits?cursor=Mi0xMDA%3D HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: cursor=My0xMDA%3D", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:47.707264Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:47.707743Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:47.707898Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:47.708090Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
{"event": "Fetching Composio toolkits with limit: 100, cursor: My0xMDA=, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:24:47.708324Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
{"event": "Fetching toolkits with limit: 100, cursor: My0xMDA=, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:24:47.708594Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:24:48.191737Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "d1d4297b-7b4c-4b89-9316-001bca3b11cf", "query_params": "cursor=My0xMDA%3D"}
INFO:     127.0.0.1:44236 - "GET /api/composio/toolkits?cursor=My0xMDA%3D HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits/github/details from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:57.928760Z", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:57.929268Z", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:57.929406Z", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:57.929898Z", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Fetching detailed toolkit info for: github", "level": "info", "filename": "api.py", "func_name": "get_toolkit_details", "lineno": 142, "timestamp": "2025-09-20T20:24:57.930218Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Fetching detailed toolkit info for: github", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 259, "timestamp": "2025-09-20T20:24:57.930449Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Raw toolkit response for github: ToolkitRetrieveResponse(deprecated=Deprecated(raw_proxy_info_by_auth_schemes=[{'auth_method': 'OAUTH2', 'proxy': {}}, {'auth_method': 'SERVICE_ACCOUNT', 'proxy': {}}], toolkit_id='01e22f33-dc3f-46ae-b58d-050e4d2d1909', get_current_user_endpoint='https://api.github.com/user', toolkitId='01e22f33-dc3f-46ae-b58d-050e4d2d1909', getCurrentUserEndpoint='https://api.github.com/user', rawProxyInfoByAuthSchemes=[{'auth_method': 'OAUTH2', 'proxy': {}}, {'auth_method': 'SERVICE_ACCOUNT', 'proxy': {}}]), enabled=True, is_local_toolkit=False, meta=Meta(categories=[MetaCategory(name='Developer Tools & DevOps', slug='developer-tools-&-devops')], created_at='Fri May 03 2024', description='GitHub is a code hosting platform for version control and collaboration, offering Git-based repository management, issue tracking, and continuous integration features', logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png', tools_count=825.0, triggers_count=6.0, updated_at='Thu Jul 31 2025', app_url='https://github.com/'), name='GitHub', slug='github', auth_config_details=[AuthConfigDetail(fields=AuthConfigDetailFields(auth_config_creation=AuthConfigDetailFieldsAuthConfigCreation(optional=[AuthConfigDetailFieldsAuthConfigCreationOptional(description=\"Add this Redirect URL to your app's OAuth allow list.\", display_name='Redirect URI', name='oauth_redirect_uri', required=False, type='string', default='https://backend.composio.dev/api/v1/auth-apps/add', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Scopes to request from the user, comma separated', display_name='Scopes', name='scopes', required=False, type='string', default='repo,user,gist,notifications,project,workflow,codespace', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Access token injected automatically after OAuth2 authentication flow.', display_name='Access Token', name='bearer_token', required=False, type='string', default=None, legacy_template_name='access_token')], required=[AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client id of the app', display_name='Client id', name='client_id', required=True, type='string', default=None, legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client secret of the app', display_name='Client secret', name='client_secret', required=True, type='string', default=None, legacy_template_name=None)]), connected_account_initiation=AuthConfigDetailFieldsConnectedAccountInitiation(optional=[], required=[])), mode='OAUTH2', name='github_oauth', proxy=None, deprecated_auth_provider_details={'token_url': 'https://github.com/login/oauth/access_token', 'authorization_url': 'https://github.com/login/oauth/authorize'})], base_url='https://api.github.com', composio_managed_auth_schemes=['OAUTH2'], get_current_user_endpoint='https://api.github.com/user')", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 269, "timestamp": "2025-09-20T20:24:58.172306Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Parsed basic toolkit info: slug='github' name='GitHub' description='GitHub is a code hosting platform for version control and collaboration, offering Git-based repository management, issue tracking, and continuous integration features' logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png' tags=[] auth_schemes=['OAUTH2'] categories=['Developer Tools & DevOps'] auth_config_details=[] connected_account_initiation_fields=None base_url='https://api.github.com'", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 296, "timestamp": "2025-09-20T20:24:58.172634Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Successfully fetched detailed info for github", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 396, "timestamp": "2025-09-20T20:24:58.172965Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
{"event": "Initiation fields: {'required': [], 'optional': []}", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 397, "timestamp": "2025-09-20T20:24:58.173094Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/github/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "22a7b535-3287-4cbb-821a-8dbf93c40aa1", "query_params": ""}
INFO:     127.0.0.1:39256 - "GET /api/composio/toolkits/github/details HTTP/1.1" 200 OK
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:24:58.890708Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:24:58.891435Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:24:58.891842Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:24:58.892150Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Generated integration user_id: 13807c9d-d266-4293-a6b7-8c1b97f698f8 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:24:58.892569Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Starting Composio integration for toolkit: github", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:24:58.892776Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:24:58.892965Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:24:58.893159Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:24:59.265139Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit github", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:24:59.265743Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Creating auth config for toolkit: github", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:24:59.266066Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:24:59.266379Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:24:59.266650Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Successfully created auth config: ac_Qyy5vdDeIplQ", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:24:59.459327Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_Qyy5vdDeIplQ", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:24:59.459556Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_Qyy5vdDeIplQ, user: 13807c9d-d266-4293-a6b7-8c1b97f698f8", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:24:59.459990Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:24:59.460215Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:24:59.460483Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Successfully created connected account: ca_2lSG5qoh_GH9", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:25:00.106510Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_2lSG5qoh_GH9", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:25:00.106784Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_Qyy5vdDeIplQ']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:25:00.106943Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Using MCP server name: github-ea7zeugt", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:25:00.107119Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Successfully created MCP server: 1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:25:01.485341Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Step 4 complete: Created MCP server 1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:25:01.485553Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Generating MCP URL for server: 1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:25:01.485724Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:25:01.638075Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:25:01.638366Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: github", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:25:01.638546Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id=13807c9d-d266-4293-a6b7-8c1b97f698f8", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:25:01.638687Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acac20190>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'GitHub Profile'", "toolkit_slug": "'github'", "toolkit_name": "'GitHub'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/qvD2hVck'", "user_id": "'13807c9d-d266-4293-a6b7-8c1b97f698f8'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'github',\n    'toolkit_name': 'GitHub',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/qvD2hVck',\n    'user_id': '13807c9d-d266-4293-a6b7-8c1b97f698f8',\n    'created_at': '2025-09-20T20:25:01.638785+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:25:01.638785+00:00\", \"mcp_url\": \"https://apollo.co'+290", "encrypted_config": "'gAAAAABozw2dWBfULWJsCNLPp-NbAdoiPB8wAK-pnfpnGZob1C9dVS6trpijph_7XQ6YcbgcRKG09FN5'+508", "config_hash": "'bf3ed877d9aef9c3a3019ac666f5385bb9a9cf41ff7f2d6d169a1860472f82aa'", "mcp_qualified_name": "'composio.github'", "profile_id": "'562d0bbb-38f6-42fc-8424-f8fafb89fec5'", "now": "datetime.datetime(2025, 9, 20, 20, 25, 1, 639109, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'GitHub Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acac54be0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:25:02.113598Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Failed to integrate toolkit github: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acac20130>", "toolkit_slug": "'github'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'13807c9d-d266-4293-a6b7-8c1b97f698f8'", "profile_name": "'GitHub Profile'", "display_name": "'GitHub via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='github',\n    name='GitHub',\n    description='GitHub is a code hosting platform for version control and collaboration, offerin'+86,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png',\n    tags=[],\n    auth_schemes=['OAUTH2', 'SERVICE_ACCOUNT'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_Qyy5vdDeIplQ',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='github'\n)", "connected_account": "ConnectedAccount(\n    id='ca_2lSG5qoh_GH9',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/qvD2hVck',\n    redirect_uri='https://backend.composio.dev/api/v3/s/qvD2hVck',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/qvD2hVck',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': '0c5d977203e51b0ba3eb0ea7a5e6ae2eb5225d49f3b25d52',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_Qyy5vdDeIplQ',\n    user_id='13807c9d-d266-4293-a6b7-8c1b97f698f8',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3',\n    name='github-ea7zeugt',\n    auth_config_ids=['ac_Qyy5vdDeIplQ'],\n    allowed_tools=[\n        'GITHUB_ACCEPT_A_REPOSITORY_INVITATION',\n        'GITHUB_ADD_AN_EMAIL_ADDRESS_FOR_THE_AUTHENTICATED_USER',\n        'GITHUB_ADD_APP_ACCESS_RESTRICTIONS',\n        'GITHUB_ADD_A_REPOSITORY_COLLABORATOR',\n        'GITHUB_ADD_A_REPOSITORY_TO_AN_APP_INSTALLATION',\n        'GITHUB_ADD_A_SELECTED_REPOSITORY_TO_A_USER_SECRET',\n        'GITHUB_ADD_ASSIGNEES_TO_AN_ISSUE',\n        'GITHUB_ADD_LABELS_TO_AN_ISSUE',\n        'GITHUB_ADD_ORG_RUNNER_LABELS',\n        'GITHUB_ADD_OR_UPDATE_TEAM_MEMBERSHIP_FOR_A_USER',\n        ... +771\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?transpor'+5,\n    toolkits=['github'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/1b9c'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/1b9c'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/1b9c'+51\n    ),\n    updated_at='2025-09-20T20:25:01.485Z',\n    created_at='2025-09-20T20:25:01.485Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acac20190>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'GitHub Profile'", "toolkit_slug": "'github'", "toolkit_name": "'GitHub'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/qvD2hVck'", "user_id": "'13807c9d-d266-4293-a6b7-8c1b97f698f8'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'github',\n    'toolkit_name': 'GitHub',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/1b9cdf99-d766-4613-bc3a-68cfa4fbb4a3?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/qvD2hVck',\n    'user_id': '13807c9d-d266-4293-a6b7-8c1b97f698f8',\n    'created_at': '2025-09-20T20:25:01.638785+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:25:01.638785+00:00\", \"mcp_url\": \"https://apollo.co'+290", "encrypted_config": "'gAAAAABozw2dWBfULWJsCNLPp-NbAdoiPB8wAK-pnfpnGZob1C9dVS6trpijph_7XQ6YcbgcRKG09FN5'+508", "config_hash": "'bf3ed877d9aef9c3a3019ac666f5385bb9a9cf41ff7f2d6d169a1860472f82aa'", "mcp_qualified_name": "'composio.github'", "profile_id": "'562d0bbb-38f6-42fc-8424-f8fafb89fec5'", "now": "datetime.datetime(2025, 9, 20, 20, 25, 1, 639109, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'GitHub Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acac54be0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:25:02.114717Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:25:02.114904Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 3.22s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:25:02.115370Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "********-19a0-4d41-8888-5ffc627fb07f", "query_params": ""}
INFO:     127.0.0.1:39256 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:25:03.293764Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:25:03.294454Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:25:03.294631Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:25:03.294818Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Generated integration user_id: 8b73b848-d6fa-46a6-abad-da6891757ab6 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:25:03.295005Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Starting Composio integration for toolkit: github", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:25:03.295126Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:25:03.295247Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:25:03.295351Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:25:03.698139Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit github", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:25:03.698828Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Creating auth config for toolkit: github", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:25:03.699214Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:25:03.699564Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:25:03.699956Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Successfully created auth config: ac_LRP9dw2zmQ5g", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 76, "timestamp": "2025-09-20T20:25:03.953356Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Step 2 complete: Created auth config ac_LRP9dw2zmQ5g", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 60, "timestamp": "2025-09-20T20:25:03.954008Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Creating connected account for auth_config: ac_LRP9dw2zmQ5g, user: 8b73b848-d6fa-46a6-abad-da6891757ab6", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 65, "timestamp": "2025-09-20T20:25:03.954595Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Initiation fields for connected account: None", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 66, "timestamp": "2025-09-20T20:25:03.955005Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Using state.val: {'status': 'INITIALIZING'}", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 78, "timestamp": "2025-09-20T20:25:03.955223Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Successfully created connected account: ca_3dxQ3AUiuHww", "level": "info", "filename": "connected_account_service.py", "func_name": "create_connected_account", "lineno": 124, "timestamp": "2025-09-20T20:25:04.647714Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Step 3 complete: Connected account ca_3dxQ3AUiuHww", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 67, "timestamp": "2025-09-20T20:25:04.648149Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Creating MCP server with auth_configs: ['ac_LRP9dw2zmQ5g']", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 67, "timestamp": "2025-09-20T20:25:04.648549Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Using MCP server name: github-ynmbteo3", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 75, "timestamp": "2025-09-20T20:25:04.648889Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Successfully created MCP server: ebfd487e-215f-48c6-a171-5c01a85aa2cc", "level": "info", "filename": "mcp_server_service.py", "func_name": "create_mcp_server", "lineno": 111, "timestamp": "2025-09-20T20:25:06.133727Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Step 4 complete: Created MCP server ebfd487e-215f-48c6-a171-5c01a85aa2cc", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 74, "timestamp": "2025-09-20T20:25:06.134096Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Generating MCP URL for server: ebfd487e-215f-48c6-a171-5c01a85aa2cc", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 125, "timestamp": "2025-09-20T20:25:06.134433Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Successfully generated MCP URL: https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc", "level": "info", "filename": "mcp_server_service.py", "func_name": "generate_mcp_url", "lineno": 149, "timestamp": "2025-09-20T20:25:06.290388Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Step 5 complete: Generated MCP URLs", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 81, "timestamp": "2025-09-20T20:25:06.290613Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Creating Composio profile for user: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, toolkit: github", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 102, "timestamp": "2025-09-20T20:25:06.290832Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "MCP URL to store: https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id=8b73b848-d6fa-46a6-abad-da6891757ab6", "level": "info", "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 103, "timestamp": "2025-09-20T20:25:06.291014Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Request started: GET /api/composio/toolkits/keap/details from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:25:06.292702Z", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:25:06.293198Z", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:25:06.293374Z", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:25:06.293596Z", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Fetching detailed toolkit info for: keap", "level": "info", "filename": "api.py", "func_name": "get_toolkit_details", "lineno": 142, "timestamp": "2025-09-20T20:25:06.293881Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Fetching detailed toolkit info for: keap", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 259, "timestamp": "2025-09-20T20:25:06.294101Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Raw toolkit response for keap: ToolkitRetrieveResponse(deprecated=Deprecated(raw_proxy_info_by_auth_schemes=[{'auth_method': 'OAUTH2', 'proxy': {}}], toolkit_id='6a9bafef-fa11-4e45-83a9-04f71667b5cd', get_current_user_endpoint='https://api.infusionsoft.com/crm/rest/v2/businessProfile', toolkitId='6a9bafef-fa11-4e45-83a9-04f71667b5cd', getCurrentUserEndpoint='https://api.infusionsoft.com/crm/rest/v2/businessProfile', rawProxyInfoByAuthSchemes=[{'auth_method': 'OAUTH2', 'proxy': {}}]), enabled=True, is_local_toolkit=False, meta=Meta(categories=[MetaCategory(name='Marketing & Social Media', slug='marketing-&-social-media'), MetaCategory(name='CRM', slug='crm'), MetaCategory(name='Sales & Customer Support', slug='sales-&-customer-support')], created_at='Fri May 03 2024', description='Keap (formerly Infusionsoft) combines CRM, marketing automation, and e-commerce, helping small businesses manage leads, automate campaigns, and boost sales', logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/keap.svg', tools_count=0.0, triggers_count=0.0, updated_at='Thu Jul 31 2025', app_url='https://keap.com'), name='Keap', slug='keap', auth_config_details=[AuthConfigDetail(fields=AuthConfigDetailFields(auth_config_creation=AuthConfigDetailFieldsAuthConfigCreation(optional=[AuthConfigDetailFieldsAuthConfigCreationOptional(description=\"Add this Redirect URL to your app's OAuth allow list.\", display_name='Redirect URI', name='oauth_redirect_uri', required=False, type='string', default='https://backend.composio.dev/api/v1/auth-apps/add', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Scopes to request from the user, comma separated', display_name='Scopes', name='scopes', required=False, type='string', default='full', legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationOptional(description='Access token injected automatically after OAuth2 authentication flow.', display_name='Access Token', name='bearer_token', required=False, type='string', default=None, legacy_template_name='access_token')], required=[AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client id of the app', display_name='Client id', name='client_id', required=True, type='string', default=None, legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationRequired(description='Client secret of the app', display_name='Client secret', name='client_secret', required=True, type='string', default=None, legacy_template_name=None), AuthConfigDetailFieldsAuthConfigCreationRequired(description='Base URL for Keap API', display_name='Base URL', name='full', required=True, type='string', default='https://api.infusionsoft.com', legacy_template_name='base_url')]), connected_account_initiation=AuthConfigDetailFieldsConnectedAccountInitiation(optional=[], required=[])), mode='OAUTH2', name='OAUTH2', proxy=None, deprecated_auth_provider_details={'token_url': 'https://api.infusionsoft.com/token', 'authorization_url': 'https://accounts.infusionsoft.com/app/oauth/authorize'})], base_url='https://api.infusionsoft.com', composio_managed_auth_schemes=['OAUTH2'], get_current_user_endpoint='https://api.infusionsoft.com/crm/rest/v2/businessProfile')", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 269, "timestamp": "2025-09-20T20:25:06.446066Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Parsed basic toolkit info: slug='keap' name='Keap' description='Keap (formerly Infusionsoft) combines CRM, marketing automation, and e-commerce, helping small businesses manage leads, automate campaigns, and boost sales' logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/keap.svg' tags=[] auth_schemes=['OAUTH2'] categories=['Marketing & Social Media', 'CRM', 'Sales & Customer Support'] auth_config_details=[] connected_account_initiation_fields=None base_url='https://api.infusionsoft.com'", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 296, "timestamp": "2025-09-20T20:25:06.447655Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Successfully fetched detailed info for keap", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 396, "timestamp": "2025-09-20T20:25:06.448251Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
{"event": "Initiation fields: {'required': [], 'optional': []}", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 397, "timestamp": "2025-09-20T20:25:06.448433Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/keap/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3c08cf56-1417-4ff1-a9ff-4e2a914147de", "query_params": ""}
INFO:     127.0.0.1:44712 - "GET /api/composio/toolkits/keap/details HTTP/1.1" 200 OK
{"event": "Failed to create Composio profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acaf2a2f0>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'GitHub Profile'", "toolkit_slug": "'github'", "toolkit_name": "'GitHub'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/dPp6GRpG'", "user_id": "'8b73b848-d6fa-46a6-abad-da6891757ab6'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'github',\n    'toolkit_name': 'GitHub',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/dPp6GRpG',\n    'user_id': '8b73b848-d6fa-46a6-abad-da6891757ab6',\n    'created_at': '2025-09-20T20:25:06.291241+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:25:06.291241+00:00\", \"mcp_url\": \"https://apollo.co'+290", "encrypted_config": "'gAAAAABozw2iwlIBTjS9DTAwQyzeF2B_dqgrePlL2UBvKXUqW-jXzBR8D-HGHRBbm46PpKkw6IZXh6tz'+508", "config_hash": "'fec8d2da70c9d33894df2206b51398f44ced43d711de49bbe9d56f74ded6e093'", "mcp_qualified_name": "'composio.github'", "profile_id": "'1eca48c9-d819-43ed-a029-2b76b8451bdb'", "now": "datetime.datetime(2025, 9, 20, 20, 25, 6, 291388, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'GitHub Profile'", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acaa73cd0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_profile_service.py", "func_name": "create_profile", "lineno": 169, "timestamp": "2025-09-20T20:25:06.606779Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Failed to integrate toolkit github: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "exception": [{"exc_type": "APIError", "exc_value": "{'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 90, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acaf2a350>", "toolkit_slug": "'github'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'8b73b848-d6fa-46a6-abad-da6891757ab6'", "profile_name": "'GitHub Profile'", "display_name": "'GitHub via Composio'", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='github',\n    name='GitHub',\n    description='GitHub is a code hosting platform for version control and collaboration, offerin'+86,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png',\n    tags=[],\n    auth_schemes=['OAUTH2', 'SERVICE_ACCOUNT'],\n    categories=[]\n)", "auth_config": "AuthConfig(\n    id='ac_LRP9dw2zmQ5g',\n    auth_scheme='OAUTH2',\n    is_composio_managed=True,\n    restrict_to_following_tools=[],\n    toolkit_slug='github'\n)", "connected_account": "ConnectedAccount(\n    id='ca_3dxQ3AUiuHww',\n    status='INITIATED',\n    redirect_url='https://backend.composio.dev/api/v3/s/dPp6GRpG',\n    redirect_uri='https://backend.composio.dev/api/v3/s/dPp6GRpG',\n    connection_data=ConnectionState(\n        auth_scheme='OAUTH2',\n        val={\n            'redirect_url': 'https://backend.composio.dev/api/v3/s/dPp6GRpG',\n            'status': 'INITIATED',\n            'account_id': None,\n            'account_url': None,\n            'api_url': None,\n            'base_url': None,\n            'borneo_dashboard_url': None,\n            'callback_url': 'https://backend.composio.dev/api/v1/auth-apps/add',\n            'code_verifier': 'd475e869056db37868ae9b9d73f8d679a8c2cf3422962d2a',\n            'companydomain': None,\n            ... +20\n        }\n    ),\n    auth_config_id='ac_LRP9dw2zmQ5g',\n    user_id='8b73b848-d6fa-46a6-abad-da6891757ab6',\n    deprecated=True\n)", "mcp_server": "MCPServer(\n    id='ebfd487e-215f-48c6-a171-5c01a85aa2cc',\n    name='github-ynmbteo3',\n    auth_config_ids=['ac_LRP9dw2zmQ5g'],\n    allowed_tools=[\n        'GITHUB_ACCEPT_A_REPOSITORY_INVITATION',\n        'GITHUB_ADD_AN_EMAIL_ADDRESS_FOR_THE_AUTHENTICATED_USER',\n        'GITHUB_ADD_APP_ACCESS_RESTRICTIONS',\n        'GITHUB_ADD_A_REPOSITORY_COLLABORATOR',\n        'GITHUB_ADD_A_REPOSITORY_TO_AN_APP_INSTALLATION',\n        'GITHUB_ADD_A_SELECTED_REPOSITORY_TO_A_USER_SECRET',\n        'GITHUB_ADD_ASSIGNEES_TO_AN_ISSUE',\n        'GITHUB_ADD_LABELS_TO_AN_ISSUE',\n        'GITHUB_ADD_ORG_RUNNER_LABELS',\n        'GITHUB_ADD_OR_UPDATE_TEAM_MEMBERSHIP_FOR_A_USER',\n        ... +771\n    ],\n    mcp_url='https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?transpor'+5,\n    toolkits=['github'],\n    commands=MCPCommands(\n        cursor='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/ebfd'+49,\n        claude='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/ebfd'+49,\n        windsurf='npx @composio/mcp@latest setup \"https://apollo.composio.dev/composio/server/ebfd'+51\n    ),\n    updated_at='2025-09-20T20:25:06.247Z',\n    created_at='2025-09-20T20:25:06.247Z',\n    managed_auth_via_composio=False\n)", "mcp_url_response": "MCPUrlResponse(\n    mcp_url='https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc',\n    connected_account_urls=[\n        'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?connecte'+29\n    ],\n    user_ids_url=[\n        'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id='+36\n    ]\n)", "final_mcp_url": "'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id='+36", "profile_id": "None", "e": "Error 42501:\nMessage: new row violates row-level security policy for table \"user_mcp_credential_profiles\""}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py", "lineno": 130, "name": "create_profile", "locals": {"self": "<composio_integration.composio_profile_service.ComposioProfileService object at 0x7f5acaf2a2f0>", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "profile_name": "'GitHub Profile'", "toolkit_slug": "'github'", "toolkit_name": "'GitHub'", "mcp_url": "'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id='+36", "redirect_url": "'https://backend.composio.dev/api/v3/s/dPp6GRpG'", "user_id": "'8b73b848-d6fa-46a6-abad-da6891757ab6'", "is_default": "False", "config": "{\n    'type': 'composio',\n    'toolkit_slug': 'github',\n    'toolkit_name': 'GitHub',\n    'mcp_url': 'https://apollo.composio.dev/v3/mcp/ebfd487e-215f-48c6-a171-5c01a85aa2cc?user_id='+36,\n    'redirect_url': 'https://backend.composio.dev/api/v3/s/dPp6GRpG',\n    'user_id': '8b73b848-d6fa-46a6-abad-da6891757ab6',\n    'created_at': '2025-09-20T20:25:06.291241+00:00'\n}", "config_json": "'{\"created_at\": \"2025-09-20T20:25:06.291241+00:00\", \"mcp_url\": \"https://apollo.co'+290", "encrypted_config": "'gAAAAABozw2iwlIBTjS9DTAwQyzeF2B_dqgrePlL2UBvKXUqW-jXzBR8D-HGHRBbm46PpKkw6IZXh6tz'+508", "config_hash": "'fec8d2da70c9d33894df2206b51398f44ced43d711de49bbe9d56f74ded6e093'", "mcp_qualified_name": "'composio.github'", "profile_id": "'1eca48c9-d819-43ed-a029-2b76b8451bdb'", "now": "datetime.datetime(2025, 9, 20, 20, 25, 6, 291388, tzinfo=datetime.timezone.utc)", "client": "<supabase._async.client.AsyncClient object at 0x7f5acba46440>", "unique_profile_name": "'GitHub Profile'"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/postgrest/_async/request_builder.py", "lineno": 78, "name": "execute", "locals": {"self": "<postgrest._async.request_builder.AsyncQueryRequestBuilder object at 0x7f5acaa73cd0>", "r": "<Response [401 Unauthorized]>", "json_obj": "APIErrorFromJSON(\n    message='new row violates row-level security policy for table \"user_mcp_credential_profil'+3,\n    code='42501',\n    hint=None,\n    details=None\n)"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:25:06.608042Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Failed to create profile: {'message': 'new row violates row-level security policy for table \"user_mcp_credential_profiles\"', 'code': '42501', 'hint': None, 'details': None}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:25:06.608388Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 3.32s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:25:06.608881Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "e3b0c1f9-a5a4-417b-9327-44633f0cd86a", "query_params": ""}
INFO:     127.0.0.1:39256 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:25:10.016988Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:25:10.017540Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "a541416d-11f4-4cc0-a71a-74f9e76480d3", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:25:10.018255Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "a541416d-11f4-4cc0-a71a-74f9e76480d3", "query_params": ""}
INFO:     127.0.0.1:55340 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:29:34.992661Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:34.993243Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "40cb6c8e-0109-4b29-8030-8a8bd3d57638", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:29:34.994693Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "40cb6c8e-0109-4b29-8030-8a8bd3d57638", "query_params": ""}
INFO:     127.0.0.1:42354 - "GET /api/health HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:29:34.996717Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:34.997005Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:34.997481Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:34.997657Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:34.997902Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:29:34.998149Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:29:35.007386Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:29:35.008762Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:29:35.009098Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:29:35.009482Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:29:35.019075Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:29:35.019989Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:29:35.020407Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "61e75bfb-ea03-4743-bdf0-7a54842ee019", "query_params": ""}
INFO:     127.0.0.1:42354 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "Request started: GET /feature-flags/custom_agents from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:35.160511Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "4dc44a47-a7fc-4e7c-a8d7-25d0c984618c", "query_params": ""}
{"event": "Request completed with error: GET /feature-flags/custom_agents | Status: 404 | Time: 0.00s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:29:35.161314Z", "path": "/feature-flags/custom_agents", "method": "GET", "client_ip": "127.0.0.1", "request_id": "4dc44a47-a7fc-4e7c-a8d7-25d0c984618c", "query_params": ""}
INFO:     127.0.0.1:42376 - "GET /feature-flags/custom_agents HTTP/1.1" 404 Not Found
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/health", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:29:39.921304Z"}
{"event": "Request started: GET /api/health from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:39.921714Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "38ec5051-00f3-4367-9431-1f374bad868c", "query_params": ""}
{"event": "Health check endpoint called", "level": "info", "filename": "api.py", "func_name": "health_check", "lineno": 251, "timestamp": "2025-09-20T20:29:39.922209Z", "path": "/api/health", "method": "GET", "client_ip": "127.0.0.1", "request_id": "38ec5051-00f3-4367-9431-1f374bad868c", "query_params": ""}
INFO:     127.0.0.1:42354 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:42354 - "OPTIONS /api/billing/available-models HTTP/1.1" 200 OK
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/subscription", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:29:40.475748Z"}
{"event": "Request started: GET /api/billing/subscription from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:40.476340Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:40.476854Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:40.477632Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:40.478672Z", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:29:40.479645Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:29:40.485507Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "CORS request from origin: http://localhost:3000 | Method: GET | Path: /api/billing/available-models", "level": "info", "filename": "api.py", "func_name": "cors_debug_middleware", "lineno": 174, "timestamp": "2025-09-20T20:29:40.486165Z"}
{"event": "Request started: GET /api/billing/available-models from 127.0.0.1 | Origin: http://localhost:3000 | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:40.486532Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3f425e68-e013-4e81-9f37-6c8fc3a4e5ca", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:40.486875Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3f425e68-e013-4e81-9f37-6c8fc3a4e5ca", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'sec-ch-ua-platform', 'authorization', 'user-agent', 'sec-ch-ua', 'content-type', 'sec-ch-ua-mobile', 'accept', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest', 'referer', 'accept-encoding', 'accept-language']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:40.487007Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3f425e68-e013-4e81-9f37-6c8fc3a4e5ca", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:40.487187Z", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "request_id": "3f425e68-e013-4e81-9f37-6c8fc3a4e5ca", "query_params": ""}
{"event": "Running in local development mode - billing checks are disabled", "level": "info", "filename": "billing.py", "func_name": "get_available_models", "lineno": 1517, "timestamp": "2025-09-20T20:29:40.487352Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/available-models", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "3f425e68-e013-4e81-9f37-6c8fc3a4e5ca", "query_params": ""}
INFO:     127.0.0.1:42354 - "GET /api/billing/available-models HTTP/1.1" 200 OK
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:29:40.488163Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Error getting subscription from Stripe: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_user_subscription", "lineno": 358, "timestamp": "2025-09-20T20:29:40.488318Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:29:40.488518Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:29:40.493927Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:29:40.494737Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
{"event": "Error calculating monthly usage for user 26ab50d8-f2dd-4b57-a885-f9dceb3353cc: 'NoneType' object has no attribute 'get'", "level": "error", "filename": "billing.py", "func_name": "get_subscription", "lineno": 1268, "timestamp": "2025-09-20T20:29:40.494897Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/billing/subscription", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "5df00a0c-5f58-464d-9a88-2dffae3eee68", "query_params": ""}
INFO:     127.0.0.1:42366 - "GET /api/billing/subscription HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:42.853887Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:42.854500Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:42.854665Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:42.854892Z", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
{"event": "Fetching Composio toolkits with limit: 100, cursor: None, search: None, category: None", "level": "info", "filename": "api.py", "func_name": "list_toolkits", "lineno": 112, "timestamp": "2025-09-20T20:29:42.855304Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
{"event": "Fetching toolkits with limit: 100, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:29:42.855650Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
{"event": "Successfully fetched 100 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:29:43.527874Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "c77006f3-970f-4b1a-a462-889c7294e443", "query_params": ""}
INFO:     127.0.0.1:58858 - "GET /api/composio/toolkits HTTP/1.1" 200 OK
{"event": "Request started: GET /api/composio/toolkits/composio/details from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:46.663818Z", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:46.664442Z", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:46.664799Z", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:46.665093Z", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Fetching detailed toolkit info for: composio", "level": "info", "filename": "api.py", "func_name": "get_toolkit_details", "lineno": 142, "timestamp": "2025-09-20T20:29:46.665470Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Fetching detailed toolkit info for: composio", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 259, "timestamp": "2025-09-20T20:29:46.666005Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Raw toolkit response for composio: ToolkitRetrieveResponse(deprecated=Deprecated(raw_proxy_info_by_auth_schemes=[{'auth_method': 'NO_AUTH', 'proxy': {}}], toolkit_id='784908aa-9ff2-4d6b-995c-fc63787e2dbd', get_current_user_endpoint='N/A', toolkitId='784908aa-9ff2-4d6b-995c-fc63787e2dbd', getCurrentUserEndpoint='N/A', rawProxyInfoByAuthSchemes=[{'auth_method': 'NO_AUTH', 'proxy': {}}]), enabled=True, is_local_toolkit=False, meta=Meta(categories=[MetaCategory(name='AI & Machine Learning', slug='ai-&-machine-learning')], created_at='Tue May 14 2024', description='Composio enables AI Agents and LLMs to authenticate and integrate with various tools via function calling.', logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//composio-logo.png', tools_count=28.0, triggers_count=0.0, updated_at='Thu Jul 31 2025', app_url='https://composio.dev'), name='Composio', slug='composio', auth_config_details=[AuthConfigDetail(fields=AuthConfigDetailFields(auth_config_creation=AuthConfigDetailFieldsAuthConfigCreation(optional=[], required=[]), connected_account_initiation=AuthConfigDetailFieldsConnectedAccountInitiation(optional=[], required=[])), mode='NO_AUTH', name='Composio', proxy=None)], base_url='https://api.composio.dev', composio_managed_auth_schemes=[], get_current_user_endpoint='N/A')", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 269, "timestamp": "2025-09-20T20:29:46.826061Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Parsed basic toolkit info: slug='composio' name='Composio' description='Composio enables AI Agents and LLMs to authenticate and integrate with various tools via function calling.' logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/composio-logo.png' tags=[] auth_schemes=[] categories=['AI & Machine Learning'] auth_config_details=[] connected_account_initiation_fields=None base_url='https://api.composio.dev'", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 296, "timestamp": "2025-09-20T20:29:46.826351Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Successfully fetched detailed info for composio", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 396, "timestamp": "2025-09-20T20:29:46.826574Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
{"event": "Initiation fields: {'required': [], 'optional': []}", "level": "info", "filename": "toolkit_service.py", "func_name": "get_detailed_toolkit_info", "lineno": 397, "timestamp": "2025-09-20T20:29:46.826762Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/toolkits/composio/details", "method": "GET", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "40d3d1f9-1297-44b0-978a-27bbefdc4831", "query_params": ""}
INFO:     127.0.0.1:58858 - "GET /api/composio/toolkits/composio/details HTTP/1.1" 200 OK
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:47.777605Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:47.778323Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:47.778539Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:47.778755Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Generated integration user_id: 90090aba-4b88-4821-a2e0-e18675c05c33 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:29:47.779046Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Starting Composio integration for toolkit: composio", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:29:47.779268Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:29:47.779416Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:29:47.779576Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:29:48.197835Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit composio", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:29:48.198522Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Creating auth config for toolkit: composio", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:29:48.198762Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:29:48.198914Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:29:48.199115Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Failed to create auth config for composio: Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "level": "error", "exception": [{"exc_type": "BadRequestError", "exc_value": "Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/auth_config_service.py", "lineno": 56, "name": "create_auth_config", "locals": {"self": "<composio_integration.auth_config_service.AuthConfigService object at 0x7f5acaf652d0>", "toolkit_slug": "'composio'", "initiation_fields": "None", "credentials": "{'region': 'ind'}", "e": "BadRequestError(\"Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}\")"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/resources/auth_configs.py", "lineno": 73, "name": "create", "locals": {"self": "<composio_client.resources.auth_configs.AuthConfigsResource object at 0x7f5acbaeef20>", "toolkit": "{'slug': 'composio'}", "auth_config": "{'type': 'use_composio_managed_auth', 'credentials': {'region': 'ind'}}", "extra_headers": "None", "extra_query": "None", "extra_body": "None", "timeout": "NOT_GIVEN"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1239, "name": "post", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "path": "'/api/v3/auth_configs'", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "body": "{\n    'toolkit': {'slug': 'composio'},\n    'auth_config': {\n        'type': 'use_composio_managed_auth',\n        'credentials': {'region': 'ind'}\n    }\n}", "options": "{}", "files": "None", "stream": "False", "stream_cls": "None", "opts": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key=None,\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1041, "name": "request", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-548b400d-aaad-4475-b629-af5f5fd6543b',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "stream": "False", "stream_cls": "None", "input_options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-548b400d-aaad-4475-b629-af5f5fd6543b',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "response": "<Response [400 Bad Request]>", "max_retries": "2", "retries_taken": "0", "remaining_retries": "2", "request": "<Request('POST', 'https://backend.composio.dev/api/v3/auth_configs')>", "kwargs": "{}"}}], "is_group": false, "exceptions": []}], "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 80, "timestamp": "2025-09-20T20:29:48.410886Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Failed to integrate toolkit composio: Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "level": "error", "exception": [{"exc_type": "BadRequestError", "exc_value": "Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 56, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acaf65390>", "toolkit_slug": "'composio'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'90090aba-4b88-4821-a2e0-e18675c05c33'", "profile_name": "'Composio Profile'", "display_name": "None", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='composio',\n    name='Composio',\n    description='Composio enables AI Agents and LLMs to authenticate and integrate with various t'+26,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/composio-logo.png',\n    tags=[],\n    auth_schemes=['NO_AUTH'],\n    categories=[]\n)", "e": "BadRequestError(\"Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}\")"}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/auth_config_service.py", "lineno": 56, "name": "create_auth_config", "locals": {"self": "<composio_integration.auth_config_service.AuthConfigService object at 0x7f5acaf652d0>", "toolkit_slug": "'composio'", "initiation_fields": "None", "credentials": "{'region': 'ind'}"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/resources/auth_configs.py", "lineno": 73, "name": "create", "locals": {"self": "<composio_client.resources.auth_configs.AuthConfigsResource object at 0x7f5acbaeef20>", "toolkit": "{'slug': 'composio'}", "auth_config": "{'type': 'use_composio_managed_auth', 'credentials': {'region': 'ind'}}", "extra_headers": "None", "extra_query": "None", "extra_body": "None", "timeout": "NOT_GIVEN"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1239, "name": "post", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "path": "'/api/v3/auth_configs'", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "body": "{\n    'toolkit': {'slug': 'composio'},\n    'auth_config': {\n        'type': 'use_composio_managed_auth',\n        'credentials': {'region': 'ind'}\n    }\n}", "options": "{}", "files": "None", "stream": "False", "stream_cls": "None", "opts": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key=None,\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1041, "name": "request", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-548b400d-aaad-4475-b629-af5f5fd6543b',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "stream": "False", "stream_cls": "None", "input_options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-548b400d-aaad-4475-b629-af5f5fd6543b',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "response": "<Response [400 Bad Request]>", "max_retries": "2", "retries_taken": "0", "remaining_retries": "2", "request": "<Request('POST', 'https://backend.composio.dev/api/v3/auth_configs')>", "kwargs": "{}"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:29:48.412186Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Failed to create profile: Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '862260fc-a85e-4817-b99e-06e0f5af427b', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:29:48.412376Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 0.64s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:29:48.412647Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "388febf8-22a9-4d95-a819-88cb67232a64", "query_params": ""}
INFO:     127.0.0.1:58858 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
{"event": "Request started: POST /api/composio/profiles from 127.0.0.1 | Origin: no-origin | Query: ", "level": "info", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 128, "timestamp": "2025-09-20T20:29:49.627165Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Auth header received: Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IndkSnhBdjlBTXZ...", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 143, "timestamp": "2025-09-20T20:29:49.627897Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Request headers: ['host', 'connection', 'content-type', 'authorization', 'x-refresh-token', 'x-user-id', 'x-composio-api-key', 'accept', 'accept-language', 'sec-fetch-mode', 'user-agent', 'accept-encoding', 'content-length']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 144, "timestamp": "2025-09-20T20:29:49.628118Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "JWT decoded successfully. User ID: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc, Payload keys: ['iss', 'sub', 'aud', 'exp', 'iat', 'email', 'phone', 'app_metadata', 'user_metadata', 'role', 'aal', 'amr', 'session_id', 'is_anonymous']", "level": "info", "filename": "auth_utils.py", "func_name": "get_current_user_id_from_jwt", "lineno": 161, "timestamp": "2025-09-20T20:29:49.628387Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Generated integration user_id: ac11720a-f906-4026-b6e6-00c916c5fee1 for account: 26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "level": "info", "filename": "api.py", "func_name": "create_profile", "lineno": 206, "timestamp": "2025-09-20T20:29:49.628639Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Starting Composio integration for toolkit: composio", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 47, "timestamp": "2025-09-20T20:29:49.628848Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 48, "timestamp": "2025-09-20T20:29:49.628998Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Fetching toolkits with limit: 500, cursor: None, category: None", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 111, "timestamp": "2025-09-20T20:29:49.629176Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Successfully fetched 500 toolkits with OAUTH2 in both auth schemes", "level": "info", "filename": "toolkit_service.py", "func_name": "list_toolkits", "lineno": 207, "timestamp": "2025-09-20T20:29:50.035958Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Step 1 complete: Verified toolkit composio", "level": "info", "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 54, "timestamp": "2025-09-20T20:29:50.036573Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Creating auth config for toolkit: composio", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 41, "timestamp": "2025-09-20T20:29:50.037037Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Initiation fields: None", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 42, "timestamp": "2025-09-20T20:29:50.037271Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Using credentials: {'region': 'ind'}", "level": "info", "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 54, "timestamp": "2025-09-20T20:29:50.037521Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Failed to create auth config for composio: Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "level": "error", "exception": [{"exc_type": "BadRequestError", "exc_value": "Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/auth_config_service.py", "lineno": 56, "name": "create_auth_config", "locals": {"self": "<composio_integration.auth_config_service.AuthConfigService object at 0x7f5acaf947c0>", "toolkit_slug": "'composio'", "initiation_fields": "None", "credentials": "{'region': 'ind'}", "e": "BadRequestError(\"Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}\")"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/resources/auth_configs.py", "lineno": 73, "name": "create", "locals": {"self": "<composio_client.resources.auth_configs.AuthConfigsResource object at 0x7f5acbaeef20>", "toolkit": "{'slug': 'composio'}", "auth_config": "{'type': 'use_composio_managed_auth', 'credentials': {'region': 'ind'}}", "extra_headers": "None", "extra_query": "None", "extra_body": "None", "timeout": "NOT_GIVEN"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1239, "name": "post", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "path": "'/api/v3/auth_configs'", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "body": "{\n    'toolkit': {'slug': 'composio'},\n    'auth_config': {\n        'type': 'use_composio_managed_auth',\n        'credentials': {'region': 'ind'}\n    }\n}", "options": "{}", "files": "None", "stream": "False", "stream_cls": "None", "opts": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key=None,\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1041, "name": "request", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-aec494a5-fdb5-4906-81fc-9fddbd356659',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "stream": "False", "stream_cls": "None", "input_options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-aec494a5-fdb5-4906-81fc-9fddbd356659',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "response": "<Response [400 Bad Request]>", "max_retries": "2", "retries_taken": "0", "remaining_retries": "2", "request": "<Request('POST', 'https://backend.composio.dev/api/v3/auth_configs')>", "kwargs": "{}"}}], "is_group": false, "exceptions": []}], "filename": "auth_config_service.py", "func_name": "create_auth_config", "lineno": 80, "timestamp": "2025-09-20T20:29:50.173228Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Failed to integrate toolkit composio: Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "level": "error", "exception": [{"exc_type": "BadRequestError", "exc_value": "Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "exc_notes": [], "syntax_error": null, "is_cause": false, "frames": [{"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py", "lineno": 56, "name": "integrate_toolkit", "locals": {"self": "<composio_integration.composio_service.ComposioIntegrationService object at 0x7f5acaf94850>", "toolkit_slug": "'composio'", "account_id": "'26ab50d8-f2dd-4b57-a885-f9dceb3353cc'", "user_id": "'ac11720a-f906-4026-b6e6-00c916c5fee1'", "profile_name": "'Composio Profile'", "display_name": "None", "mcp_server_name": "None", "save_as_profile": "True", "initiation_fields": "None", "toolkit": "ToolkitInfo(\n    slug='composio',\n    name='Composio',\n    description='Composio enables AI Agents and LLMs to authenticate and integrate with various t'+26,\n    logo='https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/composio-logo.png',\n    tags=[],\n    auth_schemes=['NO_AUTH'],\n    categories=[]\n)", "e": "BadRequestError(\"Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}\")"}}, {"filename": "/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/auth_config_service.py", "lineno": 56, "name": "create_auth_config", "locals": {"self": "<composio_integration.auth_config_service.AuthConfigService object at 0x7f5acaf947c0>", "toolkit_slug": "'composio'", "initiation_fields": "None", "credentials": "{'region': 'ind'}"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/resources/auth_configs.py", "lineno": 73, "name": "create", "locals": {"self": "<composio_client.resources.auth_configs.AuthConfigsResource object at 0x7f5acbaeef20>", "toolkit": "{'slug': 'composio'}", "auth_config": "{'type': 'use_composio_managed_auth', 'credentials': {'region': 'ind'}}", "extra_headers": "None", "extra_query": "None", "extra_body": "None", "timeout": "NOT_GIVEN"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1239, "name": "post", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "path": "'/api/v3/auth_configs'", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "body": "{\n    'toolkit': {'slug': 'composio'},\n    'auth_config': {\n        'type': 'use_composio_managed_auth',\n        'credentials': {'region': 'ind'}\n    }\n}", "options": "{}", "files": "None", "stream": "False", "stream_cls": "None", "opts": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key=None,\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)"}}, {"filename": "/home/<USER>/.local/lib/python3.10/site-packages/composio_client/_base_client.py", "lineno": 1041, "name": "request", "locals": {"self": "<composio_client.Composio object at 0x7f5acbaeefb0>", "cast_to": "<class 'composio_client.types.auth_config_create_response.AuthConfigCreateResponse'>", "options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-aec494a5-fdb5-4906-81fc-9fddbd356659',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "stream": "False", "stream_cls": "None", "input_options": "FinalRequestOptions(\n    method='post',\n    url='/api/v3/auth_configs',\n    params={},\n    headers=NOT_GIVEN,\n    max_retries=NOT_GIVEN,\n    timeout=NOT_GIVEN,\n    files=None,\n    idempotency_key='stainless-python-retry-aec494a5-fdb5-4906-81fc-9fddbd356659',\n    post_parser=NOT_GIVEN,\n    follow_redirects=None,\n    json_data={\n        'toolkit': {'slug': 'composio'},\n        'auth_config': {\n            'type': 'use_composio_managed_auth',\n            'credentials': {'region': 'ind'}\n        }\n    },\n    extra_json=None\n)", "response": "<Response [400 Bad Request]>", "max_retries": "2", "retries_taken": "0", "remaining_retries": "2", "request": "<Request('POST', 'https://backend.composio.dev/api/v3/auth_configs')>", "kwargs": "{}"}}], "is_group": false, "exceptions": []}], "filename": "composio_service.py", "func_name": "integrate_toolkit", "lineno": 119, "timestamp": "2025-09-20T20:29:50.174780Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Failed to create profile: Error code: 400 - {'error': {'message': 'Cannot create an auth config for a no auth toolkit: composio', 'code': 303, 'status': 400, 'request_id': '8bdc23de-5478-4acc-bc7e-7b4cd1e050ee', 'suggested_fix': 'You can use this toolkit without auth config.'}}", "level": "error", "filename": "api.py", "func_name": "create_profile", "lineno": 240, "timestamp": "2025-09-20T20:29:50.175110Z", "user_id": "26ab50d8-f2dd-4b57-a885-f9dceb3353cc", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "auth_method": "jwt", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
{"event": "Request completed with error: POST /api/composio/profiles | Status: 500 | Time: 0.55s | Origin: no-origin", "level": "warning", "filename": "api.py", "func_name": "log_requests_middleware", "lineno": 136, "timestamp": "2025-09-20T20:29:50.175730Z", "path": "/api/composio/profiles", "method": "POST", "client_ip": "127.0.0.1", "request_id": "6a564daf-a138-47d3-8464-5a7084c0198a", "query_params": ""}
INFO:     127.0.0.1:58858 - "POST /api/composio/profiles HTTP/1.1" 500 Internal Server Error
INFO:     Shutting down
INFO:     Waiting for application shutdown.
{"event": "Performance monitoring stopped", "level": "info", "filename": "performance_monitor.py", "func_name": "stop_monitoring", "lineno": 268, "timestamp": "2025-09-20T20:29:55.689036Z"}
{"event": "Self-healing system stopped", "level": "info", "filename": "self_healing.py", "func_name": "stop_monitoring", "lineno": 590, "timestamp": "2025-09-20T20:29:55.689442Z"}
{"event": "Monitoring and self-healing systems cleaned up", "level": "info", "filename": "api.py", "func_name": "cleanup", "lineno": 419, "timestamp": "2025-09-20T20:29:55.689961Z"}
{"event": "Cleaning up agent resources", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 87, "timestamp": "2025-09-20T20:29:55.690258Z"}
{"event": "Starting cleanup of agent API resources", "level": "info", "filename": "api.py", "func_name": "cleanup", "lineno": 206, "timestamp": "2025-09-20T20:29:55.690581Z"}
{"event": "Initializing Redis connection", "level": "info", "filename": "redis.py", "func_name": "initialize_async", "lineno": 65, "timestamp": "2025-09-20T20:29:55.691463Z"}
{"event": "Initializing Redis connection pool to localhost:6379 with max 128 connections", "level": "info", "filename": "redis.py", "func_name": "initialize", "lineno": 37, "timestamp": "2025-09-20T20:29:55.706770Z"}
{"event": "Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379). - using fallback mode", "level": "warning", "filename": "redis.py", "func_name": "initialize_async", "lineno": 80, "timestamp": "2025-09-20T20:29:55.708036Z"}
{"event": "Failed to clean up running agent runs: 'NoneType' object has no attribute 'keys'", "level": "error", "filename": "api.py", "func_name": "cleanup", "lineno": 226, "timestamp": "2025-09-20T20:29:55.708375Z"}
{"event": "Closing Redis connection pool", "level": "info", "filename": "redis.py", "func_name": "close", "lineno": 104, "timestamp": "2025-09-20T20:29:55.708532Z"}
{"event": "Redis connection and pool closed", "level": "info", "filename": "redis.py", "func_name": "close", "lineno": 115, "timestamp": "2025-09-20T20:29:55.708784Z"}
{"event": "Completed cleanup of agent API resources", "level": "info", "filename": "api.py", "func_name": "cleanup", "lineno": 230, "timestamp": "2025-09-20T20:29:55.708953Z"}
{"event": "Closing Redis connection", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 92, "timestamp": "2025-09-20T20:29:55.709150Z"}
{"event": "Redis connection and pool closed", "level": "info", "filename": "redis.py", "func_name": "close", "lineno": 115, "timestamp": "2025-09-20T20:29:55.709370Z"}
{"event": "Redis connection closed successfully", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 94, "timestamp": "2025-09-20T20:29:55.709581Z"}
{"event": "Disconnecting from database", "level": "info", "filename": "api.py", "func_name": "lifespan", "lineno": 99, "timestamp": "2025-09-20T20:29:55.709737Z"}
{"event": "Disconnecting from Supabase database", "level": "info", "filename": "supabase.py", "func_name": "disconnect", "lineno": 68, "timestamp": "2025-09-20T20:29:55.709888Z"}
{"event": "Database disconnected successfully", "level": "info", "filename": "supabase.py", "func_name": "disconnect", "lineno": 79, "timestamp": "2025-09-20T20:29:55.710049Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [93030]
