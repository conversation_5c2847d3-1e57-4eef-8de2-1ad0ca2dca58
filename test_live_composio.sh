#!/bin/bash
# Test the Composio profile creation endpoint to see if the fix is working

echo "Testing Composio Profile Creation with OAuth URL Fix"
echo "====================================================="

# Note: You'll need to replace this with a valid JWT token
JWT_TOKEN="your-jwt-token-here"

# Test creating a profile (this would normally trigger the OAuth flow)
curl -X POST http://localhost:8003/api/composio/profiles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "toolkit_slug": "gmail",
    "profile_name": "test-gmail-profile",
    "display_name": "Gmail Test"
  }' \
  -v 2>&1 | grep -E "(redirect_url|backend.composio.dev|app.composio.dev)"

echo ""
echo "Check the response above:"
echo "- If redirect_url contains 'app.composio.dev/auth', the fix is working ✅"
echo "- If redirect_url contains 'backend.composio.dev/api/v3/s/', the fix is NOT applied ❌"