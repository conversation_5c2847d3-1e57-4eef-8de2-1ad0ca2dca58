# How to Test the Composio OAuth Fix

## ✅ The Fix Has Been Applied

The code has been modified to fix the Composio OAuth URL issue. Here's what was done:

### Files Modified:
- `/suna-main/backend/composio_integration/connected_account_service.py`
  - 3 locations where backend short links are detected and fixed
  - Converts `backend.composio.dev/api/v3/s/...` → `app.composio.dev/auth?...`

### Backend Status:
- ✅ Running on port 8003
- ✅ Fix is loaded and active
- ✅ Health check passing

## 🧪 How to Test

### Step 1: Open the Application
1. Open your browser
2. Go to: http://localhost:3000
3. Log in with your credentials

### Step 2: Try Connecting a Composio App
1. Navigate to one of these locations:
   - **Agents Page** → Create/Edit Agent → Add Integration → Composio
   - **Settings** → Integrations → Composio
   - **Any agent configuration** → Tools & Integrations section

2. Select an app to connect (e.g., Gmail, Slack, GitHub)

3. Click "Connect" or "Create Profile"

### Step 3: Check the Popup URL
When the OAuth popup opens, check the URL in the popup window:

#### ✅ CORRECT (Fixed):
```
https://app.composio.dev/auth?connected_account_id=xxx&user_id=yyy...
```

#### ❌ INCORRECT (Old bug):
```
https://backend.composio.dev/api/v3/s/Uz4Ir-J
```

## 🔍 Debugging Tips

### If you still see the old URL:
1. **Hard refresh the frontend**: Ctrl+Shift+R (or Cmd+Shift+R on Mac)
2. **Clear browser cache**: Settings → Clear browsing data
3. **Check browser console**: F12 → Console tab for any errors
4. **Verify backend is using fix**: Check the terminal running the backend for log messages like:
   - "Detected backend short link..."
   - "Constructed OAuth URL..."

### Check Backend Logs:
```bash
# Watch backend logs in real-time
tail -f /tmp/backend_restart.log | grep -E "composio|OAuth|redirect_url"
```

### Verify the Fix is Active:
```bash
# This should show 3 occurrences
grep -c "backend.composio.dev/api/v3/s/" /mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/connected_account_service.py
```

## 📊 Expected Behavior After Fix

1. Click "Connect" for a Composio app
2. Popup opens with `app.composio.dev/auth` URL
3. Complete OAuth flow on Composio's site
4. Return to your app with successful connection
5. No more 404 errors!

## 🚀 Summary

The fix intercepts Composio's response and:
- Detects invalid backend short links
- Constructs proper OAuth URLs
- Preserves all necessary parameters
- Ensures successful OAuth flow

The backend is running with this fix active on port 8003.