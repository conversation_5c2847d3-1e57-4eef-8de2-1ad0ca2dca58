# Multi-Agent System Configuration
# Configuration for integrating AutoGen, DeepResearchAgent, and LlamaIndex

# Agent Configuration
agents:
  autogen:
    enabled: true
    models:
      primary: "gpt-4"
      fallback: "gpt-3.5-turbo"
    max_tokens: 2000
    temperature: 0.7
    timeout: 60
    retry_attempts: 3
    
  deepresearch:
    enabled: true
    search_depth: 3
    max_results: 10
    timeout: 30
    browser_timeout: 20
    parallel_searches: 2
    
  llamaindex:
    enabled: true
    chunk_size: 512
    chunk_overlap: 50
    embedding_model: "text-embedding-ada-002"
    vector_store: "chromadb"
    similarity_top_k: 5
    
  composio:
    enabled: true
    max_tools: 250
    auth_timeout: 30
    action_timeout: 60

# Orchestration Configuration
orchestration:
  max_rounds: 20
  speaker_selection: "auto"  # LLM-based speaker selection
  conversation_memory: 50    # Number of messages to keep in memory
  parallel_execution: false  # Whether to run agents in parallel
  termination_conditions:
    - "query_answered"
    - "max_rounds_reached"
    - "error_threshold_exceeded"
    - "user_interrupt"
  
  speaker_selection_prompt: |
    Based on the conversation history and the last message, select the most appropriate agent to respond next.
    Consider:
    - Agent capabilities and specializations
    - Query requirements
    - Conversation progress
    - What information is still needed
    
    Available agents:
    - UserProxy: Interface with human user
    - DataAnalyst: Internal data analysis using LlamaIndex
    - ExternalResearcher: Web research using DeepResearchAgent
    - ToolAgent: External tool integration via Composio
    - Synthesizer: Combine and synthesize information

# Performance Configuration
performance:
  token_limits:
    AutoGenAssistant: 2000
    DeepResearcher: 3000
    LlamaIndexRAG: 1500
    ComposioTool: 1000
    Synthesizer: 2500
  
  caching:
    response_cache_ttl: 3600      # 1 hour
    embedding_cache_ttl: 86400    # 24 hours
    search_cache_ttl: 1800        # 30 minutes
    tool_cache_ttl: 600           # 10 minutes
    max_cache_size: 1000          # Maximum cached items
  
  monitoring:
    enable_metrics: true
    log_level: "INFO"
    metric_collection_interval: 60  # seconds

# Security Configuration
security:
  api_key_rotation: false
  audit_logging: true
  data_encryption: false
  
  agent_permissions:
    UserProxy:
      - "read_query"
      - "write_response"
    DataAnalyst:
      - "read_internal"
      - "query_vectors"
      - "access_memory"
    ExternalResearcher:
      - "web_search"
      - "browser_access"
      - "public_apis"
    ToolAgent:
      - "approved_tools_only"
      - "oauth_handling"
    Synthesizer:
      - "read_all"
      - "write_final"
      - "combine_sources"

# Error Handling Configuration
error_handling:
  retry_attempts: 3
  fallback_enabled: true
  graceful_degradation: true
  error_threshold: 0.2  # 20% error rate triggers circuit breaker
  
  fallback_agents:
    DataAnalyst: "UserProxy"
    ExternalResearcher: "DataAnalyst"
    ToolAgent: "ExternalResearcher"
    Synthesizer: "DataAnalyst"

# Message Configuration
messaging:
  max_message_length: 4000
  message_format: "structured"  # structured | simple
  include_metadata: true
  
  metadata_fields:
    - "timestamp"
    - "agent_type"
    - "confidence"
    - "sources"
    - "tokens_used"
    - "processing_time"

# Integration Specific Settings
integration:
  # AutoGen specific
  autogen:
    group_chat_manager_config:
      llm_config:
        model: "gpt-4"
        temperature: 0.7
        max_tokens: 1000
      speaker_selection_method: "auto"
      allow_repeat_speaker: true
  
  # DeepResearch specific  
  deepresearch:
    planning_agent_config:
      max_planning_steps: 5
      planning_timeout: 30
    research_agent_config:
      max_research_depth: 3
      concurrent_searches: 2
    browser_agent_config:
      headless: true
      timeout: 20
  
  # LlamaIndex specific
  llamaindex:
    index_config:
      chunk_size: 512
      chunk_overlap: 50
    query_engine_config:
      similarity_top_k: 5
      streaming: false
    memory_config:
      memory_type: "buffer"
      max_tokens: 2000

# Development Configuration
development:
  debug_mode: true
  verbose_logging: true
  save_conversations: true
  conversation_log_path: "./logs/conversations"
  
  testing:
    mock_llm_responses: false
    use_test_data: false
    skip_external_calls: false

# Production Configuration  
production:
  debug_mode: false
  verbose_logging: false
  save_conversations: true
  conversation_log_path: "/var/log/olari/conversations"
  
  scaling:
    max_concurrent_conversations: 100
    agent_pool_size: 10
    connection_pool_size: 20
  
  monitoring:
    enable_prometheus: true
    enable_jaeger: true
    health_check_interval: 30