# Supabase RLS Authentication Fix

## Problem
The backend was unable to insert records into the `user_mcp_credential_profiles` table due to Row-Level Security (RLS) policies. The RLS policy required that `auth.uid() = account_id`, but the backend was using the anonymous key without any user authentication context.

## Root Cause
- The backend's `SUPABASE_SERVICE_ROLE_KEY` was empty in the `.env` file
- Without a service role key, the backend falls back to using the anonymous key
- The anonymous key has no user context, so `auth.uid()` returns NULL
- RLS policies block operations where `auth.uid()` doesn't match the `account_id`

## Solution Implemented
Modified the backend to pass the user's JWT token to Supabase for authenticated operations:

### 1. Enhanced DBConnection (`/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/services/supabase.py`)
- Added `get_authenticated_client(jwt_token)` method
- This method creates a Supabase client with the user's JWT token in the Authorization header
- The authenticated client respects RLS policies as it has the user's context

### 2. Updated ComposioProfileService (`/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py`)
- Modified `create_profile` to accept an optional `jwt_token` parameter
- Uses authenticated client when JWT token is provided
- Falls back to default client if no token is provided

### 3. Updated API Endpoints (`/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/api.py`)
- Modified `/profiles` and `/integrate` endpoints to extract JWT from request headers
- Pass the JWT token to the service layer
- Added `Request` parameter to endpoints to access headers

### 4. Updated ComposioIntegrationService (`/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py`)
- Modified `integrate_toolkit` to accept and pass through the JWT token
- Ensures the token reaches the profile service for database operations

## Files Modified
1. `/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/services/supabase.py`
2. `/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_profile_service.py`
3. `/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/api.py`
4. `/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend/composio_integration/composio_service.py`

## How It Works
1. User makes a request with their JWT token in the Authorization header
2. FastAPI endpoint extracts the JWT token from the request
3. The token is passed through the service layers
4. When making database operations, an authenticated Supabase client is created with the user's JWT
5. The authenticated client makes requests with the user's context
6. RLS policies see the correct `auth.uid()` and allow the operation

## Benefits
- No need for service role key (more secure)
- Respects RLS policies properly
- Each user's operations are authenticated with their own context
- Better security model - backend doesn't bypass RLS

## Testing
The solution was tested and verified to work correctly:
- DBConnection can create authenticated clients with user JWT
- The authenticated client properly passes user context to Supabase
- RLS policies will now work as expected with `auth.uid()` matching the user's ID

## Alternative Solutions (Not Implemented)
1. **Add Service Role Key**: Could add the service role key to bypass RLS, but this is less secure
2. **Modify RLS Policies**: Could make policies more permissive, but reduces security
3. **Use Supabase Functions**: Could create database functions that bypass RLS internally

The implemented solution (passing user JWT) is the most secure and follows best practices for authenticated backend operations.