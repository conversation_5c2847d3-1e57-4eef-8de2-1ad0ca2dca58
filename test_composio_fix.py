#!/usr/bin/env python3
"""
Test script to verify Composio OAuth URL fix
"""

import asyncio
import sys
import os
sys.path.insert(0, '/mnt/c/Users/<USER>/Downloads/olari/suna-main/backend')

from composio_integration.connected_account_service import ConnectedAccountService, ConnectedAccount
from unittest.mock import Mock, MagicMock
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_oauth_url_fix():
    """Test that backend short links are converted to proper OAuth URLs"""
    
    print("\n🧪 Testing Composio OAuth URL Fix\n" + "="*50)
    
    # Create a mock response from Composio API that returns a backend short link
    mock_response = MagicMock()
    mock_response.id = "test-account-123"
    mock_response.status = "PENDING"
    mock_response.redirect_url = "https://backend.composio.dev/api/v3/s/Uz4Ir-J"  # Short link that causes 404
    mock_response.redirect_uri = None
    mock_response.connection_data = MagicMock()
    mock_response.deprecated = None
    
    # Create service instance
    service = ConnectedAccountService()
    
    # Mock the client's create method to return our mock response
    original_create = service.client.connected_accounts.create
    service.client.connected_accounts.create = lambda **kwargs: mock_response
    
    try:
        # Test create_connected_account method
        print("📝 Test 1: Creating connected account with backend short link")
        print(f"   Input redirect_url: {mock_response.redirect_url}")
        
        account = await service.create_connected_account(
            auth_config_id="test-auth-123",
            user_id="test-user-456"
        )
        
        print(f"   Output redirect_url: {account.redirect_url}")
        
        # Check if the URL was fixed
        if account.redirect_url and 'app.composio.dev/auth' in account.redirect_url:
            print("   ✅ PASS: Backend short link was converted to app.composio.dev/auth URL")
        else:
            print(f"   ❌ FAIL: URL was not fixed. Got: {account.redirect_url}")
            return False
            
        # Verify the OAuth parameters are included
        if 'connected_account_id=test-account-123' in account.redirect_url:
            print("   ✅ PASS: OAuth URL includes connected_account_id parameter")
        else:
            print("   ❌ FAIL: OAuth URL missing connected_account_id parameter")
            return False
            
        print("\n✅ All tests passed! The fix is working correctly.")
        print("\n📋 Summary:")
        print("   - Backend short links (backend.composio.dev/api/v3/s/*) are detected")
        print("   - Proper OAuth URLs (app.composio.dev/auth) are generated")
        print("   - Required OAuth parameters are included in the URL")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Restore original method
        service.client.connected_accounts.create = original_create

async def test_valid_url_passthrough():
    """Test that valid URLs are not modified"""
    
    print("\n🧪 Testing Valid URL Passthrough\n" + "="*50)
    
    # Create a mock response with a valid OAuth URL
    mock_response = MagicMock()
    mock_response.id = "test-account-456"
    mock_response.status = "PENDING"
    mock_response.redirect_url = "https://app.composio.dev/auth?connected_account_id=456"  # Already correct
    mock_response.redirect_uri = None
    mock_response.connection_data = MagicMock()
    mock_response.deprecated = None
    
    # Create service instance
    service = ConnectedAccountService()
    
    # Mock the client's create method
    service.client.connected_accounts.create = lambda **kwargs: mock_response
    
    try:
        print("📝 Test 2: Creating connected account with valid OAuth URL")
        print(f"   Input redirect_url: {mock_response.redirect_url}")
        
        account = await service.create_connected_account(
            auth_config_id="test-auth-456",
            user_id="test-user-789"
        )
        
        print(f"   Output redirect_url: {account.redirect_url}")
        
        # Check if the URL was left unchanged
        if account.redirect_url == mock_response.redirect_url:
            print("   ✅ PASS: Valid OAuth URL was not modified")
            return True
        else:
            print(f"   ❌ FAIL: Valid URL was incorrectly modified")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    
    print("\n🚀 Composio OAuth URL Fix Test Suite")
    print("=" * 60)
    
    # Run tests
    test1_passed = await test_oauth_url_fix()
    test2_passed = await test_valid_url_passthrough()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED - The fix is working correctly!")
        print("\n🎯 What this means:")
        print("   1. Backend short links that cause 404 errors are fixed")
        print("   2. Proper OAuth URLs at app.composio.dev are generated")
        print("   3. Valid URLs are left unchanged")
        print("   4. Users will be redirected to the correct OAuth page")
    else:
        print("❌ SOME TESTS FAILED - Please review the output above")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(main())