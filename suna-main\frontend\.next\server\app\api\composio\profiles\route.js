/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/composio/profiles/route";
exports.ids = ["app/api/composio/profiles/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Fprofiles%2Froute&page=%2Fapi%2Fcomposio%2Fprofiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Fprofiles%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Fprofiles%2Froute&page=%2Fapi%2Fcomposio%2Fprofiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Fprofiles%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_composio_profiles_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/composio/profiles/route.ts */ \"(rsc)/./src/app/api/composio/profiles/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/composio/profiles/route\",\n        pathname: \"/api/composio/profiles\",\n        filename: \"route\",\n        bundlePath: \"app/api/composio/profiles/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"/mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/src/app/api/composio/profiles/route.ts\",\n    nextConfigOutput,\n    userland: _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_app_api_composio_profiles_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/composio/profiles/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Fprofiles%2Froute&page=%2Fapi%2Fcomposio%2Fprofiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Fprofiles%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00c3b743d074a69244eab1d948789d902598d3c17c\": () => (/* reexport safe */ _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__.createClient)\n/* harmony export */ });\n/* harmony import */ var _mnt_c_Users_Ibrahim_Downloads_olari_suna_main_frontend_src_lib_supabase_server_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/lib/supabase/server.ts */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyJTJGbW50JTJGYyUyRlVzZXJzJTJGSWJyYWhpbSUyRkRvd25sb2FkcyUyRm9sYXJpJTJGc3VuYS1tYWluJTJGZnJvbnRlbmQlMkZzcmMlMkZsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiUyQyU1QiU3QiUyMmlkJTIyJTNBJTIyMDBjM2I3NDNkMDc0YTY5MjQ0ZWFiMWQ5NDg3ODlkOTAyNTk4ZDNjMTdjJTIyJTJDJTIyZXhwb3J0ZWROYW1lJTIyJTNBJTIyY3JlYXRlQ2xpZW50JTIyJTJDJTIyZmlsZW5hbWUlMjIlM0ElMjJsaWIlMkZzdXBhYmFzZSUyRnNlcnZlci50cyUyMiU3RCU1RCU1RCU1RCZfX2NsaWVudF9pbXBvcnRlZF9fPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ2lLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBjcmVhdGVDbGllbnQgYXMgXCIwMGMzYjc0M2QwNzRhNjkyNDRlYWIxZDk0ODc4OWQ5MDI1OThkM2MxN2NcIiB9IGZyb20gXCIvbW50L2MvVXNlcnMvSWJyYWhpbS9Eb3dubG9hZHMvb2xhcmkvc3VuYS1tYWluL2Zyb250ZW5kL3NyYy9saWIvc3VwYWJhc2Uvc2VydmVyLnRzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Flib%2Fsupabase%2Fserver.ts%22%2C%5B%7B%22id%22%3A%2200c3b743d074a69244eab1d948789d902598d3c17c%22%2C%22exportedName%22%3A%22createClient%22%2C%22filename%22%3A%22lib%2Fsupabase%2Fserver.ts%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/composio/profiles/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/composio/profiles/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nconst API_URL = \"http://localhost:8003\" || 0;\n// Helper function to get OAuth scope for a toolkit\nfunction getOAuthScope(toolkit) {\n    const scopes = {\n        gmail: 'https://www.googleapis.com/auth/gmail.send https://www.googleapis.com/auth/gmail.readonly',\n        google: 'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile',\n        googlecalendar: 'https://www.googleapis.com/auth/calendar',\n        googledrive: 'https://www.googleapis.com/auth/drive',\n        slack: 'chat:write,channels:read,users:read',\n        github: 'repo,user,workflow',\n        twitter: 'tweet.read,tweet.write,users.read',\n        linkedin: 'r_liteprofile,r_emailaddress',\n        notion: 'read,write',\n        discord: 'identify,email,guilds',\n        dropbox: 'files.metadata.read,files.content.read',\n        spotify: 'user-read-email,user-read-private',\n        trello: 'read,write,account',\n        zoom: 'user:read,meeting:write',\n        salesforce: 'api,refresh_token',\n        stripe: 'read_write',\n        shopify: 'read_products,write_products'\n    };\n    // Default scope if toolkit not found\n    return scopes[toolkit] || 'read,write';\n}\n// Mock profiles data\nconst MOCK_PROFILES = {\n    success: true,\n    profiles: [\n        {\n            id: 'profile-001',\n            profile_id: 'profile-001',\n            name: 'Default Profile',\n            toolkit: 'gmail',\n            status: 'connected',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z',\n            auth_type: 'oauth2',\n            metadata: {\n                email: '<EMAIL>',\n                scopes: [\n                    'gmail.send',\n                    'gmail.read'\n                ]\n            }\n        },\n        {\n            id: 'profile-002',\n            profile_id: 'profile-002',\n            name: 'Slack Workspace',\n            toolkit: 'slack',\n            status: 'connected',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z',\n            auth_type: 'oauth2',\n            metadata: {\n                workspace: 'Example Workspace',\n                team_id: 'T123456'\n            }\n        },\n        {\n            id: 'profile-003',\n            profile_id: 'profile-003',\n            name: 'GitHub Account',\n            toolkit: 'github',\n            status: 'connected',\n            created_at: '2024-01-01T00:00:00Z',\n            updated_at: '2024-09-01T00:00:00Z',\n            auth_type: 'api_key',\n            metadata: {\n                username: 'developer',\n                org: 'example-org'\n            }\n        }\n    ],\n    total: 3\n};\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Get the current session for authentication (optional for this endpoint)\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        // If API_URL is not configured or empty, return mock data\n        if (!API_URL || API_URL.trim() === '') {\n            console.log('Using mock data for Composio profiles (API_URL not configured)');\n            // Filter mock profiles based on query parameters\n            let filteredProfiles = [\n                ...MOCK_PROFILES.profiles\n            ];\n            const toolkit = searchParams.get('toolkit');\n            if (toolkit) {\n                filteredProfiles = filteredProfiles.filter((p)=>p.toolkit === toolkit);\n            }\n            const status = searchParams.get('status');\n            if (status) {\n                filteredProfiles = filteredProfiles.filter((p)=>p.status === status);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                profiles: filteredProfiles,\n                total: filteredProfiles.length\n            });\n        }\n        // Validate URL format\n        try {\n            new URL(`${API_URL}/api/composio/profiles`);\n        } catch (urlError) {\n            console.log('Invalid backend URL format, returning mock data');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(MOCK_PROFILES);\n        }\n        // Build the backend URL with query parameters\n        const backendUrl = new URL(`${API_URL}/api/composio/profiles`);\n        searchParams.forEach((value, key)=>{\n            backendUrl.searchParams.append(key, value);\n        });\n        // Build headers\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Add authorization header if session exists\n        if (session?.access_token) {\n            headers['Authorization'] = `Bearer ${session.access_token}`;\n            console.log('GET request: Using authorization token from session');\n        } else {\n            console.warn('GET request: No session token available');\n        }\n        // Call the backend API with timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n        try {\n            const response = await fetch(backendUrl.toString(), {\n                method: 'GET',\n                headers,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                console.error(`Backend returned error: ${response.status} ${response.statusText}`);\n                console.log('Backend failed, returning mock data as fallback');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    ...MOCK_PROFILES,\n                    _fallback: true,\n                    _error: `Backend API error: ${response.status}`\n                });\n            }\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            if (fetchError.name === 'AbortError') {\n                console.log('Backend API timeout, returning mock data');\n            } else {\n                console.log('Backend API unavailable, returning mock data');\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...MOCK_PROFILES,\n                _fallback: true,\n                _error: 'Backend unavailable, using mock data'\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching profiles:', error);\n        // Return mock data on any error\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...MOCK_PROFILES,\n            _fallback: true,\n            _error: 'Failed to fetch profiles, using mock data'\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Get the current session for authentication with proper error handling\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n        // If there's an error getting the session, try to refresh it\n        if (sessionError || !session) {\n            console.error('Session error or no session:', sessionError?.message || 'No session found');\n            // Try to get the user to trigger a token refresh if needed\n            const { data: { user }, error: userError } = await supabase.auth.getUser();\n            if (userError || !user) {\n                console.error('Authentication failed:', userError?.message || 'No user found');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Authentication required. Please log in and try again.',\n                    details: 'Session expired or invalid'\n                }, {\n                    status: 401\n                });\n            }\n            // Get session again after potential refresh\n            const refreshedSession = await supabase.auth.getSession();\n            if (!refreshedSession.data.session) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Session refresh failed. Please log in again.'\n                }, {\n                    status: 401\n                });\n            }\n        }\n        // Get the refreshed or existing session\n        const currentSession = session || (await supabase.auth.getSession()).data.session;\n        console.log('Session status:', {\n            hasSession: !!currentSession,\n            hasAccessToken: !!currentSession?.access_token,\n            userId: currentSession?.user?.id,\n            tokenExpiry: currentSession?.expires_at,\n            tokenExpiresIn: currentSession?.expires_at ? new Date(currentSession.expires_at * 1000).toISOString() : null\n        });\n        // Backend URL is required\n        if (!API_URL || API_URL.trim() === '') {\n            console.error('Backend URL not configured');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Backend service not configured'\n            }, {\n                status: 503\n            });\n        }\n        // Build headers\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Check if we have a valid session\n        if (!currentSession || !currentSession.access_token) {\n            console.error('POST request: No valid session after refresh attempt');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Authentication required',\n                details: 'Please log in to connect integrations'\n            }, {\n                status: 401\n            });\n        }\n        // Add authorization header with the access token\n        headers['Authorization'] = `Bearer ${currentSession.access_token}`;\n        // Also add the refresh token if available (backend might need it)\n        if (currentSession.refresh_token) {\n            headers['X-Refresh-Token'] = currentSession.refresh_token;\n        }\n        // Add user ID for backend validation\n        if (currentSession.user?.id) {\n            headers['X-User-Id'] = currentSession.user.id;\n        }\n        // Add Composio API key if available (backend might need it)\n        const composioApiKey = process.env.COMPOSIO_API_KEY || \"ak_hPlpPFHaetHA4ErO50-v\";\n        if (composioApiKey) {\n            headers['X-Composio-Api-Key'] = composioApiKey;\n        }\n        console.log('POST request: Sending to backend with auth headers:', {\n            hasAuth: !!headers['Authorization'],\n            hasRefresh: !!headers['X-Refresh-Token'],\n            userId: headers['X-User-Id'] || 'not-set',\n            hasComposioKey: !!headers['X-Composio-Api-Key']\n        });\n        // Log the request for debugging\n        console.log('Sending to backend:', {\n            url: `${API_URL}/api/composio/profiles`,\n            headers: {\n                ...headers,\n                'Authorization': headers['Authorization'] ? '[REDACTED]' : 'missing'\n            },\n            body,\n            session: session ? 'present' : 'missing',\n            access_token: session?.access_token ? 'present' : 'missing'\n        });\n        // Call the backend API with timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout for OAuth flow\n        try {\n            const response = await fetch(`${API_URL}/api/composio/profiles`, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                console.error(`Backend returned error: ${response.status} ${response.statusText}`);\n                // Try to parse error response\n                let errorData;\n                try {\n                    errorData = await response.json();\n                } catch  {\n                    errorData = {\n                        error: await response.text() || 'Failed to create profile'\n                    };\n                }\n                console.error('Backend error details:', errorData);\n                console.error('Request body was:', body);\n                // For authentication errors, provide clear guidance\n                if (response.status === 401) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'Authentication failed. Please ensure you are logged in and try again.',\n                        details: 'The backend service requires valid authentication credentials.',\n                        ...errorData\n                    }, {\n                        status: 401\n                    });\n                }\n                // For 400 errors, provide detailed information\n                if (response.status === 400) {\n                    console.error('Backend returned 400 Bad Request:', {\n                        errorData,\n                        requestBody: body,\n                        url: `${API_URL}/api/composio/profiles`,\n                        hasSession: !!session,\n                        hasToken: !!session?.access_token\n                    });\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: errorData.detail || errorData.error || 'Invalid request parameters',\n                        details: `Backend validation error: ${JSON.stringify(errorData)}`,\n                        request_body: body,\n                        debug_info: {\n                            backend_url: API_URL,\n                            has_session: !!session,\n                            has_token: !!session?.access_token,\n                            error_response: errorData\n                        }\n                    }, {\n                        status: 400\n                    });\n                }\n                // For other errors, return the actual error without fallback\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    ...errorData\n                }, {\n                    status: response.status\n                });\n            }\n            const data = await response.json();\n            console.log('Backend response received:', {\n                hasProfileId: !!data.profile_id,\n                hasRedirectUrl: !!data.redirect_url,\n                profileName: data.profile_name,\n                toolkitSlug: data.toolkit_slug\n            });\n            // Ensure the response includes required fields for frontend compatibility\n            const responseData = {\n                success: true,\n                profile_id: data.profile_id || data.id,\n                redirect_url: data.redirect_url || data.auth_url || null,\n                auth_url: data.redirect_url || data.auth_url || null,\n                mcp_url: data.mcp_url || `http://localhost:3001/mcp/composio/${data.profile_id || data.id}`,\n                profile_name: data.profile_name || body.profile_name,\n                toolkit_slug: data.toolkit_slug || body.toolkit_slug,\n                ...data\n            };\n            console.log('Returning response to frontend:', {\n                profileId: responseData.profile_id,\n                hasAuthUrl: !!responseData.redirect_url,\n                mcpUrl: responseData.mcp_url\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(responseData);\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            const errorMessage = fetchError.name === 'AbortError' ? 'Request timeout - backend took too long to respond' : 'Backend service is unavailable';\n            console.error(`Backend API error: ${errorMessage}`, fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: errorMessage,\n                details: 'Please ensure the backend service is running and accessible.'\n            }, {\n                status: 503\n            });\n        }\n    } catch (error) {\n        console.error('Error creating profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'An unexpected error occurred while creating the profile',\n            details: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { profile_id, ...updateData } = body;\n        if (!profile_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'profile_id is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get the current session for authentication\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        // If API_URL is not configured or empty, return mock response\n        if (!API_URL || API_URL.trim() === '') {\n            console.log('Mock updating Composio profile (API_URL not configured)');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                profile: {\n                    id: profile_id,\n                    profile_id: profile_id,\n                    ...updateData,\n                    updated_at: new Date().toISOString()\n                }\n            });\n        }\n        // Build headers\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Add authorization header if session exists\n        if (session?.access_token) {\n            headers['Authorization'] = `Bearer ${session.access_token}`;\n        }\n        // Call the backend API with timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n        try {\n            const response = await fetch(`${API_URL}/api/composio/profiles/${profile_id}`, {\n                method: 'PUT',\n                headers,\n                body: JSON.stringify(updateData),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                console.error(`Backend returned error: ${response.status} ${response.statusText}`);\n                // Return mock success for non-critical errors\n                console.log('Backend failed, returning mock success');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    profile: {\n                        id: profile_id,\n                        profile_id: profile_id,\n                        ...updateData,\n                        updated_at: new Date().toISOString()\n                    },\n                    _fallback: true\n                });\n            }\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            console.log('Backend unavailable, returning mock response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                profile: {\n                    id: profile_id,\n                    profile_id: profile_id,\n                    ...updateData,\n                    updated_at: new Date().toISOString()\n                },\n                _fallback: true\n            });\n        }\n    } catch (error) {\n        console.error('Error updating profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to update profile'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const profile_id = searchParams.get('profile_id');\n        if (!profile_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'profile_id is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get the current session for authentication\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        // If API_URL is not configured or empty, return mock response\n        if (!API_URL || API_URL.trim() === '') {\n            console.log('Mock deleting Composio profile (API_URL not configured)');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Profile deleted successfully'\n            });\n        }\n        // Build headers\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Add authorization header if session exists\n        if (session?.access_token) {\n            headers['Authorization'] = `Bearer ${session.access_token}`;\n        }\n        // Call the backend API with timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n        try {\n            const response = await fetch(`${API_URL}/api/composio/profiles/${profile_id}`, {\n                method: 'DELETE',\n                headers,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                console.error(`Backend returned error: ${response.status} ${response.statusText}`);\n                // Return mock success for non-critical errors\n                console.log('Backend failed, returning mock success');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: 'Profile deleted successfully',\n                    _fallback: true\n                });\n            }\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            console.log('Backend unavailable, returning mock response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Profile deleted successfully',\n                _fallback: true\n            });\n        }\n    } catch (error) {\n        console.error('Error deleting profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to delete profile'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/composio/profiles/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00c3b743d074a69244eab1d948789d902598d3c17c\":\"createClient\"} */ \n\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://llliyihxaeakffunjjcb.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxsbGl5aWh4YWVha2ZmdW5qamNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4NDMxOTAsImV4cCI6MjA3MjQxOTE5MH0.Cu0nxk1MregjKOTlXNU4S0NXZlTiaRsPEmiK4isI04M\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    createClient\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(createClient, \"00c3b743d074a69244eab1d948789d902598d3c17c\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDWjtBQUUvQixlQUFlRTtJQUNwQixNQUFNQyxjQUFjLE1BQU1GLHFEQUFPQTtJQUVqQyxPQUFPRCxpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLGtOQUF5QyxFQUN6QztRQUNFSCxTQUFTO1lBQ1BPO2dCQUNFLE9BQU9MLFlBQVlLLE1BQU07WUFDM0I7WUFDQUMsUUFBT0MsWUFBWTtnQkFDakIsSUFBSTtvQkFDRkEsYUFBYUMsT0FBTyxDQUFDLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLE9BQU8sRUFBRSxHQUM1Q1gsWUFBWVksR0FBRyxDQUFDSCxNQUFNQyxPQUFPQztnQkFFakMsRUFBRSxPQUFNO2dCQUNOLDBEQUEwRDtnQkFDMUQsd0RBQXdEO2dCQUN4RCxpQkFBaUI7Z0JBQ25CO1lBQ0Y7UUFDRjtJQUNGO0FBRUo7OztJQXpCc0JaOztBQUFBQSwwRkFBQUEsQ0FBQUEiLCJzb3VyY2VzIjpbIi9tbnQvYy9Vc2Vycy9JYnJhaGltL0Rvd25sb2Fkcy9vbGFyaS9zdW5hLW1haW4vZnJvbnRlbmQvc3JjL2xpYi9zdXBhYmFzZS9zZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzZXJ2ZXInXG5pbXBvcnQgeyBjcmVhdGVTZXJ2ZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJ1xuaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycydcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgY29uc3QgY29va2llU3RvcmUgPSBhd2FpdCBjb29raWVzKClcblxuICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50KFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhLFxuICAgIHtcbiAgICAgIGNvb2tpZXM6IHtcbiAgICAgICAgZ2V0QWxsKCkge1xuICAgICAgICAgIHJldHVybiBjb29raWVTdG9yZS5nZXRBbGwoKVxuICAgICAgICB9LFxuICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZXNUb1NldC5mb3JFYWNoKCh7IG5hbWUsIHZhbHVlLCBvcHRpb25zIH0pID0+XG4gICAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgIClcbiAgICAgICAgICB9IGNhdGNoIHtcbiAgICAgICAgICAgIC8vIFRoZSBgc2V0QWxsYCBtZXRob2Qgd2FzIGNhbGxlZCBmcm9tIGEgU2VydmVyIENvbXBvbmVudC5cbiAgICAgICAgICAgIC8vIFRoaXMgY2FuIGJlIGlnbm9yZWQgaWYgeW91IGhhdmUgbWlkZGxld2FyZSByZWZyZXNoaW5nXG4gICAgICAgICAgICAvLyB1c2VyIHNlc3Npb25zLlxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfVxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlU2VydmVyQ2xpZW50IiwiY29va2llcyIsImNyZWF0ZUNsaWVudCIsImNvb2tpZVN0b3JlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiZ2V0QWxsIiwic2V0QWxsIiwiY29va2llc1RvU2V0IiwiZm9yRWFjaCIsIm5hbWUiLCJ2YWx1ZSIsIm9wdGlvbnMiLCJzZXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcomposio%2Fprofiles%2Froute&page=%2Fapi%2Fcomposio%2Fprofiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomposio%2Fprofiles%2Froute.ts&appDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2FIbrahim%2FDownloads%2Folari%2Fsuna-main%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();