
> Kortix@0.1.0 dev
> next dev

 ⚠ Warning: Next.js inferred your workspace root, but it may not be correct.
 We detected multiple lockfiles and selected the directory of /mnt/c/Users/<USER>/package-lock.json as the root directory.
 To silence this warning, set `outputFileTracingRoot` in your Next.js config, or consider removing one of the lockfiles if it's not needed.
   See https://nextjs.org/docs/app/api-reference/config/next-config-js/output#caveats for more information.
 Detected additional lockfiles: 
   * /mnt/c/Users/<USER>/Downloads/olari/suna-main/frontend/package-lock.json
   * /mnt/c/Users/<USER>/Downloads/olari/suna-main/package-lock.json

   ▲ Next.js 15.5.3
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local
   - Experiments (use with caution):
     · optimizePackageImports

 ✓ Starting...
Warning: Reverting webpack devtool to 'false'.
Changing the webpack devtool in development mode will cause severe performance regressions.
Read more: https://nextjs.org/docs/messages/improper-devtool
 ✓ Ready in 16.3s
 ✓ Compiled /middleware in 132ms (114 modules)
 ○ Compiling /api/health ...
 ✓ Compiled /api/health in 527ms (403 modules)
 GET /api/health 200 in 1355ms
 ○ Compiling / ...
 ✓ Compiled / in 5.8s (3540 modules)
 GET / 200 in 8950ms
 ○ Compiling /api/triggers/workflows/agents/[agentId]/workflows ...
 ✓ Compiled /dashboard in 3.3s (3952 modules)
 GET /api/edge-flags 200 in 4165ms
 GET /api/composio/toolkits 200 in 4824ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 4804ms
 GET /api/feature-flags/custom_agents 200 in 5080ms
 GET /dashboard 200 in 3426ms
 ○ Compiling /api/health ...
 ✓ Compiled /api/health in 737ms (2039 modules)
 GET /api/health 200 in 1062ms
 GET /api/edge-flags 200 in 534ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 200 in 378ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 777ms
 GET /api/composio/toolkits 200 in 689ms
 ○ Compiling /api/composio/toolkits/[slug]/details ...
 ✓ Compiled /api/composio/toolkits/[slug]/details in 944ms (3956 modules)
 GET /api/composio/toolkits/googlesheets/details 200 in 7259ms
 ○ Compiling /api/composio/profiles ...
 ✓ Compiled /api/composio/profiles in 841ms (3958 modules)
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: { detail: '500: Profile created but not found' }
Request body was: { toolkit_slug: 'googlesheets', profile_name: 'Googlesheets Profile' }
 POST /api/composio/profiles 500 in 3915ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 500 Internal Server Error
Backend error details: { detail: '500: Profile created but not found' }
Request body was: { toolkit_slug: 'googlesheets', profile_name: 'Googlesheets Profile' }
 POST /api/composio/profiles 500 in 2704ms
 GET /api/health 200 in 156ms
 GET /api/health 200 in 173ms
 GET /api/health 200 in 195ms
 GET /api/edge-flags 200 in 188ms
 GET /dashboard 200 in 811ms
 GET /api/health 200 in 187ms
 GET /api/edge-flags 200 in 180ms
 GET /api/composio/toolkits 200 in 663ms
 GET /api/composio/toolkits/googlesheets/details 200 in 380ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: {
    toolkit_slug: 'googlesheets',
    profile_name: 'Googlesheets Profile'
  },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Googlesheets Profile',
  toolkitSlug: 'googlesheets'
}
Returning response to frontend: {
  profileId: 'b0397cb3-a8e8-4ba0-99ec-138ca520c7db',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/7295d4ff-62f2-4067-92f5-c80df091b5ca?user_id=6b4c95c1-78fb-47c4-95d6-e98e5aaf6744'
}
 POST /api/composio/profiles 200 in 2889ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 426ms
 GET /api/health 200 in 179ms
 GET /api/health 200 in 160ms
 GET /api/health 200 in 112ms
 GET /api/edge-flags 200 in 185ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 239ms
 GET /dashboard 200 in 282ms
 GET /api/health 200 in 179ms
 GET /api/edge-flags 200 in 180ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 216ms
 GET /api/composio/toolkits 200 in 770ms
 GET /api/composio/toolkits?cursor=Mi0xMDA%3D 200 in 477ms
 GET /api/composio/toolkits/twitter/details 200 in 484ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4070ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 347ms
 GET /api/health 200 in 189ms
 GET /api/edge-flags 200 in 162ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3603ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 306ms
 GET /dashboard 200 in 250ms
 GET /api/health 200 in 167ms
 GET /api/edge-flags 200 in 170ms
 GET /api/composio/toolkits 200 in 651ms
 GET /api/composio/toolkits/gmail/details 200 in 381ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 400 Bad Request
Backend error details: {
  detail: "Invalid isoformat string: '2025-09-20T20:46:21.83955+00:00'"
}
Request body was: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' }
Backend returned 400 Bad Request: {
  errorData: {
    detail: "Invalid isoformat string: '2025-09-20T20:46:21.83955+00:00'"
  },
  requestBody: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  url: 'http://localhost:8003/api/composio/profiles',
  hasSession: true,
  hasToken: true
}
 POST /api/composio/profiles 400 in 3294ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 400 Bad Request
Backend error details: {
  detail: "Invalid isoformat string: '2025-09-20T20:46:21.83955+00:00'"
}
Request body was: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' }
Backend returned 400 Bad Request: {
  errorData: {
    detail: "Invalid isoformat string: '2025-09-20T20:46:21.83955+00:00'"
  },
  requestBody: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  url: 'http://localhost:8003/api/composio/profiles',
  hasSession: true,
  hasToken: true
}
 POST /api/composio/profiles 400 in 2678ms
 GET /api/composio/toolkits/firecrawl/details 200 in 360ms
 GET /api/composio/toolkits/googledocs/details 200 in 364ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'googledocs', profile_name: 'Googledocs Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Googledocs Profile',
  toolkitSlug: 'googledocs'
}
Returning response to frontend: {
  profileId: '17b62fd5-df66-47d5-9572-1ba1e4a4eb9d',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/21c7ea46-2548-433f-a808-b265a1894b0e?user_id=eecc6fce-1fa6-47ae-bc51-bf6a73404d89'
}
 POST /api/composio/profiles 200 in 2908ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 306ms
 GET /api/composio/toolkits/notion/details 200 in 437ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'notion', profile_name: 'Notion Profile' },
  session: 'present',
  access_token: 'present'
}
 GET /api/health 200 in 202ms
 GET /api/edge-flags 200 in 207ms
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Notion Profile',
  toolkitSlug: 'notion'
}
Returning response to frontend: {
  profileId: 'fe57168b-b026-4e6b-9aaf-eafef7c6ffe5',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/d4eee063-8643-432c-89a8-e78dc54528b6?user_id=542a8675-b92f-403f-a9d2-61bf1b25fb5c'
}
 POST /api/composio/profiles 200 in 2810ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 323ms
 GET /api/composio/toolkits/serpapi/details 200 in 406ms
 GET /api/composio/toolkits/slackbot/details 200 in 364ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'slackbot', profile_name: 'Slackbot Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Slackbot Profile',
  toolkitSlug: 'slackbot'
}
Returning response to frontend: {
  profileId: '64c6fad9-6ce1-48e9-90f1-7c3a567f5b8b',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/f9bb3944-2d55-4fa8-a6f6-daa7e3d5f5a3?user_id=6ddec513-d462-485c-b849-f8b79dd3056f'
}
 POST /api/composio/profiles 200 in 3108ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 1417ms
 GET /api/composio/toolkits?search=m 200 in 827ms
 GET /api/composio/toolkits?search=mo 200 in 1579ms
 GET /api/composio/toolkits?search=mon 200 in 1385ms
 GET /api/composio/toolkits/monday/details 200 in 538ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'monday', profile_name: 'Monday Profile' },
  session: 'present',
  access_token: 'present'
}
Backend returned error: 400 Bad Request
Backend error details: {
  detail: "Invalid isoformat string: '2025-09-20T20:47:43.7878+00:00'"
}
Request body was: { toolkit_slug: 'monday', profile_name: 'Monday Profile' }
Backend returned 400 Bad Request: {
  errorData: {
    detail: "Invalid isoformat string: '2025-09-20T20:47:43.7878+00:00'"
  },
  requestBody: { toolkit_slug: 'monday', profile_name: 'Monday Profile' },
  url: 'http://localhost:8003/api/composio/profiles',
  hasSession: true,
  hasToken: true
}
 POST /api/composio/profiles 400 in 2478ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'monday', profile_name: 'Monday Profile' },
  session: 'present',
  access_token: 'present'
}
 GET /api/health 200 in 171ms
 GET /api/edge-flags 200 in 160ms
Backend returned error: 400 Bad Request
Backend error details: {
  detail: "Invalid isoformat string: '2025-09-20T20:47:43.7878+00:00'"
}
Request body was: { toolkit_slug: 'monday', profile_name: 'Monday Profile' }
Backend returned 400 Bad Request: {
  errorData: {
    detail: "Invalid isoformat string: '2025-09-20T20:47:43.7878+00:00'"
  },
  requestBody: { toolkit_slug: 'monday', profile_name: 'Monday Profile' },
  url: 'http://localhost:8003/api/composio/profiles',
  hasSession: true,
  hasToken: true
}
 POST /api/composio/profiles 400 in 2856ms
 GET /api/edge-flags 200 in 158ms
 GET /api/health 200 in 162ms
 GET /api/edge-flags 200 in 276ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 280ms
 GET /api/health 200 in 120ms
 GET /api/edge-flags 200 in 165ms
 GET /api/health 200 in 157ms
 GET /api/health 200 in 152ms
 GET /api/health 200 in 156ms
 GET /api/health 200 in 152ms
 GET /api/health 200 in 165ms
 GET /api/health 200 in 153ms
 GET /api/health 200 in 179ms
 GET /api/edge-flags 200 in 272ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 354ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 332ms
GET request: Using authorization token from session
 GET /api/composio/toolkits 200 in 914ms
 GET /api/composio/profiles 200 in 1175ms
 GET /api/health 200 in 148ms
 GET /api/composio/toolkits/twitter/details 200 in 431ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3426ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 356ms
 GET /api/health 200 in 160ms
 GET /api/health 200 in 167ms
 GET /api/edge-flags 200 in 213ms
 GET /dashboard 200 in 246ms
 GET /api/health 200 in 178ms
 GET /api/edge-flags 200 in 182ms
 GET /api/composio/toolkits 200 in 764ms
 GET /api/composio/toolkits/twitter/details 200 in 317ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3148ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 365ms
 GET /api/health 200 in 131ms
 GET /api/health 200 in 148ms
 GET /api/health 200 in 158ms
 GET /api/edge-flags 200 in 236ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 219ms
 GET /dashboard 200 in 291ms
 GET /api/health 200 in 189ms
 GET /api/edge-flags 200 in 184ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 214ms
 GET /api/composio/toolkits 200 in 894ms
 GET /api/composio/toolkits/twitter/details 200 in 329ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3675ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 372ms
 GET /api/edge-flags 200 in 175ms
 GET /api/health 200 in 187ms
 GET /api/health 200 in 152ms
 GET /api/health 200 in 138ms
 GET /api/health 200 in 115ms
 GET /api/health 200 in 119ms
 GET /api/health 200 in 130ms
 GET /api/health 200 in 150ms
 GET /api/health 200 in 160ms
 GET /api/edge-flags 200 in 262ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 347ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 372ms
 GET /api/composio/toolkits 200 in 1102ms
 GET /api/health 200 in 245ms
 GET /dashboard 200 in 257ms
 GET /api/health 200 in 177ms
 GET /api/edge-flags 200 in 178ms
 GET /api/composio/toolkits 200 in 642ms
 GET /api/composio/toolkits/twitter/details 200 in 470ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3493ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 501ms
 GET /api/health 200 in 163ms
 GET /api/health 200 in 158ms
 GET /api/health 200 in 176ms
 GET /api/health 200 in 158ms
 GET /api/edge-flags 200 in 229ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 214ms
 GET /api/health 200 in 189ms
 GET /api/health 200 in 190ms
 GET /api/health 200 in 157ms
 GET /api/health 200 in 163ms
 GET /api/health 200 in 180ms
 GET /api/health 200 in 159ms
 GET /api/health 200 in 183ms
 GET /api/health 200 in 166ms
 GET /api/health 200 in 172ms
 GET /api/edge-flags 200 in 282ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 313ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 805ms
 GET /dashboard 200 in 274ms
 GET /api/health 200 in 203ms
 GET /api/edge-flags 200 in 214ms
 GET /api/health 200 in 153ms
 GET /api/health 200 in 172ms
 GET /api/edge-flags 200 in 167ms
 GET /dashboard 200 in 223ms
 GET /api/health 200 in 169ms
 GET /api/edge-flags 200 in 168ms
 GET /api/composio/toolkits 200 in 958ms
 GET /api/composio/toolkits/twitter/details 200 in 413ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3591ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 421ms
 GET /api/edge-flags 200 in 176ms
 GET /api/health 200 in 172ms
 GET /api/edge-flags 200 in 171ms
 GET /api/edge-flags 200 in 174ms
 GET /dashboard 200 in 279ms
 GET /api/health 200 in 165ms
 GET /api/edge-flags 200 in 163ms
 GET /api/health 200 in 159ms
 GET /api/health 200 in 197ms
 GET /api/health 200 in 158ms
 GET /api/health 200 in 176ms
 GET /api/health 200 in 137ms
 GET /api/health 200 in 134ms
 GET /api/health 200 in 136ms
 GET /api/health 200 in 175ms
 GET /api/health 200 in 158ms
 GET /api/health 200 in 116ms
 GET /api/edge-flags 200 in 253ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 322ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 236ms
 GET /api/edge-flags 200 in 299ms
 GET /api/health 200 in 316ms
GET request: Using authorization token from session
 GET /api/composio/toolkits 200 in 995ms
 GET /api/composio/profiles 200 in 1233ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3397ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 326ms
 GET /api/health 200 in 164ms
 GET /api/edge-flags 200 in 177ms
 GET /api/health 200 in 184ms
 GET /api/health 200 in 213ms
 GET /api/health 200 in 261ms
 GET /api/health 200 in 183ms
 GET /api/health 200 in 246ms
 GET /api/edge-flags 200 in 170ms
 GET /api/health 200 in 157ms
 GET /api/health 200 in 172ms
 GET /api/health 200 in 244ms
 GET /api/health 200 in 315ms
 GET /api/edge-flags 200 in 292ms
 GET /api/health 200 in 422ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 496ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 1576ms
 GET /dashboard 200 in 317ms
 GET /api/health 200 in 194ms
 GET /api/edge-flags 200 in 178ms
 GET /api/health 200 in 167ms
 GET /api/edge-flags 200 in 183ms
 GET /api/edge-flags 200 in 167ms
 GET /api/composio/toolkits 200 in 875ms
 GET /api/composio/toolkits 200 in 503ms
 GET /api/health 200 in 172ms
 GET /api/composio/toolkits/twitter/details 200 in 346ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
 GET /api/health 200 in 189ms
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4119ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 356ms
 GET /api/edge-flags 200 in 187ms
 GET /api/edge-flags 200 in 168ms
 GET /api/health 200 in 173ms
 GET /api/health 200 in 181ms
 GET /api/health 200 in 164ms
 GET /api/health 200 in 172ms
 GET /api/edge-flags 200 in 170ms
 GET /dashboard 200 in 219ms
 GET /api/health 200 in 164ms
 GET /api/edge-flags 200 in 176ms
 GET /api/composio/toolkits 200 in 644ms
 GET /api/composio/toolkits/twitter/details 200 in 354ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4170ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 371ms
 GET /api/health 200 in 169ms
 GET /api/edge-flags 200 in 160ms
 GET /api/health 200 in 169ms
 GET /api/edge-flags 200 in 159ms
 GET /api/health 200 in 146ms
 GET /api/edge-flags 200 in 197ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 219ms
 GET /api/health 200 in 198ms
 GET /api/health 200 in 171ms
 GET /api/health 200 in 153ms
 GET /api/health 200 in 144ms
 GET /api/health 200 in 176ms
 GET /api/health 200 in 150ms
 GET /api/health 200 in 149ms
 GET /api/health 200 in 184ms
 GET /api/health 200 in 141ms
 GET /api/health 200 in 167ms
 GET /api/edge-flags 200 in 272ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 455ms
 GET /api/edge-flags 200 in 246ms
 GET /api/health 200 in 228ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 3199ms
 GET /api/composio/toolkits 200 in 2911ms
 GET /api/composio/toolkits 200 in 1660ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 490ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4588ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 334ms
 GET /api/health 200 in 273ms
 GET /api/edge-flags 200 in 173ms
 GET /api/health 200 in 187ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 3920ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 442ms
 GET /api/edge-flags 200 in 170ms
 GET /api/health 200 in 168ms
 GET /api/health 200 in 181ms
 GET /api/edge-flags 200 in 171ms
 GET /api/health 200 in 152ms
 GET /api/edge-flags 200 in 191ms
 GET /api/health 200 in 244ms
 GET /api/edge-flags 200 in 241ms
 GET /api/health 200 in 311ms
 GET /dashboard 200 in 230ms
 GET /api/health 200 in 172ms
 GET /api/edge-flags 200 in 172ms
 GET /api/composio/toolkits 200 in 724ms
 GET /api/composio/toolkits/twitter/details 200 in 363ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4515ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 362ms
 GET /dashboard 200 in 179ms
 GET /api/health 200 in 158ms
 GET /api/edge-flags 200 in 159ms
 GET /api/composio/toolkits 200 in 933ms
 GET /api/composio/toolkits/twitter/details 200 in 375ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
 GET /api/health 200 in 173ms
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4936ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 398ms
 GET /api/health 200 in 174ms
 GET /api/edge-flags 200 in 234ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 213ms
 GET /api/health 200 in 165ms
 GET /api/health 200 in 230ms
 GET /api/edge-flags 200 in 275ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 333ms
 GET /api/edge-flags 200 in 191ms
 GET /api/composio/toolkits 200 in 517ms
 GET /dashboard 200 in 276ms
 GET /api/health 200 in 196ms
 GET /api/edge-flags 200 in 190ms
 GET /api/composio/toolkits 200 in 807ms
 GET /api/composio/toolkits/twitter/details 200 in 401ms
 GET /api/health 200 in 189ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 4546ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 396ms
 GET /api/health 200 in 177ms
 GET /api/health 200 in 168ms
 GET /api/health 200 in 161ms
 GET /api/health 200 in 165ms
 GET /api/health 200 in 165ms
 GET /api/health 200 in 168ms
 GET /api/health 200 in 137ms
 GET /api/health 200 in 167ms
 GET /api/health 200 in 160ms
 GET /api/health 200 in 176ms
 GET /api/health 200 in 144ms
 GET /api/health 200 in 166ms
 GET /api/health 200 in 744ms
 GET /api/health 200 in 907ms
 GET /api/health 200 in 227ms
 GET /api/health 200 in 297ms
 GET /api/health 200 in 687ms
 GET /api/health 200 in 797ms
 GET /api/health 200 in 231ms
 GET /api/health 200 in 293ms
 GET /api/health 200 in 235ms
 GET /api/health 200 in 295ms
 GET /api/health 200 in 218ms
 GET /api/health 200 in 279ms
 GET /api/health 200 in 244ms
 GET /api/health 200 in 314ms
 GET /api/health 200 in 249ms
 GET /api/health 200 in 349ms
 GET /api/health 200 in 249ms
 GET /api/health 200 in 323ms
 GET /api/health 200 in 215ms
 GET /api/health 200 in 278ms
 GET /api/health 200 in 248ms
 GET /api/health 200 in 289ms
 GET /api/health 200 in 289ms
 GET /api/health 200 in 334ms
 GET /api/edge-flags 200 in 273ms
 GET /api/health 200 in 440ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 442ms
 GET /api/health 200 in 255ms
 GET /dashboard 200 in 311ms
 GET /api/feature-flags/custom_agents 200 in 2409ms
 GET /api/composio/toolkits 200 in 2110ms
Failed to fetch feature flag custom_agents from backend: This operation was aborted
 GET /api/health 200 in 1429ms
 GET /api/edge-flags 200 in 193ms
 GET /api/feature-flags/custom_agents 200 in 201ms
 GET /api/composio/toolkits 200 in 553ms
 GET /api/composio/toolkits/twitter/details 200 in 341ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 5648ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 409ms
 GET /api/health 200 in 167ms
 GET /api/health 200 in 168ms
 GET /api/edge-flags 200 in 171ms
 GET /dashboard 200 in 209ms
 GET /api/health 200 in 154ms
 GET /api/edge-flags 200 in 176ms
 GET /api/health 200 in 161ms
 GET /api/health 200 in 172ms
 GET /api/edge-flags 200 in 182ms
 GET /api/health 200 in 298ms
 GET /api/composio/toolkits 200 in 796ms
 GET /dashboard 200 in 244ms
 GET /api/health 200 in 189ms
 GET /api/edge-flags 200 in 200ms
 GET /api/composio/toolkits 200 in 759ms
 GET /api/composio/toolkits/twitter/details 200 in 530ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
 GET /api/health 200 in 143ms
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 6498ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 329ms
 GET /api/edge-flags 200 in 161ms
 GET /api/health 200 in 135ms
 GET /api/health 200 in 122ms
 GET /api/health 200 in 162ms
 GET /api/edge-flags 200 in 266ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 265ms
 GET /api/edge-flags 200 in 191ms
 GET /api/health 200 in 175ms
 GET /dashboard 200 in 227ms
 GET /api/health 200 in 175ms
 GET /api/edge-flags 200 in 169ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 183ms
 GET /api/composio/toolkits 200 in 616ms
 GET /api/composio/toolkits/twitter/details 200 in 690ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 5283ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 281ms
 GET /api/health 200 in 167ms
 GET /api/health 200 in 151ms
 GET /api/edge-flags 200 in 176ms
 GET /api/edge-flags 200 in 179ms
 GET /api/composio/toolkits/gmail/details 200 in 360ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'gmail', profile_name: 'Gmail Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Gmail Profile',
  toolkitSlug: 'gmail'
}
Returning response to frontend: {
  profileId: 'ef570924-b65b-478a-a531-d56bba234ca2',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/d8eac31e-c976-492c-8e23-e63a4703900a?user_id=5596066d-edf0-4f9b-ac9e-724cc5fc0cbc'
}
 POST /api/composio/profiles 200 in 3427ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 370ms
 GET /api/health 200 in 154ms
 GET /api/health 200 in 167ms
 GET /api/health 200 in 176ms
 GET /api/health 200 in 188ms
 GET /api/health 200 in 173ms
 GET /api/health 200 in 164ms
 GET /api/health 200 in 216ms
 GET /api/health 200 in 153ms
 GET /api/health 200 in 168ms
 GET /api/edge-flags 200 in 246ms
Failed to fetch feature flag custom_agents from backend: Backend returned 404: Not Found
 GET /api/feature-flags/custom_agents 304 in 338ms
 GET /api/triggers/workflows/agents/olari-agent-001/workflows 200 in 342ms
 GET /api/composio/toolkits 200 in 621ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 357ms
 GET /api/health 200 in 174ms
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Session status: {
  hasSession: true,
  hasAccessToken: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  tokenExpiry: **********,
  tokenExpiresIn: '2025-09-21T04:02:32.000Z'
}
POST request: Sending to backend with auth headers: {
  hasAuth: true,
  hasRefresh: true,
  userId: '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
  hasComposioKey: true
}
Sending to backend: {
  url: 'http://localhost:8003/api/composio/profiles',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '[REDACTED]',
    'X-Refresh-Token': 'ptcnkrqvnblh',
    'X-User-Id': '26ab50d8-f2dd-4b57-a885-f9dceb3353cc',
    'X-Composio-Api-Key': 'ak_hPlpPFHaetHA4ErO50-v'
  },
  body: { toolkit_slug: 'twitter', profile_name: 'Twitter Profile' },
  session: 'present',
  access_token: 'present'
}
Backend response received: {
  hasProfileId: true,
  hasRedirectUrl: true,
  profileName: 'Twitter Profile',
  toolkitSlug: 'twitter'
}
Returning response to frontend: {
  profileId: 'd72c2f7a-dc6c-4bd3-8e80-291bf803ceaf',
  hasAuthUrl: true,
  mcpUrl: 'https://apollo.composio.dev/v3/mcp/56505298-3c5b-4b73-a05a-95b9a6257947?user_id=f6126817-050c-4c23-9ee0-47af7a283624'
}
 POST /api/composio/profiles 200 in 5436ms
GET request: Using authorization token from session
 GET /api/composio/profiles 200 in 365ms
 GET /api/health 200 in 170ms
 GET /api/health 200 in 142ms
 GET /api/health 200 in 141ms
 GET /api/edge-flags 200 in 174ms
