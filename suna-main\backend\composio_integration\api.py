from fastapi import APIRouter, HTTPException, Depends, Query, Request
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from uuid import uuid4
from utils.auth_utils import get_current_user_id_from_jwt
from utils.logger import logger
from services.supabase import DBConnection
from datetime import datetime
import os

from .composio_service import (
    get_integration_service,
)
from .toolkit_service import ToolkitService, ToolsListResponse
from .composio_profile_service import ComposioProfileService, ComposioProfile

router = APIRouter(prefix="/composio", tags=["composio"])

db: Optional[DBConnection] = None

def initialize(database: DBConnection):
    global db
    db = database

class IntegrateToolkitRequest(BaseModel):
    toolkit_slug: str
    profile_name: Optional[str] = None
    display_name: Optional[str] = None
    mcp_server_name: Optional[str] = None
    save_as_profile: bool = True

class IntegrationStatusResponse(BaseModel):
    status: str
    toolkit: str
    auth_config_id: str
    connected_account_id: str
    mcp_server_id: str
    final_mcp_url: str
    profile_id: Optional[str] = None
    redirect_url: Optional[str] = None

class CreateProfileRequest(BaseModel):
    toolkit_slug: str
    profile_name: str
    display_name: Optional[str] = None
    mcp_server_name: Optional[str] = None
    is_default: bool = False
    initiation_fields: Optional[Dict[str, str]] = None

class ToolsListRequest(BaseModel):
    toolkit_slug: str
    limit: int = 50
    cursor: Optional[str] = None

class ProfileResponse(BaseModel):
    profile_id: str
    profile_name: str
    display_name: str
    toolkit_slug: str
    toolkit_name: str
    mcp_url: str
    redirect_url: Optional[str] = None
    is_connected: bool
    is_default: bool
    created_at: str
    connection_id: Optional[str] = None  # Add connection_id for OAuth polling

    @classmethod
    def from_composio_profile(cls, profile: ComposioProfile, connection_id: Optional[str] = None) -> "ProfileResponse":
        return cls(
            profile_id=profile.profile_id,
            profile_name=profile.profile_name,
            display_name=profile.display_name,
            toolkit_slug=profile.toolkit_slug,
            toolkit_name=profile.toolkit_name,
            mcp_url=profile.mcp_url,
            redirect_url=profile.redirect_url,
            is_connected=profile.is_connected,
            is_default=profile.is_default,
            created_at=profile.created_at.isoformat() if profile.created_at else datetime.now().isoformat(),
            connection_id=connection_id
        )


@router.get("/categories")
async def list_categories(
    user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        logger.info("Fetching Composio categories")
        
        toolkit_service = ToolkitService()
        categories = await toolkit_service.list_categories()
        
        return {
            "success": True,
            "categories": [cat.dict() for cat in categories],
            "total": len(categories)
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch categories: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to fetch categories: {str(e)}")


@router.get("/toolkits")
async def list_toolkits(
    limit: int = Query(100, le=500),
    cursor: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        logger.info(f"Fetching Composio toolkits with limit: {limit}, cursor: {cursor}, search: {search}, category: {category}")
        
        service = get_integration_service()
        
        if search:
            result = await service.search_toolkits(search, category=category, limit=limit, cursor=cursor)
        else:
            result = await service.list_available_toolkits(limit, cursor=cursor, category=category)
        
        return {
            "success": True,
            "toolkits": [toolkit.dict() for toolkit in result.get('items', [])],
            "total_items": result.get('total_items', 0),
            "total_pages": result.get('total_pages', 0),
            "current_page": result.get('current_page', 1),
            "next_cursor": result.get('next_cursor'),
            "has_more": result.get('next_cursor') is not None
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch toolkits: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to fetch toolkits: {str(e)}")


@router.get("/toolkits/{toolkit_slug}/details")
async def get_toolkit_details(
    toolkit_slug: str,
    user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        logger.info(f"Fetching detailed toolkit info for: {toolkit_slug}")
        
        toolkit_service = ToolkitService()
        detailed_toolkit = await toolkit_service.get_detailed_toolkit_info(toolkit_slug)
        
        if not detailed_toolkit:
            raise HTTPException(status_code=404, detail=f"Toolkit {toolkit_slug} not found")
        
        return {
            "success": True,
            "toolkit": detailed_toolkit.dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to fetch toolkit details for {toolkit_slug}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to fetch toolkit details: {str(e)}")


@router.post("/integrate", response_model=IntegrationStatusResponse)
async def integrate_toolkit(
    request: IntegrateToolkitRequest,
    req: Request,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> IntegrationStatusResponse:
    try:
        # Extract JWT token from request
        auth_header = req.headers.get('Authorization')
        jwt_token = None
        if auth_header and auth_header.startswith('Bearer '):
            jwt_token = auth_header.split(' ')[1]
        
        integration_user_id = str(uuid4())
        logger.info(f"Generated integration user_id: {integration_user_id} for account: {current_user_id}")
        
        service = get_integration_service(db_connection=db)
        result = await service.integrate_toolkit(
            toolkit_slug=request.toolkit_slug,
            account_id=current_user_id,
            user_id=integration_user_id,
            profile_name=request.profile_name,
            display_name=request.display_name,
            mcp_server_name=request.mcp_server_name,
            save_as_profile=request.save_as_profile,
            jwt_token=jwt_token
        )
        
        return IntegrationStatusResponse(
            status="integrated",
            toolkit=result.toolkit.name,
            auth_config_id=result.auth_config.id,
            connected_account_id=result.connected_account.id,
            mcp_server_id=result.mcp_server.id,
            final_mcp_url=result.final_mcp_url,
            profile_id=result.profile_id,
            redirect_url=result.connected_account.redirect_url
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Integration failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/profiles", response_model=ProfileResponse)
async def create_profile(
    request: CreateProfileRequest,
    req: Request,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> ProfileResponse:
    try:
        # Extract JWT token from request
        auth_header = req.headers.get('Authorization')
        jwt_token = None
        if auth_header and auth_header.startswith('Bearer '):
            jwt_token = auth_header.split(' ')[1]
        
        integration_user_id = str(uuid4())
        logger.info(f"Generated integration user_id: {integration_user_id} for account: {current_user_id}")
        
        service = get_integration_service(db_connection=db)
        result = await service.integrate_toolkit(
            toolkit_slug=request.toolkit_slug,
            account_id=current_user_id,
            user_id=integration_user_id,
            profile_name=request.profile_name,
            display_name=request.display_name,
            mcp_server_name=request.mcp_server_name,
            save_as_profile=True,
            initiation_fields=request.initiation_fields,
            jwt_token=jwt_token
        )
        
        logger.info(f"Integration result for {request.toolkit_slug}: redirect_url = {result.connected_account.redirect_url}")
        logger.info(f"Connected account ID: {result.connected_account.id}")
        
        profile_service = ComposioProfileService(db)
        profiles = await profile_service.get_profiles(current_user_id, request.toolkit_slug, jwt_token=jwt_token)

        created_profile = None
        for profile in profiles:
            if profile.profile_name == request.profile_name:
                created_profile = profile
                break
        
        if not created_profile:
            raise HTTPException(status_code=500, detail="Profile created but not found")
        
        logger.info(f"Returning profile response with redirect_url: {created_profile.redirect_url} and connection_id: {result.connected_account.id}")
        
        # Pass the actual connected_account ID for OAuth polling
        return ProfileResponse.from_composio_profile(created_profile, connection_id=result.connected_account.id)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connections/{connection_id}/status")
async def get_connection_status(
    connection_id: str,
    request: Request,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Check the status of a Composio connected account"""
    try:
        logger.info(f"Checking connection status for ID: {connection_id}")
        service = get_integration_service(db_connection=db)
        
        # Extract JWT token from request
        auth_header = request.headers.get('Authorization', '')
        jwt_token = auth_header.split(' ')[1] if auth_header.startswith('Bearer ') else None
        
        # First check if this is a profile_id and get the actual connected_account_id
        profile_service = ComposioProfileService(db)
        actual_connection_id = connection_id
        
        # Try to get profile config to extract the real connected_account_id
        try:
            profile_config = await profile_service.get_profile_config(connection_id)
            if profile_config and profile_config.get('connected_account_id'):
                actual_connection_id = profile_config['connected_account_id']
                logger.info(f"Resolved profile_id {connection_id} to connected_account_id {actual_connection_id}")
            else:
                logger.info(f"No connected_account_id in config, using {connection_id} as-is")
        except Exception as e:
            # Not a profile_id, use as-is
            logger.info(f"Could not get profile config for {connection_id}, using as-is: {e}")
            pass
        
        # Try to get the connected account status from Composio
        try:
            from composio_client import Composio
            from composio_integration.connected_account_service import ConnectedAccountService
            
            # Use the ConnectedAccountService which has the correct implementation
            account_service = ConnectedAccountService()
            connected_account = await account_service.get_connected_account(actual_connection_id)
            
            if not connected_account:
                # Account not found, might still be initializing
                raise Exception(f"Connected account {actual_connection_id} not found")
            
            # Update the profile if status is ACTIVE
            if connected_account.status == "ACTIVE":
                # Update the profile to mark it as connected using the actual_connection_id
                await profile_service.mark_profile_connected(actual_connection_id, current_user_id, jwt_token)
            
            return {
                "success": True,
                "connected": connected_account.status == "ACTIVE",
                "status": connected_account.status,
                "connection_id": connection_id
            }
        except Exception as e:
            logger.warning(f"Error checking connection status: {e}")
            
            # Fall back to checking if we have a profile with this connection
            profiles = await profile_service.get_profiles(current_user_id, jwt_token=jwt_token)
            
            # Check if any profile references this connection
            for profile in profiles:
                if connection_id in (profile.redirect_url or ''):
                    # Check if the connection became active
                    config = await profile_service.get_profile_config(profile.profile_id)
                    return {
                        "success": True,
                        "connected": config.get('oauth_completed', False),
                        "status": "ACTIVE" if config.get('oauth_completed', False) else "PENDING",
                        "connection_id": connection_id
                    }
            
            return {
                "success": True,
                "connected": False,
                "status": "PENDING",
                "connection_id": connection_id
            }
            
    except Exception as e:
        logger.error(f"Failed to get connection status: {e}")
        return {
            "success": False,
            "connected": False,
            "status": "ERROR",
            "error": str(e)
        }

@router.get("/profiles")
async def get_profiles(
    toolkit_slug: Optional[str] = Query(None),
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        profile_service = ComposioProfileService(db)
        profiles = await profile_service.get_profiles(current_user_id, toolkit_slug)
        
        profile_responses = [ProfileResponse.from_composio_profile(profile) for profile in profiles]
        
        return {
            "success": True,
            "profiles": profile_responses
        }
        
    except Exception as e:
        logger.error(f"Failed to get profiles: {e}", exc_info=True)
        return {
            "success": False,
            "profiles": [],
            "error": str(e)
        }


@router.get("/profiles/{profile_id}/mcp-config")
async def get_profile_mcp_config(
    profile_id: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        profile_service = ComposioProfileService(db)
        mcp_config = await profile_service.get_mcp_config_for_agent(profile_id)
        
        return {
            "success": True,
            "mcp_config": mcp_config,
            "profile_id": profile_id
        }
        
    except Exception as e:
        logger.error(f"Failed to get MCP config for profile {profile_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get MCP config: {str(e)}")


@router.get("/profiles/{profile_id}")
async def get_profile_info(
    profile_id: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        profile_service = ComposioProfileService(db)
        profile = await profile_service.get_profile(profile_id, current_user_id)
        
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        return {
            "success": True,
            "profile": {
                "profile_id": profile.profile_id,
                "profile_name": profile.profile_name,
                "toolkit_name": profile.toolkit_name,
                "toolkit_slug": profile.toolkit_slug,
                "created_at": profile.created_at.isoformat() if profile.created_at else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get profile info for {profile_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get profile info: {str(e)}")


@router.get("/integration/{connected_account_id}/status")
async def get_integration_status(
    connected_account_id: str,
    user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        service = get_integration_service()
        status = await service.get_integration_status(connected_account_id)
        return {"connected_account_id": connected_account_id, **status}
    except Exception as e:
        logger.error(f"Failed to get status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/profiles/{profile_id}/discover-tools")
async def discover_composio_tools(
    profile_id: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    try:
        profile_service = ComposioProfileService(db)
        config = await profile_service.get_profile_config(profile_id)
        
        if config.get('type') != 'composio':
            raise HTTPException(status_code=400, detail="Not a Composio profile")
        
        mcp_url = config.get('mcp_url')
        if not mcp_url:
            raise HTTPException(status_code=400, detail="Profile has no MCP URL")
        
        from mcp_module.mcp_service import mcp_service
        
        result = await mcp_service.discover_custom_tools(
            request_type="http",
            config={"url": mcp_url}
        )
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"Failed to discover tools: {result.message}")
        
        logger.info(f"Discovered {len(result.tools)} tools from Composio profile {profile_id}")
        
        return {
            "success": True,
            "profile_id": profile_id,
            "toolkit_name": config.get('toolkit_name', 'Unknown'),
            "tools": result.tools,
            "total_tools": len(result.tools)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to discover tools for profile {profile_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/discover-tools/{profile_id}")
async def discover_tools_post(
    profile_id: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    return await discover_composio_tools(profile_id, current_user_id)


@router.post("/tools/list")
async def list_toolkit_tools(
    request: ToolsListRequest,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
):
    try:
        logger.info(f"User {current_user_id} requesting tools for toolkit: {request.toolkit_slug}")
        
        toolkit_service = ToolkitService()
        tools_response = await toolkit_service.get_toolkit_tools(
            toolkit_slug=request.toolkit_slug,
            limit=request.limit,
            cursor=request.cursor
        )
        
        return {
            "success": True,
            "tools": [tool.dict() for tool in tools_response.items],
            "total_items": tools_response.total_items,
            "current_page": tools_response.current_page,
            "total_pages": tools_response.total_pages,
            "next_cursor": tools_response.next_cursor
        }
        
    except Exception as e:
        logger.error(f"Failed to list toolkit tools for {request.toolkit_slug}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get toolkit tools: {str(e)}")


@router.get("/health")
async def health_check() -> Dict[str, str]:
    try:
        from .client import ComposioClient
        ComposioClient.get_client()
        return {"status": "healthy"}
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=str(e))


@router.get("/callback")
async def oauth_callback(
    code: Optional[str] = Query(None),
    state: Optional[str] = Query(None),
    error: Optional[str] = Query(None),
    error_description: Optional[str] = Query(None)
) -> Dict[str, Any]:
    """Handle OAuth callback from Composio services"""
    try:
        if error:
            logger.error(f"OAuth callback error: {error} - {error_description}")
            # Return an HTML page that closes the window and notifies the parent
            html_content = f"""
            <html>
                <head>
                    <title>Authentication Failed</title>
                    <script>
                        window.opener.postMessage({{
                            type: 'composio-oauth-error',
                            error: '{error}',
                            description: '{error_description or ""}'
                        }}, '*');
                        setTimeout(() => window.close(), 2000);
                    </script>
                </head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h2>Authentication Failed</h2>
                    <p>{error_description or error}</p>
                    <p>This window will close automatically...</p>
                </body>
            </html>
            """
            from fastapi.responses import HTMLResponse
            return HTMLResponse(content=html_content)
        
        if not code:
            raise HTTPException(status_code=400, detail="Missing authorization code")
        
        logger.info(f"OAuth callback received with code: {code[:10]}... and state: {state}")
        
        # The actual OAuth token exchange is handled by Composio's backend
        # We just need to return a success page that notifies the parent window
        html_content = """
        <html>
            <head>
                <title>Authentication Successful</title>
                <script>
                    window.opener.postMessage({
                        type: 'composio-oauth-success',
                        code: '""" + code + """',
                        state: '""" + (state or '') + """'
                    }, '*');
                    setTimeout(() => window.close(), 2000);
                </script>
            </head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h2>Authentication Successful!</h2>
                <p>You can now close this window.</p>
                <p>Redirecting back to your application...</p>
            </body>
        </html>
        """
        
        from fastapi.responses import HTMLResponse
        return HTMLResponse(content=html_content)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth callback failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"OAuth callback failed: {str(e)}")


@router.post("/callback")
async def oauth_callback_post(
    code: Optional[str] = None,
    state: Optional[str] = None,
    error: Optional[str] = None,
    error_description: Optional[str] = None
) -> Dict[str, Any]:
    """Handle OAuth callback POST from Composio services"""
    return await oauth_callback(code, state, error, error_description)


@router.get("/health/circuit-breakers")
async def get_circuit_breakers_health(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Get comprehensive circuit breaker health information"""
    try:
        from utils.circuit_breaker import circuit_registry
        from .cdn_circuit_breaker import cdn_circuit_breaker
        
        # Get system-wide circuit breaker health
        system_health = circuit_registry.get_health_status()
        
        # Get CDN-specific health if available
        cdn_health = None
        if cdn_circuit_breaker.active:
            cdn_health = cdn_circuit_breaker.get_system_health()
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "circuit_breaker_system": system_health,
            "cdn_system": cdn_health,
            "summary": {
                "total_breakers": system_health["total_breakers"],
                "open_breakers": system_health["open_breakers"],
                "system_status": system_health["status"],
                "overall_health_score": system_health["health_score"]
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get circuit breaker health: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get circuit breaker health: {str(e)}")


@router.get("/health/circuit-breakers/{breaker_name}")
async def get_circuit_breaker_details(
    breaker_name: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Get detailed information about a specific circuit breaker"""
    try:
        from utils.circuit_breaker import circuit_registry
        
        breaker = circuit_registry.get(breaker_name)
        if not breaker:
            raise HTTPException(status_code=404, detail=f"Circuit breaker '{breaker_name}' not found")
        
        detailed_stats = breaker.get_stats()
        health_summary = breaker.get_health_summary()
        
        return {
            "success": True,
            "breaker_name": breaker_name,
            "health_summary": health_summary,
            "detailed_stats": detailed_stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get circuit breaker details for {breaker_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get circuit breaker details: {str(e)}")


@router.post("/health/circuit-breakers/{breaker_name}/reset")
async def reset_circuit_breaker(
    breaker_name: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Manually reset a circuit breaker"""
    try:
        from utils.circuit_breaker import circuit_registry
        
        breaker = circuit_registry.get(breaker_name)
        if not breaker:
            raise HTTPException(status_code=404, detail=f"Circuit breaker '{breaker_name}' not found")
        
        old_state = breaker.state.value
        await breaker.reset()
        
        logger.info(
            f"Circuit breaker '{breaker_name}' manually reset by user {current_user_id}",
            old_state=old_state,
            new_state=breaker.state.value
        )
        
        return {
            "success": True,
            "message": f"Circuit breaker '{breaker_name}' has been reset",
            "breaker_name": breaker_name,
            "old_state": old_state,
            "new_state": breaker.state.value,
            "reset_by": current_user_id,
            "reset_time": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset circuit breaker {breaker_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to reset circuit breaker: {str(e)}")


@router.get("/health/cdn")
async def get_cdn_health(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Get comprehensive CDN health information"""
    try:
        from .cdn_health_monitor import cdn_health_monitor
        from .cdn_circuit_breaker import cdn_circuit_breaker
        from .logo_service_manager import logo_service_manager
        
        # Get health report from CDN monitor
        cdn_monitor_health = cdn_health_monitor.get_health_report()
        
        # Get circuit breaker health if active
        circuit_breaker_health = None
        if cdn_circuit_breaker.active:
            circuit_breaker_health = cdn_circuit_breaker.get_system_health()
        
        # Get logo service status
        logo_service_status = logo_service_manager.get_status()
        
        # Calculate overall CDN system health
        total_providers = len(cdn_monitor_health.get("providers", {}))
        healthy_providers = cdn_monitor_health.get("healthy_providers", 0)
        health_percentage = (healthy_providers / total_providers * 100) if total_providers > 0 else 0
        
        # Determine overall status
        if health_percentage >= 80:
            overall_status = "healthy"
        elif health_percentage >= 50:
            overall_status = "degraded"
        else:
            overall_status = "critical"
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "overall_status": overall_status,
            "health_percentage": f"{health_percentage:.1f}%",
            "cdn_monitoring": cdn_monitor_health,
            "circuit_breaker_system": circuit_breaker_health,
            "logo_service": logo_service_status,
            "recommendations": _generate_cdn_recommendations(
                cdn_monitor_health, circuit_breaker_health
            )
        }
        
    except Exception as e:
        logger.error(f"Failed to get CDN health: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get CDN health: {str(e)}")


@router.post("/health/cdn/force-check")
async def force_cdn_health_check(
    provider: Optional[str] = Query(None, description="Specific CDN provider to check"),
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Force immediate CDN health check"""
    try:
        from .cdn_health_monitor import cdn_health_monitor, CDNProvider
        
        if provider:
            try:
                cdn_provider = CDNProvider(provider.lower())
                await cdn_health_monitor.force_health_check(cdn_provider)
                message = f"Forced health check for CDN provider: {provider}"
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Unknown CDN provider: {provider}")
        else:
            await cdn_health_monitor.force_health_check()
            message = "Forced health check for all CDN providers"
        
        # Get updated health report
        health_report = cdn_health_monitor.get_health_report()
        
        logger.info(f"CDN health check forced by user {current_user_id}", provider=provider)
        
        return {
            "success": True,
            "message": message,
            "forced_by": current_user_id,
            "check_time": datetime.now().isoformat(),
            "health_report": health_report
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to force CDN health check: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to force CDN health check: {str(e)}")


@router.get("/health/metrics")
async def get_health_metrics(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Get comprehensive health metrics for monitoring systems"""
    try:
        from utils.circuit_breaker import circuit_registry
        from utils.performance_monitor import performance_monitor
        from .cdn_circuit_breaker import cdn_circuit_breaker
        
        # Get circuit breaker metrics
        circuit_metrics = {}
        for name, breaker in circuit_registry.breakers.items():
            if name.startswith("cdn_"):
                circuit_metrics[name] = {
                    "state": breaker.state.value,
                    "total_calls": breaker.stats.total_calls,
                    "success_rate": breaker.stats.success_rate,
                    "failure_rate": breaker.stats.failure_rate,
                    "avg_response_time": breaker.stats.average_response_time,
                    "consecutive_failures": breaker.stats.consecutive_failures,
                    "circuit_open_calls": breaker.stats.circuit_open_calls
                }
        
        # Get performance metrics
        performance_stats = performance_monitor.get_stats() if hasattr(performance_monitor, 'get_stats') else {}
        
        # Get CDN system metrics
        cdn_metrics = {}
        if cdn_circuit_breaker.active:
            system_health = cdn_circuit_breaker.get_system_health()
            cdn_metrics = {
                "system_success_rate": system_health.get("system_success_rate"),
                "healthy_providers": system_health.get("healthy_providers"),
                "total_providers": system_health.get("total_providers"),
                "total_calls": system_health.get("total_calls"),
                "best_provider": system_health.get("best_provider")
            }
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "circuit_breakers": circuit_metrics,
            "performance": performance_stats,
            "cdn_system": cdn_metrics
        }
        
    except Exception as e:
        logger.error(f"Failed to get health metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get health metrics: {str(e)}")


def _generate_cdn_recommendations(
    cdn_health: Dict[str, Any], 
    circuit_health: Optional[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Generate recommendations based on CDN health status"""
    recommendations = []
    
    if not cdn_health:
        return recommendations
    
    # Check overall health
    healthy_providers = cdn_health.get("healthy_providers", 0)
    total_providers = cdn_health.get("total_providers", 1)
    health_percentage = (healthy_providers / total_providers) * 100
    
    if health_percentage < 50:
        recommendations.append({
            "type": "critical",
            "title": "Critical CDN Health",
            "description": f"Only {healthy_providers} out of {total_providers} CDN providers are healthy",
            "action": "Investigate CDN connectivity and consider enabling additional fallback providers",
            "priority": 1
        })
    elif health_percentage < 80:
        recommendations.append({
            "type": "warning",
            "title": "Degraded CDN Performance",
            "description": f"{healthy_providers} out of {total_providers} CDN providers are healthy",
            "action": "Monitor CDN performance and prepare for potential fallback activation",
            "priority": 2
        })
    
    # Check individual provider health
    providers = cdn_health.get("providers", {})
    for provider, health in providers.items():
        if not health.get("is_healthy", True):
            recommendations.append({
                "type": "warning",
                "title": f"CDN Provider {provider} Unhealthy",
                "description": f"Provider {provider} has consecutive failures: {health.get('consecutive_failures', 0)}",
                "action": f"Check {provider} status and connectivity",
                "priority": 3
            })
        
        response_time = health.get("response_time", 0)
        if response_time and response_time > 3.0:
            recommendations.append({
                "type": "info",
                "title": f"Slow Response from {provider}",
                "description": f"Provider {provider} response time: {response_time:.2f}s",
                "action": f"Consider adjusting timeout or priority for {provider}",
                "priority": 4
            })
    
    return recommendations


# Circuit Breaker Dashboard and Management Endpoints

@router.get("/dashboard/circuit-breaker")
async def get_circuit_breaker_dashboard(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Get comprehensive circuit breaker dashboard data"""
    try:
        from utils.circuit_breaker import circuit_registry
        from .cdn_circuit_breaker import cdn_circuit_breaker
        from .adaptive_cdn_monitor import adaptive_cdn_monitor
        from .cdn_alerting_system import cdn_alerting_system
        
        # Get system overview
        system_health = circuit_registry.get_health_status()
        
        # Get CDN-specific data
        cdn_data = None
        if cdn_circuit_breaker.active:
            cdn_data = cdn_circuit_breaker.get_system_health()
        
        # Get adaptive monitoring status
        adaptive_status = None
        if adaptive_cdn_monitor.active:
            adaptive_status = adaptive_cdn_monitor.get_adaptive_status()
        
        # Get alerting system status
        alerting_status = cdn_alerting_system.get_alerting_status()
        
        # Calculate dashboard metrics
        dashboard_metrics = _calculate_dashboard_metrics(system_health, cdn_data)
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "dashboard_metrics": dashboard_metrics,
            "system_health": system_health,
            "cdn_system": cdn_data,
            "adaptive_monitoring": adaptive_status,
            "alerting_system": alerting_status,
            "recommendations": _generate_dashboard_recommendations(
                system_health, cdn_data, adaptive_status
            )
        }
        
    except Exception as e:
        logger.error(f"Failed to get circuit breaker dashboard: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard data: {str(e)}")


@router.get("/dashboard/circuit-breaker/metrics")
async def get_circuit_breaker_metrics(
    time_range: str = Query("1h", description="Time range: 5m, 15m, 1h, 6h, 24h"),
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Get time-series metrics for circuit breaker dashboard"""
    try:
        from .cdn_alerting_system import cdn_alerting_system
        
        # Validate time range
        valid_ranges = ["5m", "15m", "1h", "6h", "24h"]
        if time_range not in valid_ranges:
            raise HTTPException(status_code=400, detail=f"Invalid time range. Must be one of: {valid_ranges}")
        
        # Convert time range to seconds
        range_to_seconds = {
            "5m": 300,
            "15m": 900,
            "1h": 3600,
            "6h": 21600,
            "24h": 86400
        }
        seconds = range_to_seconds[time_range]
        
        # Get metrics from alerting system
        metrics_summary = cdn_alerting_system.get_metrics_summary()
        
        # Format metrics for dashboard consumption
        dashboard_metrics = {}
        for metric_name, metric_data in metrics_summary.get('metrics', {}).items():
            if metric_name.startswith('cdn_'):
                # Get appropriate aggregation
                aggregations = metric_data.get('aggregations', {})
                time_window = f"{seconds}s"
                
                if time_window in aggregations:
                    dashboard_metrics[metric_name] = {
                        'type': metric_data['type'],
                        'stats': aggregations[time_window],
                        'total_points': metric_data['total_points']
                    }
        
        return {
            "success": True,
            "time_range": time_range,
            "metrics": dashboard_metrics,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get circuit breaker metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.post("/management/circuit-breaker/start-monitoring")
async def start_circuit_breaker_monitoring(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Start comprehensive CDN circuit breaker monitoring"""
    try:
        from .adaptive_cdn_monitor import adaptive_cdn_monitor
        from .cdn_alerting_system import cdn_alerting_system
        
        # Start systems in order
        if not cdn_alerting_system.active:
            await cdn_alerting_system.start()
        
        if not adaptive_cdn_monitor.active:
            await adaptive_cdn_monitor.start()
        
        logger.info(f"CDN monitoring started by user {current_user_id}")
        
        return {
            "success": True,
            "message": "CDN circuit breaker monitoring started",
            "started_by": current_user_id,
            "start_time": datetime.now().isoformat(),
            "components": {
                "adaptive_monitor": adaptive_cdn_monitor.active,
                "alerting_system": cdn_alerting_system.active,
                "circuit_manager": adaptive_cdn_monitor.circuit_manager.active
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start monitoring: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")


@router.post("/management/circuit-breaker/stop-monitoring")
async def stop_circuit_breaker_monitoring(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Stop comprehensive CDN circuit breaker monitoring"""
    try:
        from .adaptive_cdn_monitor import adaptive_cdn_monitor
        from .cdn_alerting_system import cdn_alerting_system
        
        # Stop systems in reverse order
        if adaptive_cdn_monitor.active:
            await adaptive_cdn_monitor.stop()
        
        if cdn_alerting_system.active:
            await cdn_alerting_system.stop()
        
        logger.info(f"CDN monitoring stopped by user {current_user_id}")
        
        return {
            "success": True,
            "message": "CDN circuit breaker monitoring stopped",
            "stopped_by": current_user_id,
            "stop_time": datetime.now().isoformat(),
            "components": {
                "adaptive_monitor": adaptive_cdn_monitor.active,
                "alerting_system": cdn_alerting_system.active,
                "circuit_manager": adaptive_cdn_monitor.circuit_manager.active
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to stop monitoring: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")


class AlertRuleRequest(BaseModel):
    name: str
    condition: str
    severity: str  # info, warning, error, critical
    channels: List[str]  # log, email, webhook, slack, discord
    cooldown_seconds: int = 300
    message_template: str = ""
    enabled: bool = True


@router.post("/management/alert-rules")
async def add_alert_rule(
    request: AlertRuleRequest,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Add custom alert rule"""
    try:
        from .cdn_alerting_system import cdn_alerting_system, AlertRule, MonitoringSeverity, AlertChannel
        
        # Validate severity
        try:
            severity = MonitoringSeverity(request.severity.lower())
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid severity: {request.severity}")
        
        # Validate channels
        channels = []
        for channel_str in request.channels:
            try:
                channels.append(AlertChannel(channel_str.lower()))
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid channel: {channel_str}")
        
        # Create alert rule
        alert_rule = AlertRule(
            name=request.name,
            condition=request.condition,
            severity=severity,
            channels=channels,
            cooldown_seconds=request.cooldown_seconds,
            message_template=request.message_template,
            enabled=request.enabled
        )
        
        # Add rule to alerting system
        cdn_alerting_system.add_alert_rule(alert_rule)
        
        logger.info(f"Alert rule '{request.name}' added by user {current_user_id}")
        
        return {
            "success": True,
            "message": f"Alert rule '{request.name}' added successfully",
            "rule": {
                "name": request.name,
                "severity": request.severity,
                "channels": request.channels,
                "enabled": request.enabled
            },
            "added_by": current_user_id,
            "added_time": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add alert rule: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to add alert rule: {str(e)}")


@router.delete("/management/alert-rules/{rule_name}")
async def remove_alert_rule(
    rule_name: str,
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """Remove alert rule"""
    try:
        from .cdn_alerting_system import cdn_alerting_system
        
        # Check if rule exists
        if rule_name not in cdn_alerting_system.alert_rules:
            raise HTTPException(status_code=404, detail=f"Alert rule '{rule_name}' not found")
        
        # Remove rule
        cdn_alerting_system.remove_alert_rule(rule_name)
        
        logger.info(f"Alert rule '{rule_name}' removed by user {current_user_id}")
        
        return {
            "success": True,
            "message": f"Alert rule '{rule_name}' removed successfully",
            "removed_by": current_user_id,
            "removed_time": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove alert rule: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to remove alert rule: {str(e)}")


@router.get("/management/alert-rules")
async def list_alert_rules(
    current_user_id: str = Depends(get_current_user_id_from_jwt)
) -> Dict[str, Any]:
    """List all configured alert rules"""
    try:
        from .cdn_alerting_system import cdn_alerting_system
        
        rules = {}
        for name, rule in cdn_alerting_system.alert_rules.items():
            rules[name] = {
                "name": rule.name,
                "condition": rule.condition,
                "severity": rule.severity.value,
                "channels": [ch.value for ch in rule.channels],
                "cooldown_seconds": rule.cooldown_seconds,
                "message_template": rule.message_template,
                "enabled": rule.enabled,
                "last_triggered": datetime.fromtimestamp(rule.last_triggered).isoformat()
                                 if rule.last_triggered else None
            }
        
        return {
            "success": True,
            "rules": rules,
            "total_rules": len(rules),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to list alert rules: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to list alert rules: {str(e)}")


def _calculate_dashboard_metrics(system_health: Dict[str, Any], cdn_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate key metrics for dashboard display"""
    metrics = {
        "total_circuit_breakers": system_health.get("total_breakers", 0),
        "open_circuit_breakers": system_health.get("open_breakers", 0),
        "system_health_score": system_health.get("health_score", "100%"),
        "system_status": system_health.get("status", "healthy")
    }
    
    if cdn_data:
        metrics.update({
            "cdn_system_success_rate": cdn_data.get("system_success_rate", "100%"),
            "healthy_cdn_providers": cdn_data.get("healthy_providers", 0),
            "total_cdn_providers": cdn_data.get("total_providers", 0),
            "best_cdn_provider": cdn_data.get("best_provider"),
            "total_cdn_calls": cdn_data.get("total_calls", 0)
        })
    
    return metrics


def _generate_dashboard_recommendations(
    system_health: Dict[str, Any],
    cdn_data: Optional[Dict[str, Any]],
    adaptive_status: Optional[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Generate recommendations for dashboard display"""
    recommendations = []
    
    # System health recommendations
    if system_health.get("open_breakers", 0) > 0:
        recommendations.append({
            "type": "warning",
            "title": "Circuit Breakers Open",
            "description": f"{system_health['open_breakers']} circuit breakers are currently open",
            "action": "Review circuit breaker status and consider manual intervention",
            "priority": 1
        })
    
    # CDN health recommendations
    if cdn_data:
        healthy_providers = cdn_data.get("healthy_providers", 0)
        total_providers = cdn_data.get("total_providers", 1)
        health_percentage = (healthy_providers / total_providers) * 100
        
        if health_percentage < 80:
            recommendations.append({
                "type": "error" if health_percentage < 50 else "warning",
                "title": "CDN Health Degraded",
                "description": f"Only {healthy_providers} out of {total_providers} CDN providers are healthy",
                "action": "Check CDN provider status and network connectivity",
                "priority": 2 if health_percentage < 50 else 3
            })
    
    # Adaptive monitoring recommendations
    if adaptive_status and not adaptive_status.get("active", False):
        recommendations.append({
            "type": "info",
            "title": "Adaptive Monitoring Disabled",
            "description": "Adaptive monitoring is not currently active",
            "action": "Consider enabling adaptive monitoring for better performance",
            "priority": 4
        })
    
    # Active alerts recommendations
    if adaptive_status and adaptive_status.get("alert_summary", {}).get("total_active", 0) > 0:
        total_active = adaptive_status["alert_summary"]["total_active"]
        recommendations.append({
            "type": "warning",
            "title": "Active Alerts",
            "description": f"There are {total_active} active alerts requiring attention",
            "action": "Review and address active alerts",
            "priority": 2
        })
    
    return sorted(recommendations, key=lambda x: x["priority"])
